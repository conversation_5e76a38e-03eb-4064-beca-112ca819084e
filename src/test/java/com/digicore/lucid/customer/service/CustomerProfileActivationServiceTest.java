/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service;

/*
 * <AUTHOR>
 * @createdOn Jun-07(Sat)-2025
 */

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

import com.digicore.lucid.common.lib.account.dto.CustomerAccountDTO;
import com.digicore.lucid.common.lib.account.service.AccountService;
import com.digicore.lucid.common.lib.activation.dto.CustomerAccountPreferenceDTO;
import com.digicore.lucid.common.lib.activation.dto.CustomerActivationDTO;
import com.digicore.lucid.common.lib.activation.dto.CustomerUserDetailDTO;
import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.authorization.dto.PermissionDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleCreationDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleDTO;
import com.digicore.lucid.common.lib.authorization.service.PermissionService;
import com.digicore.lucid.common.lib.authorization.service.RoleService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerBusinessDetailDTO;
import com.digicore.lucid.customer.data.modules.activation.service.CustomerProfileActivationService;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerProfile;
import com.digicore.lucid.customer.data.modules.profile.repository.CustomerProfileRepository;
import com.digicore.lucid.customer.data.modules.profile.repository.CustomerUserProfileRepository;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.enums.Status;
import java.util.Collections;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@ExtendWith(MockitoExtension.class)
class CustomerProfileActivationServiceTest {

  @Mock private CustomerProfileRepository customerProfileRepository;

  @Mock private ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;

  @Mock private MessagePropertyConfig messagePropertyConfig;

  @Mock private PermissionService<PermissionDTO> customerUserPermissionService;

  @Mock private RoleService<RoleDTO, RoleCreationDTO> customerUserRoleService;

  @Mock private AuthProfileService<UserAuthProfileDTO> customerUserAuthProfileService;

  @Mock private CustomerUserProfileRepository customerUserProfileRepository;

  @Mock private AccountService<CustomerAccountDTO> customerAccountService;

  @InjectMocks private CustomerProfileActivationService customerProfileActivationService;

  @Test
  void processActivation_shouldUpdateProfileStatusAndAddAccount_whenValidActivationDTOProvided() {
    CustomerActivationDTO activationDTO = new CustomerActivationDTO();
    activationDTO.setOrganizationId("orgId");
    activationDTO.setCustomerBusinessDetailDTO(new CustomerBusinessDetailDTO());
    activationDTO.getCustomerBusinessDetailDTO().setCbaOrganizationId("cbaOrgId");
    activationDTO.setCustomerUserDetailDTO(Collections.emptyList());

    CustomerAccountPreferenceDTO accountPreferenceDTO = new CustomerAccountPreferenceDTO();
    accountPreferenceDTO.setAccountNumber("**********");
    activationDTO.setCustomerAccountPreferenceDTO(accountPreferenceDTO);

    CustomerProfile profile = new CustomerProfile();
    profile.setOrganizationId("orgId");
    profile.setCustomerUserProfiles(Set.of());

    when(customerProfileRepository.findFirstByOrganizationIdAndIsDeletedFalseOrderByCreatedDateDesc(
            "orgId"))
        .thenReturn(Optional.of(profile));

    customerProfileActivationService.processActivation(activationDTO);

    verify(customerProfileRepository).save(profile);
    verify(customerAccountService).addAccount("orgId", "**********");
  }

  @Test
  void processActivation_shouldThrowException_whenProfileNotFound() {
    CustomerActivationDTO activationDTO = new CustomerActivationDTO();
    activationDTO.setOrganizationId("orgId");

    when(customerProfileRepository.findFirstByOrganizationIdAndIsDeletedFalseOrderByCreatedDateDesc(
            "orgId"))
        .thenReturn(Optional.empty());
    when(exceptionHandler.processCustomException(anyString(), eq(HttpStatus.BAD_REQUEST)))
        .thenThrow(new RuntimeException("Profile not found"));

    assertThrows(
        RuntimeException.class,
        () -> customerProfileActivationService.processActivation(activationDTO));
  }

  @Test
  void assignUserRoles_shouldAssignAllPermissionsToSingleUser_whenOnlyOneUserExists() {
    CustomerUserDetailDTO user = new CustomerUserDetailDTO();
    user.setEmail("<EMAIL>");
    user.setUsername("username");

    when(customerUserPermissionService.retrieveSystemPermissions())
        .thenReturn(Set.of(new PermissionDTO("permission1"), new PermissionDTO("permission2")));
    // Ensure roleExists returns false so editRole is called
    when(customerUserRoleService.roleExists("CUSTODIAN", "orgId")).thenReturn(true);

    customerProfileActivationService.updateRolePermissions(
        Collections.singletonList(user), "orgId");

    verify(customerUserRoleService).editRole(any(RoleCreationDTO.class));
  }

  @Test
  void assignUserRoles_shouldNotAssignRoles_whenNoUsersExist() {
    customerProfileActivationService.updateRolePermissions(Collections.emptyList(), "orgId");

    verifyNoInteractions(customerUserRoleService);
    verifyNoInteractions(customerUserAuthProfileService);
  }

  @Test
  void updateProfileStatus_shouldUpdateStatusAndCbaReference_whenValidProfileProvided() {
    CustomerProfile profile = new CustomerProfile();
    CustomerActivationDTO activationDTO = new CustomerActivationDTO();
    activationDTO.setCustomerBusinessDetailDTO(new CustomerBusinessDetailDTO());
    activationDTO.getCustomerBusinessDetailDTO().setCbaOrganizationId("cbaOrgId");

    customerProfileActivationService.updateProfileStatus(profile, activationDTO);

    assertEquals(Status.ACTIVE, profile.getStatus());
    assertEquals("cbaOrgId", profile.getCbaOrganizationId());
    verify(customerProfileRepository).save(profile);
  }
}
