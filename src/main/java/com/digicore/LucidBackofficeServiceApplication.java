package com.digicore;

import com.digicore.lucid.common.lib.properties.EnterprisePropertyConfig;
import com.digicore.lucid.common.lib.util.ClientUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;

@EnableFeignClients
@SpringBootApplication(
    exclude = {UserDetailsServiceAutoConfiguration.class, DataSourceAutoConfiguration.class})
public class LucidBackofficeServiceApplication {

  public static void main(String[] args) {
    ConfigurableApplicationContext context =
        SpringApplication.run(LucidBackofficeServiceApplication.class, args);

    Environment env = context.getBean(Environment.class);
    if (env.acceptsProfiles(Profiles.of("enterprise"))) {
      ClientUtil.enterprisePropertyConfig = context.getBean(EnterprisePropertyConfig.class);
    }
  }
}
