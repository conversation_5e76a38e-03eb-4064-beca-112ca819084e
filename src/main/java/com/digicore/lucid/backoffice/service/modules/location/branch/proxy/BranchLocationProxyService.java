/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.location.branch.proxy;

/*
 * <AUTHOR>
 * @createdOn 19/03/2025
 */
public interface BranchLocationProxyService {
  Object createBranchLocation(Object initialData, Object updateData, Object... files);

  Object editBranchLocation(Object initialData, Object updateData, Object... files);

  Object deleteBranchLocation(Object initialData, Object updateData, Object... files);
}
