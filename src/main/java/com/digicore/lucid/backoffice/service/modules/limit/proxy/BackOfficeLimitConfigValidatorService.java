/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.limit.proxy;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.LIMIT_VIOLATION;
import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;

import com.digicore.api.helper.response.ApiError;
import com.digicore.api.helper.response.ApiResponseJson;
import com.digicore.lucid.common.lib.client.AdminFeignClient;
import com.digicore.lucid.common.lib.limit.dto.LimitConfigDTO;
import com.digicore.lucid.common.lib.limit.service.LimitConfigService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-04(Tue)-2025
 */

@Service
@RequiredArgsConstructor
public class BackOfficeLimitConfigValidatorService {
  private final LimitConfigService<LimitConfigDTO> backOfficeLimitConfigService;
  private final BackOfficeLimitConfigProxyService backOfficeLimitConfigProxyService;
  private final AdminFeignClient adminFeignClient;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  public void updateLimitConfig(LimitConfigDTO limitConfigDTO) {
    ApiResponseJson<Object> response = adminFeignClient.validateLimitUpdate(limitConfigDTO);
    Object responseBody = response.getData();
    List<ApiError> limitViolationErrors =
        getObjectMapper().convertValue(responseBody, new TypeReference<>() {});
    if (!ClientUtil.nullOrEmpty(limitViolationErrors))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getLimitMessage(LIMIT_VIOLATION),
          HttpStatus.BAD_REQUEST,
          limitViolationErrors);
    LimitConfigDTO existingLimitConfig =
        backOfficeLimitConfigService.verifyLimitConfigExist(limitConfigDTO);
    backOfficeLimitConfigProxyService.updateLimitConfig(existingLimitConfig, limitConfigDTO);
  }
}
