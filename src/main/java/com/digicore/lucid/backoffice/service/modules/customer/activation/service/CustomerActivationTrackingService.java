/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.customer.activation.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;

import com.digicore.lucid.backoffice.data.modules.registration.dto.CustomerAccountOpeningDTO;
import com.digicore.lucid.backoffice.data.modules.registration.service.CustomerAccountOpeningService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.enums.Status;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/*
 * <AUTHOR>
 * @createdOn May-21(Wed)-2025
 */
@Service
@RequiredArgsConstructor
public class CustomerActivationTrackingService {
  private final CustomerAccountOpeningService<CustomerAccountOpeningDTO> accountOpeningService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  @Transactional
  public CustomerAccountOpeningDTO getActivationStatus(String organizationId) {
    return accountOpeningService
        .findByOrganizationId(organizationId)
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getActivationMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }

  @Transactional
  public List<CustomerAccountOpeningDTO> getFailedActivations() {
    return accountOpeningService.findAllByStatusIn(
        List.of(Status.FAILED, Status.COMPLETED_WITH_ERROR));
  }
}
