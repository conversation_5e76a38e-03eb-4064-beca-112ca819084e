/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.fee.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.fee.FeeSwaggerDocConstant.*;
import static com.digicore.registhentication.util.PageableUtil.*;
import static com.digicore.registhentication.util.PageableUtil.PAGE_SIZE_DEFAULT_VALUE;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.backoffice.service.modules.fee.dto.BankFeeConfigCreateRequest;
import com.digicore.lucid.backoffice.service.modules.fee.proxy.BackOfficeFeeConfigValidatorService;
import com.digicore.lucid.backoffice.service.modules.fee.service.BackOfficeFeeOperations;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> John
 * @createdOn Mar-12(Wed)-2025
 */

@RestController
@RequestMapping(API_V1 + FEE_API)
@RequiredArgsConstructor
@Tag(name = FEE_CONTROLLER_TITLE, description = FEE_CONTROLLER_DESCRIPTION)
public class BackOfficeFeeController {
  private final BackOfficeFeeOperations backOfficeFeeOperations;
  private final BackOfficeFeeConfigValidatorService backOfficeFeeConfigValidatorService;

  @PostMapping(CREATE_API)
  @PreAuthorize("hasAuthority('create-backoffice-fee')")
  @Operation(summary = FEE_CONTROLLER_CREATE_TITLE, description = FEE_CONTROLLER_CREATE_DESCRIPTION)
  public ResponseEntity<Object> createFeeConfig(
      @Valid @RequestBody BankFeeConfigCreateRequest bankFeeConfigDTO) {
    backOfficeFeeConfigValidatorService.createBackOfficeFeeConfig(bankFeeConfigDTO);
    return ControllerResponse.buildSuccessResponse("Request logged for approval");
  }

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-backoffice-fees')")
  @Operation(
      summary = FEE_CONTROLLER_VIEW_ALL_TITLE,
      description = FEE_CONTROLLER_VIEW_ALL_DESCRIPTION)
  public ResponseEntity<Object> viewAllFeeConfigs(
      @RequestParam(value = PAGE_NUMBER, defaultValue = PAGE_NUMBER_DEFAULT_VALUE, required = false)
          int pageNumber,
      @RequestParam(value = PAGE_SIZE, defaultValue = PAGE_SIZE_DEFAULT_VALUE, required = false)
          int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        backOfficeFeeOperations.viewAllBackOfficeFeeConfigs(pageNumber, pageSize),
        "Fee configurations retrieved successfully.");
  }

  @GetMapping(RETRIEVE_API)
  @PreAuthorize("hasAuthority('view-backoffice-fee-details')")
  @Operation(summary = FEE_CONTROLLER_VIEW_TITLE, description = FEE_CONTROLLER_VIEW_DESCRIPTION)
  public ResponseEntity<Object> viewFeeConfigDetails(@RequestParam String feeId) {
    return ControllerResponse.buildSuccessResponse(
        backOfficeFeeOperations.viewBackOfficeFeeConfigDetails(feeId),
        "Fee configuration details retrieved successfully.");
  }

  @GetMapping(FEE_TRANSACTION_TYPES_API)
  @PreAuthorize("hasAuthority('view-fee-transaction-types')")
  @Operation(
      summary = FEE_CONTROLLER_VIEW_TYPES_TITLE,
      description = FEE_CONTROLLER_VIEW_TYPES_DESCRIPTION)
  public ResponseEntity<Object> viewFeeConfigDetails() {
    return ControllerResponse.buildSuccessResponse(
        backOfficeFeeOperations.viewFeeTransactionTypes(),
        "Fee transaction types retrieved successfully.");
  }
}
