/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.faq.processor;

import com.digicore.lucid.backoffice.service.modules.faq.service.BackOfficeFaqOperations;
import com.digicore.lucid.common.lib.processor.annotation.RequestHandler;
import com.digicore.lucid.common.lib.processor.annotation.RequestType;
import com.digicore.lucid.common.lib.processor.constant.RequestHandlerType;
import lombok.RequiredArgsConstructor;

/*
 * <AUTHOR>
 * @createdOn Mar-14(Fri)-2025
 */

@RequestHandler(type = RequestHandlerType.PROCESS_MAKER_REQUESTS)
@RequiredArgsConstructor
public class BackOfficeFaqProcessor {
  private final BackOfficeFaqOperations backOfficeFaqOperations;

  @RequestType(name = "createFaq")
  public Object createFaq(Object approvalDecisionDTO) {
    return backOfficeFaqOperations.createFaq(null, approvalDecisionDTO);
  }

  @RequestType(name = "editFaq")
  public Object editFaq(Object approvalDecisionDTO) {
    return backOfficeFaqOperations.editFaq(null, approvalDecisionDTO);
  }
}
