/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.customer.profile.processor;

import com.digicore.lucid.backoffice.service.modules.customer.profile.service.CustomerProfileService;
import com.digicore.lucid.common.lib.approval.dto.ApprovalDecisionDTO;
import com.digicore.lucid.common.lib.processor.annotation.RequestHandler;
import com.digicore.lucid.common.lib.processor.annotation.RequestType;
import com.digicore.lucid.common.lib.processor.constant.RequestHandlerType;
import lombok.RequiredArgsConstructor;

/*
 * <AUTHOR> <PERSON>
 * @createdOn Mar-05(Wed)-2025
 */

@RequestHandler(type = RequestHandlerType.PROCESS_MAKER_REQUESTS)
@RequiredArgsConstructor
public class CustomerProfileProcessor {
  private final CustomerProfileService customerProfileService;

  @RequestType(name = "enableCustomer")
  public Object enableCustomer(ApprovalDecisionDTO approvalDecisionDTO) {
    return customerProfileService.enableCustomer(null, approvalDecisionDTO);
  }

  @RequestType(name = "disableCustomer")
  public Object disableCustomer(ApprovalDecisionDTO approvalDecisionDTO) {
    return customerProfileService.disableCustomer(null, approvalDecisionDTO);
  }
}
