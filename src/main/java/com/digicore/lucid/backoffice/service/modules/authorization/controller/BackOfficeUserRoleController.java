/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.authorization.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.role.RoleSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.role.RoleSwaggerDocConstant.ROLE_CONTROLLER_TITLE;
import static com.digicore.registhentication.util.PageableUtil.*;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.backoffice.service.modules.authorization.proxy.BackOfficeUserRoleValidatorService;
import com.digicore.lucid.backoffice.service.modules.authorization.service.UserRoleService;
import com.digicore.lucid.common.lib.authorization.dto.PermissionDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleCreationDTO;
import com.digicore.lucid.common.lib.authorization.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Jan-28(Tue)-2025
 */

@RestController
@RequestMapping(API_V1 + ROLE_API)
@RequiredArgsConstructor
@Tag(name = ROLE_CONTROLLER_TITLE, description = ROLE_CONTROLLER_DESCRIPTION)
@Validated
public class BackOfficeUserRoleController {
  private final BackOfficeUserRoleValidatorService backOfficeUserRoleValidatorService;
  private final UserRoleService userRoleService;
  private final PermissionService<PermissionDTO> adminUserPermissionService;

  @PostMapping(CREATE_API)
  @PreAuthorize("hasAuthority('create-backoffice-roles')")
  @Operation(
      summary = ROLE_CONTROLLER_CREATE_ROLE_TITLE,
      description = ROLE_CONTROLLER_CREATE_ROLE_DESCRIPTION)
  public ResponseEntity<Object> createRole(@Valid @RequestBody RoleCreationDTO roleDTO) {
    backOfficeUserRoleValidatorService.createRole(roleDTO);
    return ControllerResponse.buildSuccessResponse("Request logged for approval");
  }

  @DeleteMapping(DELETE_API)
  @PreAuthorize("hasAuthority('delete-backoffice-role')")
  @Operation(
      summary = ROLE_CONTROLLER_DELETE_ROLE_TITLE,
      description = ROLE_CONTROLLER_DELETE_ROLE_DESCRIPTION)
  public ResponseEntity<Object> deleteRole(@PathVariable String name) {
    backOfficeUserRoleValidatorService.deleteRole(name);
    return ControllerResponse.buildSuccessResponse();
  }

  @PatchMapping(EDIT_API)
  @PreAuthorize("hasAuthority('edit-backoffice-role')")
  @Operation(
      summary = ROLE_CONTROLLER_EDIT_ROLE_TITLE,
      description = ROLE_CONTROLLER_EDIT_ROLE_DESCRIPTION)
  public ResponseEntity<Object> editRole(@Valid @RequestBody RoleCreationDTO roleDTO) {
    backOfficeUserRoleValidatorService.editRole(roleDTO);
    return ControllerResponse.buildSuccessResponse("Request logged for approval");
  }

  @PatchMapping(DISABLE_API)
  @PreAuthorize("hasAuthority('disable-backoffice-role')")
  @Operation(
      summary = ROLE_CONTROLLER_DISABLE_ROLE_TITLE,
      description = ROLE_CONTROLLER_DISABLE_ROLE_DESCRIPTION)
  public ResponseEntity<Object> disableRole(@PathVariable String name) {
    backOfficeUserRoleValidatorService.disableRole(name);
    return ControllerResponse.buildSuccessResponse();
  }

  @PatchMapping(ENABLE_API)
  @PreAuthorize("hasAuthority('enable-backoffice-role')")
  @Operation(
      summary = ROLE_CONTROLLER_ENABLE_ROLE_TITLE,
      description = ROLE_CONTROLLER_ENABLE_ROLE_DESCRIPTION)
  public ResponseEntity<Object> enableRole(@PathVariable String name) {
    backOfficeUserRoleValidatorService.enableRole(name);
    return ControllerResponse.buildSuccessResponse();
  }

  @GetMapping(FETCH_ALL_API)
  @PreAuthorize("hasAuthority('view-customer-roles')")
  public ResponseEntity<Object> getAllRoles() {
    return ControllerResponse.buildSuccessResponse(userRoleService.getAllRoles());
  }

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-backoffice-roles')")
  @Operation(
      summary = ROLE_CONTROLLER_RETRIEVE_ALL_ROLES_TITLE,
      description = ROLE_CONTROLLER_RETRIEVE_ALL_ROLES_DESCRIPTION)
  public ResponseEntity<Object> getAllRoles(
      @RequestParam(value = PAGE_NUMBER, defaultValue = PAGE_NUMBER_DEFAULT_VALUE, required = false)
          int pageNumber,
      @RequestParam(value = PAGE_SIZE, defaultValue = PAGE_SIZE_DEFAULT_VALUE, required = false)
          int pageSize,
      @RequestParam(value = "paginated", defaultValue = "false", required = false)
          String paginated) {
    return ControllerResponse.buildSuccessResponse(
        userRoleService.getAllRoles(pageNumber, pageSize, paginated),
        "Request logged for approval");
  }

  @GetMapping(ROLE_RETRIEVE_ALL_SYSTEM_PERMISSIONS_API)
  @PreAuthorize("hasAuthority('view-backoffice-permissions')")
  @Operation(
      summary = ROLE_CONTROLLER_RETRIEVE_ALL_PERMISSIONS_TITLE,
      description = ROLE_CONTROLLER_RETRIEVE_ALL_PERMISSIONS_DESCRIPTION)
  public ResponseEntity<Object> getAllPermissions() {
    return ControllerResponse.buildSuccessResponse(
        adminUserPermissionService.retrieveSystemPermissions(), "Request logged for approval");
  }
}
