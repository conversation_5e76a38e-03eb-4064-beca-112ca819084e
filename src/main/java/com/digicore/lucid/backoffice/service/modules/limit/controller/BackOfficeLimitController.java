/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.limit.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.limit.LimitSwaggerDocConstant.*;
import static com.digicore.registhentication.util.PageableUtil.*;
import static com.digicore.registhentication.util.PageableUtil.PAGE_SIZE_DEFAULT_VALUE;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.backoffice.service.modules.limit.proxy.BackOfficeLimitConfigValidatorService;
import com.digicore.lucid.backoffice.service.modules.limit.service.BackOfficeLimitService;
import com.digicore.lucid.common.lib.limit.dto.LimitConfigDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.registhentication.validator.enums.Currency;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> John
 * @createdOn Feb-25(Tue)-2025
 */

@RestController
@RequestMapping(API_V1 + LIMIT_API)
@RequiredArgsConstructor
@Tag(name = LIMIT_CONTROLLER_TITLE, description = LIMIT_CONTROLLER_DESCRIPTION)
public class BackOfficeLimitController {
  private final BackOfficeLimitService backOfficeLimitService;
  private final BackOfficeLimitConfigValidatorService backOfficeLimitConfigValidatorService;

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-backoffice-limit')")
  @Operation(
      summary = LIMIT_CONTROLLER_RETRIEVE_ALL_TITLE,
      description = LIMIT_CONTROLLER_RETRIEVE_ALL_DESCRIPTION)
  public ResponseEntity<Object> retrieveLimitConfig(
      @RequestParam(value = PAGE_NUMBER, defaultValue = PAGE_NUMBER_DEFAULT_VALUE, required = false)
          int pageNumber,
      @RequestParam(value = PAGE_SIZE, defaultValue = PAGE_SIZE_DEFAULT_VALUE, required = false)
          int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        backOfficeLimitService.fetchBackOfficeLimitConfigs(pageNumber, pageSize),
        "Limit types fetched successfully.");
  }

  @GetMapping(RETRIEVE_API)
  @PreAuthorize("hasAuthority('view-backoffice-limit')")
  @Operation(
      summary = LIMIT_CONTROLLER_RETRIEVE_TITLE,
      description = LIMIT_CONTROLLER_RETRIEVE_DESCRIPTION)
  public ResponseEntity<Object> retrieveLimitConfig(
      @RequestParam boolean defaultLimit,
      @RequestParam LimitType limitType,
      @RequestParam Currency currency) {
    return ControllerResponse.buildSuccessResponse(
        backOfficeLimitService.retrieveLimitConfig(limitType, currency, defaultLimit),
        "Limit types fetched successfully.");
  }

  @PostMapping(EDIT_API)
  @PreAuthorize("hasAuthority('edit-backoffice-limit')")
  @Operation(
      summary = LIMIT_CONTROLLER_UPDATE_TITLE,
      description = LIMIT_CONTROLLER_UPDATE_DESCRIPTION)
  public ResponseEntity<Object> updateLimitConfig(@RequestBody LimitConfigDTO request) {
    backOfficeLimitConfigValidatorService.updateLimitConfig(request);
    return ControllerResponse.buildSuccessResponse(null, "Request logged for approval");
  }
}
