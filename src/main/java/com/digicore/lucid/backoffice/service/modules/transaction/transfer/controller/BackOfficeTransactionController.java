/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.transaction.transfer.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.transfer.TransferHistorySwaggerDocConstant.*;
import static com.digicore.registhentication.util.PageableUtil.*;
import static com.digicore.registhentication.util.PageableUtil.PAGE_SIZE_DEFAULT_VALUE;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.backoffice.service.modules.transaction.transfer.service.BackOfficeTransferOperations;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-16(Sun)-2025
 */

@RestController
@RequestMapping(API_V1 + TRANSFER_HISTORY_API)
@Tag(
    name = TRANSFER_HISTORY_CONTROLLER_TITLE,
    description = TRANSFER_HISTORY_CONTROLLER_DESCRIPTION)
@RequiredArgsConstructor
public class BackOfficeTransactionController {
  private final BackOfficeTransferOperations backOfficeTransferOperations;

  @GetMapping(RETRIEVE_ALL_API)
  ResponseEntity<Object> fetchTransfers(
      @RequestParam(value = PAGE_NUMBER, defaultValue = PAGE_NUMBER_DEFAULT_VALUE, required = false)
          int pageNumber,
      @RequestParam(value = PAGE_SIZE, defaultValue = PAGE_SIZE_DEFAULT_VALUE, required = false)
          int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        backOfficeTransferOperations.fetchTransfers(pageNumber, pageSize));
  }
}
