/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.customer.account.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.account.AccountSwaggerDocConstant.*;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.backoffice.service.modules.customer.account.service.CustomerAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/*
 * <AUTHOR> Ilori
 * @createdOn 05/03/2025
 */

@RestController
@RequestMapping(API_V1 + CUSTOMER_API + ACCOUNT_API)
@Tag(name = CUSTOMER_CONTROLLER_TITLE, description = CUSTOMER_CONTROLLER_DESCRIPTION)
@RequiredArgsConstructor
public class CustomerAccountController {
  private final CustomerAccountService customerAccountService;

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-customer-account')")
  @Operation(summary = ACCOUNT_RETRIEVE_TITLE, description = ACCOUNT_RETRIEVE_DESCRIPTION)
  ResponseEntity<Object> fetchAccounts(@RequestParam String organizationId) {
    return ControllerResponse.buildSuccessResponse(
        customerAccountService.fetchAccounts(organizationId));
  }
}
