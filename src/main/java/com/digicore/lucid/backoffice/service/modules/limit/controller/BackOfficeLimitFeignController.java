/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.limit.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.limit.LimitSwaggerDocConstant.*;
import static com.digicore.registhentication.util.PageableUtil.*;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.backoffice.service.modules.limit.service.BankLimitFeignService;
import com.digicore.lucid.common.lib.limit.dto.BankLimitConfigDTO;
import com.digicore.lucid.common.lib.limit.dto.LimitBucketDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.registhentication.validator.enums.Currency;
import io.swagger.v3.oas.annotations.Hidden;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-05(Wed)-2025
 */

@Hidden
@RestController
@RequestMapping(API_V1 + BACKOFFICE_API + LIMIT_API)
@RequiredArgsConstructor
public class BackOfficeLimitFeignController {
  private final BankLimitFeignService bankLimitFeignService;

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-bank-limit')")
  public ResponseEntity<Object> retrieveLimitConfig(
      @RequestParam(value = PAGE_NUMBER, defaultValue = PAGE_NUMBER_DEFAULT_VALUE, required = false)
          int pageNumber,
      @RequestParam(value = PAGE_SIZE, defaultValue = PAGE_SIZE_DEFAULT_VALUE, required = false)
          int pageSize,
      @RequestParam String organizationId) {
    return ControllerResponse.buildSuccessResponse(
        bankLimitFeignService.fetchBankLimitConfigs(organizationId, pageNumber, pageSize),
        "Limit types fetched successfully.");
  }

  @GetMapping(RETRIEVE_API)
  @PreAuthorize("hasAuthority('view-backoffice-limit')")
  public ResponseEntity<Object> retrieveLimitConfig(
      @RequestParam boolean defaultLimit,
      @RequestParam LimitType limitType,
      @RequestParam Currency currency,
      @RequestParam String organizationId) {
    return ControllerResponse.buildSuccessResponse(
        bankLimitFeignService.retrieveLimitConfig(
            organizationId, limitType, currency, defaultLimit),
        "Limit types fetched successfully.");
  }

  @PostMapping(FETCH_API)
  @PreAuthorize("hasAuthority('view-customer-limit')")
  public ResponseEntity<Object> retrieveLimitConfig(
      @RequestBody List<LimitBucketDTO> limitBucketDTOS, @RequestParam String organizationId) {
    return ControllerResponse.buildSuccessResponse(
        bankLimitFeignService.retrieveDefaultLimitConfig(organizationId, limitBucketDTOS),
        "Limit types fetched successfully.");
  }

  @PostMapping(EDIT_API)
  @PreAuthorize("hasAuthority('approve-edit-bank-limit')")
  public ResponseEntity<Object> updateBankLimit(@RequestBody BankLimitConfigDTO request) {
    return ControllerResponse.buildSuccessResponse(
        bankLimitFeignService.updateBankLimitConfig(request), "Request logged for approval");
  }

  @PostMapping(VALIDATE_API)
  @PreAuthorize("hasAuthority('edit-bank-limit')")
  public ResponseEntity<Object> validateBankLimit(
      @RequestBody BankLimitConfigDTO bankLimitConfigDTO) {
    return ControllerResponse.buildSuccessResponse(
        bankLimitFeignService.validateBankLimitConfig(bankLimitConfigDTO));
  }
}
