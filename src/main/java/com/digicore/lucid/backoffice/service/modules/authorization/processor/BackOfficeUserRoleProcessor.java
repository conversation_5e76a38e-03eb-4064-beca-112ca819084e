/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.authorization.processor;

import com.digicore.lucid.backoffice.service.modules.authorization.service.UserRoleService;
import com.digicore.lucid.common.lib.processor.annotation.RequestHandler;
import com.digicore.lucid.common.lib.processor.annotation.RequestType;
import com.digicore.lucid.common.lib.processor.constant.RequestHandlerType;
import lombok.RequiredArgsConstructor;

/*
 * <AUTHOR>
 * @createdOn Jan-28(Tue)-2025
 */

@RequestHandler(type = RequestHandlerType.PROCESS_MAKER_REQUESTS)
@RequiredArgsConstructor
public class BackOfficeUserRoleProcessor {
  private final UserRoleService userRoleService;

  @RequestType(name = "createRole")
  public Object createRole(Object approvalDecisionDTO) {
    return userRoleService.createRole(null, approvalDecisionDTO);
  }

  @RequestType(name = "deleteRole")
  public Object deleteRole(Object approvalDecisionDTO) {
    return userRoleService.deleteRole(null, approvalDecisionDTO);
  }

  @RequestType(name = "enableRole")
  public Object enableRole(Object approvalDecisionDTO) {
    return userRoleService.enableRole(null, approvalDecisionDTO);
  }

  @RequestType(name = "disableRole")
  public Object disableRole(Object approvalDecisionDTO) {
    return userRoleService.disableRole(null, approvalDecisionDTO);
  }

  @RequestType(name = "editRole")
  public Object editRole(Object approvalDecisionDTO) {
    return userRoleService.editRole(null, approvalDecisionDTO);
  }
}
