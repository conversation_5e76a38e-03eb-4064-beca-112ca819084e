/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.location.atm.service;

import com.digicore.lucid.backoffice.data.modules.location.atm.dto.ATMLocationDTO;
import com.digicore.lucid.backoffice.data.modules.location.service.DataAccessService;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn 20/03/2025
 */

@Service
@RequiredArgsConstructor
public class ATMLocationFeignService {
  private final DataAccessService<ATMLocationDTO> dataAccessService;

  public ATMLocationDTO retrieve(String bankOrganizationId, String terminalId) {
    setRequestContext(bankOrganizationId);
    return dataAccessService.retrieve(terminalId);
  }

  public PaginatedResponseDTO<ATMLocationDTO> retrieve(
      String bankOrganizationId, int pageNumber, int pageSize) {
    setRequestContext(bankOrganizationId);
    return dataAccessService.retrieve(pageNumber, pageSize);
  }

  private void setRequestContext(String bankOrganizationId) {
    RequestContextHolder.RequestContext requestContext = new RequestContextHolder.RequestContext();
    requestContext.setOrganizationId(bankOrganizationId);
    RequestContextHolder.set(requestContext);
  }
}
