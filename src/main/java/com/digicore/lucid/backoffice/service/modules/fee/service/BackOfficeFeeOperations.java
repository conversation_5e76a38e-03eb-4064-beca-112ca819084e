/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.fee.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.CREATE;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.FEE;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.BANK_FEE_CONFIG_DTO;

import com.digicore.lucid.backoffice.data.modules.fee.dto.BankFeeConfigDTO;
import com.digicore.lucid.backoffice.service.modules.fee.proxy.BackOfficeFeeConfigProxyService;
import com.digicore.lucid.common.lib.fee.service.FeeConfigService;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Mar-12(Wed)-2025
 */

@Service
@RequiredArgsConstructor
public class BackOfficeFeeOperations implements BackOfficeFeeConfigProxyService {
  private final FeeConfigService<BankFeeConfigDTO> bankFeeConfigService;

  @Override
  @MakerChecker(
      checkerPermission = "approve-create-backoffice-fee",
      makerPermission = "create-backoffice-fee",
      requestClassName = BANK_FEE_CONFIG_DTO,
      activity = CREATE,
      module = FEE)
  public Object createFeeConfig(Object initialData, Object updateData, Object... files) {
    BankFeeConfigDTO bankFeeConfigDTO = (BankFeeConfigDTO) updateData;
    bankFeeConfigService.createFeeConfig(bankFeeConfigDTO);
    return Optional.empty();
  }

  public List<String> viewFeeTransactionTypes() {
    return bankFeeConfigService.retrieveFeeTransactionTypes();
  }

  public BankFeeConfigDTO viewBackOfficeFeeConfigDetails(String feeId) {
    return bankFeeConfigService.retrieveFeeConfig(feeId);
  }

  public PaginatedResponseDTO<BankFeeConfigDTO> viewAllBackOfficeFeeConfigs(int page, int size) {
    return bankFeeConfigService.retrieveAllFeeConfigs(page, size);
  }
}
