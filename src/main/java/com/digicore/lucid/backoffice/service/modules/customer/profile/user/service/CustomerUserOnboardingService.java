/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.customer.profile.user.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.ONBOARD;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.USER;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.USER_REGISTRATION_DTO;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.USER_RESEND_INVITE_DTO;

import com.digicore.lucid.backoffice.service.modules.customer.profile.user.proxy.CustomerUserOnboardingProxyService;
import com.digicore.lucid.common.lib.client.CustomerFeignClient;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.lucid.common.lib.registration.dto.UserRegistrationDTO;
import com.digicore.lucid.common.lib.registration.dto.UserResendInviteDTO;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Mar-11(Tue)-2025
 */

@Service
@RequiredArgsConstructor
public class CustomerUserOnboardingService implements CustomerUserOnboardingProxyService {
  private final CustomerFeignClient customerFeignClient;

  @Override
  @MakerChecker(
      checkerPermission = "approve-invite-customer-user",
      makerPermission = "invite-customer-user",
      requestClassName = USER_REGISTRATION_DTO,
      activity = ONBOARD,
      module = USER)
  public Object inviteCustomerUser(Object initialData, Object updateData, Object... files) {
    UserRegistrationDTO userRegistrationDTO = (UserRegistrationDTO) updateData;
    customerFeignClient.inviteCustomerUser(userRegistrationDTO);
    return Optional.empty();
  }

  @Override
  @MakerChecker(
      checkerPermission = "approve-resend-customer-user-invite",
      makerPermission = "resend-customer-user-invite",
      requestClassName = USER_RESEND_INVITE_DTO,
      activity = ONBOARD,
      module = USER)
  public Object resendCustomerUserInvite(Object initialData, Object updateData, Object... files) {
    UserResendInviteDTO userResendInviteDTO = (UserResendInviteDTO) updateData;
    customerFeignClient.resendCustomerUserInvite(userResendInviteDTO);
    return Optional.empty();
  }
}
