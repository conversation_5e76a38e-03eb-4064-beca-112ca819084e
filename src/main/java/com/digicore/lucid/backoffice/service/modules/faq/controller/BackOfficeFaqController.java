/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.faq.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.faq.FaqSwaggerDocConstant.*;
import static com.digicore.registhentication.util.PageableUtil.*;
import static com.digicore.registhentication.util.PageableUtil.PAGE_SIZE_DEFAULT_VALUE;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.backoffice.service.modules.faq.dto.FaqCreateRequest;
import com.digicore.lucid.backoffice.service.modules.faq.proxy.BackOfficeFaqValidatorService;
import com.digicore.lucid.backoffice.service.modules.faq.service.BackOfficeFaqOperations;
import com.digicore.lucid.common.lib.faq.dto.BankFaqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> John
 * @createdOn Mar-14(Fri)-2025
 */

@RestController
@RequestMapping(API_V1 + FAQ_API)
@RequiredArgsConstructor
@Tag(name = FAQ_CONTROLLER_TITLE, description = FAQ_CONTROLLER_DESCRIPTION)
public class BackOfficeFaqController {
  private final BackOfficeFaqValidatorService backOfficeFaqValidatorService;
  private final BackOfficeFaqOperations backOfficeFaqOperations;

  @PostMapping(CREATE_API)
  @PreAuthorize("hasAuthority('create-backoffice-faq')")
  @Operation(summary = FAQ_CONTROLLER_CREATE_TITLE, description = FAQ_CONTROLLER_CREATE_DESCRIPTION)
  public ResponseEntity<Object> createBackOfficeFaq(
      @Valid @RequestBody FaqCreateRequest faqCreateRequest) {
    backOfficeFaqValidatorService.createBackOfficeFaq(faqCreateRequest);
    return ControllerResponse.buildSuccessResponse("Request logged for approval");
  }

  @PostMapping(EDIT_API)
  @PreAuthorize("hasAuthority('edit-backoffice-faq')")
  @Operation(summary = FAQ_CONTROLLER_EDIT_TITLE, description = FAQ_CONTROLLER_EDIT_DESCRIPTION)
  public ResponseEntity<Object> editBackOfficeFaq(@Valid @RequestBody BankFaqDTO request) {
    backOfficeFaqValidatorService.editBackOfficeFaq(request);
    return ControllerResponse.buildSuccessResponse("Request logged for approval");
  }

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-backoffice-faqs')")
  @Operation(
      summary = FAQ_CONTROLLER_VIEW_ALL_TITLE,
      description = FAQ_CONTROLLER_VIEW_ALL_DESCRIPTION)
  public ResponseEntity<Object> viewAllBackOfficeFaq(
      @RequestParam(value = PAGE_NUMBER, defaultValue = PAGE_NUMBER_DEFAULT_VALUE, required = false)
          int pageNumber,
      @RequestParam(value = PAGE_SIZE, defaultValue = PAGE_SIZE_DEFAULT_VALUE, required = false)
          int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        backOfficeFaqOperations.viewAllBackOfficeFaqs(pageNumber, pageSize),
        "Faqs retrieved successfully.");
  }

  @GetMapping(RETRIEVE_API)
  @PreAuthorize("hasAuthority('view-backoffice-faq-details')")
  @Operation(
      summary = FAQ_CONTROLLER_VIEW_DETAILS_TITLE,
      description = FAQ_CONTROLLER_VIEW_DETAILS_DESCRIPTION)
  public ResponseEntity<Object> viewBackOfficeFaqDetails(@RequestParam String faqId) {
    return ControllerResponse.buildSuccessResponse(
        backOfficeFaqOperations.viewBackOfficeFaqDetails(faqId),
        "Faq details retrieved successfully.");
  }
}
