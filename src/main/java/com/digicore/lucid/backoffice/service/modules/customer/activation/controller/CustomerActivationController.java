/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.customer.activation.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.activation.ActivationSwaggerDocConstant.*;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.backoffice.data.modules.registration.dto.CustomerAccountOpeningDTO;
import com.digicore.lucid.backoffice.service.modules.customer.activation.service.CustomerActivationService;
import com.digicore.lucid.backoffice.service.modules.customer.activation.service.CustomerActivationTrackingService;
import com.digicore.lucid.common.lib.activation.dto.BackOfficeCustomerActivationDTO;
import com.digicore.lucid.common.lib.activation.enums.CustomerActivationStatus;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-18(Tue)-2025
 */

@RestController
@RequestMapping(API_V1 + ACTIVATION_API)
@Tag(name = ACTIVATION_CONTROLLER_TITLE, description = ACTIVATION_CONTROLLER_DESCRIPTION)
@RequiredArgsConstructor
public class CustomerActivationController {
  private final CustomerActivationService customerActivationService;

  private final CustomerActivationTrackingService trackingService;

  @GetMapping(RETRIEVE_ACTIVATION_STATUS_API)
  @PreAuthorize("hasAuthority('view-customer-activation')")
  @Operation(
      summary = ACTIVATION_CONTROLLER_RETRIEVE_STATUS_TITLE,
      description = ACTIVATION_CONTROLLER_RETRIEVE_STATUS_DESCRIPTION)
  public ResponseEntity<CustomerAccountOpeningDTO> getActivationStatus(
      @RequestParam String organizationId) {
    return ResponseEntity.ok(trackingService.getActivationStatus(organizationId));
  }

  @GetMapping(FETCH_ALL_API)
  @Operation(
      summary = ACTIVATION_CONTROLLER_RETRIEVE_FAILED_TITLE,
      description = ACTIVATION_CONTROLLER_RETRIEVE_FAILED_DESCRIPTION)
  @PreAuthorize("hasAuthority('view-customer-activation')")
  public ResponseEntity<List<CustomerAccountOpeningDTO>> getFailedActivations() {
    return ResponseEntity.ok(trackingService.getFailedActivations());
  }

  @GetMapping(RETRIEVE_ACTIVATION_PROGRESS_API)
  @PreAuthorize("hasAuthority('view-customer-activation')")
  @Operation(
      summary = ACTIVATION_CONTROLLER_RETRIEVE_PROGRESS_TITLE,
      description = ACTIVATION_CONTROLLER_RETRIEVE_PROGRESS_DESCRIPTION)
  public ResponseEntity<Object> retrieveProgress(
      @RequestParam() CustomerActivationStatus customerActivationStatus,
      @RequestParam() int page,
      @RequestParam() int size) {
    return ControllerResponse.buildSuccessResponse(
        customerActivationService.retrieveProgress(customerActivationStatus, page, size));
  }

  @PostMapping(SAVE_PROGRESS_API)
  @PreAuthorize("hasAuthority('activate-customer')")
  @Operation(
      summary = ACTIVATION_CONTROLLER_SAVE_PROGRESS_TITLE,
      description = ACTIVATION_CONTROLLER_SAVE_PROGRESS_DESCRIPTION)
  public ResponseEntity<Object> saveProgress(
      @RequestBody BackOfficeCustomerActivationDTO approvalDecisionDTO) {
    customerActivationService.saveProgress(approvalDecisionDTO);
    return ControllerResponse.buildSuccessResponse();
  }

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-customer-activation')")
  @Operation(
      summary = ACTIVATION_CONTROLLER_RETRIEVE_ACCOUNT_OFFICERS_TITLE,
      description = ACTIVATION_CONTROLLER_RETRIEVE_ACCOUNT_OFFICERS_DESCRIPTION)
  public ResponseEntity<Object> retrieveAccountOfficers() {
    return ControllerResponse.buildSuccessResponse(
        customerActivationService.retrieveAccountOfficers());
  }

  @GetMapping(RETRIEVE_ACTIVIATION_COMMENT_API)
  @PreAuthorize("hasAuthority('view-customer-activation')")
  @Operation(
      summary = ACTIVATION_CONTROLLER_RETRIEVE_COMMENT_TITLE,
      description = ACTIVATION_CONTROLLER_RETRIEVE_COMMENT_DESCRIPTION)
  public ResponseEntity<Object> retrieveComment(
      @RequestHeader("organizationId") String organizationId) {
    return ControllerResponse.buildSuccessResponse(
        customerActivationService.retrieveComments(organizationId));
  }

  @GetMapping(DOWNLOAD_DOCUMENT_API)
  @PreAuthorize("hasAuthority('view-customer-activation')")
  @Operation(
      summary = ACTIVATION_CONTROLLER_DOWNLOAD_DOCUMENT_TITLE,
      description = ACTIVATION_CONTROLLER_DOWNLOAD_DOCUMENT_DESCRIPTION)
  public void downloadActivationDocument(
      @RequestParam String fileId,
      @RequestParam String organizationId,
      HttpServletResponse httpServletResponse) {
    customerActivationService.downloadActivationDocument(
        fileId, organizationId, httpServletResponse);
  }
}
