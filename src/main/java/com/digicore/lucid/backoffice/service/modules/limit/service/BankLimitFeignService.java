/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.limit.service;

import com.digicore.lucid.common.lib.limit.dto.BankLimitConfigDTO;
import com.digicore.lucid.common.lib.limit.dto.LimitBucketDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.limit.service.LimitConfigService;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.validator.enums.Currency;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Mar-05(Wed)-2025
 */

@Service
@RequiredArgsConstructor
public class BankLimitFeignService {
  private final LimitConfigService<BankLimitConfigDTO> bankLimitConfigService;

  public PaginatedResponseDTO<BankLimitConfigDTO> fetchBankLimitConfigs(
      String organizationId, int pageNumber, int pageSize) {
    return bankLimitConfigService.retrieveLimitConfig(organizationId, pageNumber, pageSize);
  }

  public BankLimitConfigDTO retrieveLimitConfig(
      String organizationId, LimitType limitType, Currency currency, boolean defaultLimit) {
    return bankLimitConfigService.retrieveLimitConfig(
        organizationId, limitType, currency, defaultLimit);
  }

  public List<BankLimitConfigDTO> retrieveDefaultLimitConfig(
      String organizationId, List<LimitBucketDTO> limitBucketDTOS) {
    return bankLimitConfigService.retrieveLimitConfig(limitBucketDTOS, organizationId);
  }

  public BankLimitConfigDTO validateBankLimitConfig(BankLimitConfigDTO bankLimitConfigDTO) {
    return bankLimitConfigService.verifyLimitConfigExist(bankLimitConfigDTO);
  }

  public Object updateBankLimitConfig(BankLimitConfigDTO bankLimitConfigDTO) {
    bankLimitConfigService.updateLimitConfig(bankLimitConfigDTO);
    return Optional.empty();
  }
}
