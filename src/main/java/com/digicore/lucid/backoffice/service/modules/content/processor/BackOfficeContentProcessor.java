/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.content.processor;

import com.digicore.lucid.backoffice.service.modules.content.service.BackOfficeContentOperations;
import com.digicore.lucid.common.lib.processor.annotation.RequestHandler;
import com.digicore.lucid.common.lib.processor.annotation.RequestType;
import com.digicore.lucid.common.lib.processor.constant.RequestHandlerType;
import lombok.RequiredArgsConstructor;

/*
 * <AUTHOR>
 * @createdOn Apr-01(Tue)-2025
 */

@RequestHandler(type = RequestHandlerType.PROCESS_MAKER_REQUESTS)
@RequiredArgsConstructor
public class BackOfficeContentProcessor {
  private final BackOfficeContentOperations backOfficeContentOperations;

  @RequestType(name = "createContent")
  public Object createContent(Object approvalDecisionDTO) {
    return backOfficeContentOperations.createContent(null, approvalDecisionDTO);
  }

  @RequestType(name = "editContent")
  public Object editContent(Object approvalDecisionDTO) {
    return backOfficeContentOperations.editContent(null, approvalDecisionDTO);
  }

  @RequestType(name = "deleteContent")
  public Object deleteContent(Object approvalDecisionDTO) {
    return backOfficeContentOperations.deleteContent(null, approvalDecisionDTO);
  }
}
