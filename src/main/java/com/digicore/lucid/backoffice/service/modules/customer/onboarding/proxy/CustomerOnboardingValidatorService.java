/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.customer.onboarding.proxy;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;

import com.digicore.lucid.common.lib.client.CustomerFeignClient;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerOnboardingDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonClient;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Mar-05(Wed)-2025
 */

@Service
@RequiredArgsConstructor
public class CustomerOnboardingValidatorService {
  private final CustomerFeignClient customerFeignClient;
  private final CustomerOnboardingProxyService customerOnboardingProxyService;
  private final MessagePropertyConfig messagePropertyConfig;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final RedissonClient redissonClient;

  public Object onboardCustomer(CustomerOnboardingDTO customerOnboardingDTO) {
    customerFeignClient.validateCustomerDetails(customerOnboardingDTO);
    return customerOnboardingProxyService.onboardCustomer(null, customerOnboardingDTO);
  }

  public Object addCustomerAccount(String accountNumber, String organizationId) {
    CustomerOnboardingDTO customerOnboardingDTO =
        (CustomerOnboardingDTO)
            redissonClient.getBucket("accountNumber:".concat(accountNumber)).get();

    if (customerOnboardingDTO == null) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    }
    customerOnboardingDTO.setOrganizationId(organizationId);
    customerOnboardingProxyService.addCustomerExistingAccount(null, customerOnboardingDTO);
    return customerOnboardingDTO;
  }
}
