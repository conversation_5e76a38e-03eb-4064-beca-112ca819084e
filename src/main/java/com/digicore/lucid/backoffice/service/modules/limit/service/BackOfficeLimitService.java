/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.service.modules.limit.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.EDIT;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.LIMIT;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.LIMIT_CONFIG_DTO;
import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;

import com.digicore.api.helper.response.ApiResponseJson;
import com.digicore.lucid.backoffice.service.modules.limit.proxy.BackOfficeLimitConfigProxyService;
import com.digicore.lucid.common.lib.client.AdminFeignClient;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.limit.dto.LimitConfigDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.limit.service.LimitConfigService;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.validator.enums.Currency;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Feb-25(Tue)-2025
 */

@Service
@RequiredArgsConstructor
public class BackOfficeLimitService implements BackOfficeLimitConfigProxyService {
  private final AdminFeignClient adminFeignClient;
  private final LimitConfigService<LimitConfigDTO> backOfficeLimitConfigService;

  public PaginatedResponseDTO<LimitConfigDTO> fetchBackOfficeLimitConfigs(int page, int size) {
    PaginatedResponseDTO<LimitConfigDTO> bankLimitConfigs =
        backOfficeLimitConfigService.retrieveLimitConfig(page, size);

    if (ClientUtil.nullOrEmpty(bankLimitConfigs.getContent())) {
      ApiResponseJson<Object> response = adminFeignClient.fetchSystemLimitConfig();
      Object responseBody = response.getData();
      List<LimitConfigDTO> systemLimitConfigDTO =
          getObjectMapper().convertValue(responseBody, new TypeReference<>() {});
      backOfficeLimitConfigService.createLimitConfig(systemLimitConfigDTO);
      bankLimitConfigs = backOfficeLimitConfigService.retrieveLimitConfig(page, size);
    }

    return bankLimitConfigs;
  }

  public LimitConfigDTO retrieveLimitConfig(
      LimitType limitType, Currency currency, boolean defaultLimit) {
    return backOfficeLimitConfigService.retrieveLimitConfig(
        RequestContextHolder.get().getOrganizationId(), limitType, currency, defaultLimit);
  }

  @MakerChecker(
      checkerPermission = "approve-edit-backoffice-limit",
      makerPermission = "edit-backoffice-limit",
      requestClassName = LIMIT_CONFIG_DTO,
      activity = EDIT,
      module = LIMIT)
  public Object updateLimitConfig(Object initialData, Object updateData, Object... files) {
    LimitConfigDTO limitConfigDTO = (LimitConfigDTO) updateData;
    backOfficeLimitConfigService.updateLimitConfig(limitConfigDTO);
    return Optional.empty();
  }
}
