/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.authentication.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.system.SystemConstant.*;

import com.digicore.lucid.backoffice.data.modules.authentication.model.BackOfficeUserAuthProfile;
import com.digicore.lucid.backoffice.data.modules.authentication.model.BackOfficeUserAuthProfilePasswordHistory;
import com.digicore.lucid.backoffice.data.modules.authentication.repository.BackOfficeUserAuthProfileRepository;
import com.digicore.lucid.backoffice.data.modules.authentication.repository.BackOfficeUserPasswordHistoryRepository;
import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.otp.enums.OtpType;
import com.digicore.lucid.common.lib.otp.service.OtpService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.registhentication.authentication.dtos.request.ResetPasswordSecondBaseRequestDTO;
import com.digicore.registhentication.authentication.dtos.request.UpdatePasswordRequestDTO;
import com.digicore.registhentication.authentication.services.PasswordResetService;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.enums.Status;
import jakarta.transaction.Transactional;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Jan-29(Wed)-2025
 */

@Service
@RequiredArgsConstructor
@Transactional
public class BackOfficeUserPasswordResetService implements PasswordResetService {
  private final AuthProfileService<UserAuthProfileDTO> backOfficeUserAuthProfileService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final OtpService otpService;
  private final MessagePropertyConfig messagePropertyConfig;

  private final PasswordEncoder passwordEncoder;
  private final BackOfficeUserPasswordHistoryRepository backOfficeUserPasswordHistoryRepository;
  private final BackOfficeUserAuthProfileRepository backOfficeUserAuthProfileRepository;

  @Override
  public void updateAccountPassword(ResetPasswordSecondBaseRequestDTO passwordFirstBaseRequestDTO) {
    otpService.effect(
        passwordFirstBaseRequestDTO.getEmail(),
        OtpType.PASSWORD_UPDATE,
        passwordFirstBaseRequestDTO.getOtp());
    updateAuthProfilePassword(
        passwordFirstBaseRequestDTO.getEmail(), passwordFirstBaseRequestDTO.getNewPassword());
  }

  @Override
  public void updateAccountPasswordWithoutVerification(String email, String plainPassword) {
    BackOfficeUserAuthProfile backOfficeAuthProfile =
        backOfficeUserAuthProfileService.retrieveAuthProfileEntity(email);
    passwordIntegrityChecks(plainPassword, backOfficeAuthProfile);
    updateAuthProfilePassword(email, plainPassword);
  }

  @Override
  public void updateAccountPassword(UpdatePasswordRequestDTO updatePasswordRequestDTO) {
    BackOfficeUserAuthProfile backOfficeAuthProfile =
        backOfficeUserAuthProfileService.retrieveAuthProfileEntity(
            ClientUtil.getLoggedInUsername());
    if (passwordEncoder.matches(
        updatePasswordRequestDTO.getOldPassword(), backOfficeAuthProfile.getPassword())) {
      passwordIntegrityChecks(updatePasswordRequestDTO.getNewPassword(), backOfficeAuthProfile);
      updateAuthProfilePassword(
          ClientUtil.getLoggedInUsername(), updatePasswordRequestDTO.getNewPassword());
      return;
    }

    exceptionHandler.processCustomExceptions(
        messagePropertyConfig.getLoginMessage(OLD_PASSWORD_WRONG), HttpStatus.BAD_REQUEST);
  }

  private void passwordIntegrityChecks(
      String plainPassword, BackOfficeUserAuthProfile backOfficeAuthProfile) {
    List<BackOfficeUserAuthProfilePasswordHistory> passwordHistories =
        new ArrayList<>(backOfficeAuthProfile.getBackOfficeUserPasswordHistories());

    if (passwordEncoder.matches(plainPassword, backOfficeAuthProfile.getPassword())
        || passwordHistories.stream()
            .anyMatch(s -> passwordEncoder.matches(plainPassword, s.getOldPassword())))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getLoginMessage(PASSWORD_ALREADY_USED), HttpStatus.BAD_REQUEST);

    if (backOfficeAuthProfile.getBackOfficeUserPasswordHistories().size() > 5) {
      long id = passwordHistories.getFirst().getId();
      backOfficeAuthProfile
          .getBackOfficeUserPasswordHistories()
          .remove(passwordHistories.getFirst());
      passwordHistories.getFirst().setBackOfficeUserAuthProfile(null);
      passwordHistories.removeFirst();
      backOfficeUserAuthProfileRepository.save(backOfficeAuthProfile);
      backOfficeUserPasswordHistoryRepository.deleteById(id);
    }

    String encodePassword = passwordEncoder.encode(plainPassword);
    BackOfficeUserAuthProfilePasswordHistory passwordHistory =
        new BackOfficeUserAuthProfilePasswordHistory();
    passwordHistory.setOldPassword(encodePassword);
    passwordHistory.setBackOfficeUserAuthProfile(backOfficeAuthProfile);
    passwordHistories.add(passwordHistory);
    backOfficeUserPasswordHistoryRepository.save(passwordHistory);

    backOfficeAuthProfile.setBackOfficeUserPasswordHistories(new HashSet<>(passwordHistories));
    backOfficeAuthProfile.setPassword(encodePassword);
  }

  private void updateAuthProfilePassword(String email, String plainPassword) {
    if (ADMIN_CHECKER_EMAIL.equalsIgnoreCase(email) || ADMIN_MAKER_EMAIL.equalsIgnoreCase(email)) {
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getLoginMessage(SYSTEM_DEFAULT_PASSWORD_CANT_BE_UPDATED),
          HttpStatus.BAD_REQUEST);
    }
    UserAuthProfileDTO adminUserAuthProfile =
        backOfficeUserAuthProfileService.retrieveAuthProfile(email, true);
    adminUserAuthProfile.setDefaultPassword(false);
    adminUserAuthProfile.setPassword(plainPassword);
    if (Status.PENDING_INVITE_ACCEPTANCE.equals(adminUserAuthProfile.getStatus())) {
      adminUserAuthProfile.setStatus(Status.ACTIVE);
      backOfficeUserAuthProfileService.updateAuthProfilePassword(adminUserAuthProfile);
      return;
    }
    if (Status.ACTIVE.equals(adminUserAuthProfile.getStatus())) {
      backOfficeUserAuthProfileService.updateAuthProfilePassword(adminUserAuthProfile);
      return;
    }
    exceptionHandler.processCustomExceptions(
        messagePropertyConfig.getLoginMessage(INACTIVE), HttpStatus.BAD_REQUEST);
  }
}
