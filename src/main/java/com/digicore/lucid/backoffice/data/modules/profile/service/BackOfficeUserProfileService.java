/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.profile.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;
import static com.digicore.lucid.common.lib.util.ClientUtil.getPageable;

import com.digicore.lucid.backoffice.data.modules.profile.model.BackOfficeUserProfile;
import com.digicore.lucid.backoffice.data.modules.profile.repository.BackOfficeUserProfileRepository;
import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.authorization.dto.PermissionDTO;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.profile.dto.LucidSearchRequest;
import com.digicore.lucid.common.lib.profile.dto.UserEditDTO;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.profile.service.UserProfileService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import jakarta.transaction.Transactional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Jan-22(Wed)-2025
 */

@Service
@RequiredArgsConstructor
@Transactional
public class BackOfficeUserProfileService implements UserProfileService<UserProfileDTO> {
  private final BackOfficeUserProfileRepository backOfficeUserProfileRepository;
  private final AuthProfileService<UserAuthProfileDTO> backOfficeUserAuthProfileService;
  private final AuthProfileService<UserAuthProfileDTO> backOfficeAuthProfileService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  @Override
  public boolean profileExists(String email) {
    return backOfficeUserProfileRepository.existsByEmail(email);
  }

  @Override
  public void editUserProfile(UserEditDTO userEditDTO) {
    BackOfficeUserProfile backOfficeUserProfileToUpdate =
        this.retrieveProfile(userEditDTO.getEmail());
    BeanUtilWrapper.copyNonNullProperties(userEditDTO, backOfficeUserProfileToUpdate);
    backOfficeUserProfileRepository.save(backOfficeUserProfileToUpdate);
    UserAuthProfileDTO userAuthProfileDTO = new UserAuthProfileDTO();
    userAuthProfileDTO.setUsername(userEditDTO.getEmail());
    if (userEditDTO.getPermissions() != null)
      userAuthProfileDTO.setPermissions(
          userEditDTO.getPermissions().stream()
              .map(
                  x -> {
                    PermissionDTO permissionDTO = new PermissionDTO();
                    permissionDTO.setName(x);
                    return permissionDTO;
                  })
              .collect(Collectors.toSet()));
    userAuthProfileDTO.setPassword(null);
    userAuthProfileDTO.setAssignedRole(userEditDTO.getAssignedRole());
    userAuthProfileDTO.setSystemInitiated(userEditDTO.isSystemInitiated());
    backOfficeAuthProfileService.updateAuthProfile(userAuthProfileDTO);
  }

  @Override
  public UserProfileDTO retrieveLoggedInUserProfile() {
    return getUserProfileDTO(retrieveProfile(ClientUtil.getLoggedInUsername()));
  }

  @Override
  public PaginatedResponseDTO<UserProfileDTO> retrieveAllUserProfiles(
      int pageNumber, int pageSize) {
    Page<BackOfficeUserProfile> backOfficeUserProfiles =
        backOfficeUserProfileRepository.findAllByIsDeletedFalseAndBankProfileOrganizationId(
            RequestContextHolder.get().getOrganizationId(), getPageable(pageNumber, pageSize));
    return PaginatedResponseDTO.<UserProfileDTO>builder()
        .content(backOfficeUserProfiles.stream().map(this::getUserProfileDTO).toList())
        .totalPages(backOfficeUserProfiles.getTotalPages())
        .totalItems(backOfficeUserProfiles.getTotalElements())
        .currentPage(backOfficeUserProfiles.getNumber() + 1)
        .isFirstPage(backOfficeUserProfiles.isFirst())
        .isLastPage(backOfficeUserProfiles.isLast())
        .build();
  }

  @Override
  public PaginatedResponseDTO<UserProfileDTO> filterOrSearch(
      LucidSearchRequest lucidSearchRequest) {
    return null;
  }

  // @Override
  public PaginatedResponseDTO<UserProfileDTO> filterOrSearch(
      LucidSearchRequest lucidSearchRequest, String organization) {
    return null;
  }

  @Override
  public void deleteUserProfile(String email) {}

  @Override
  public UserProfileDTO retrieveUserProfile(String username) {
    return getUserProfileDTO(retrieveProfile(username));
  }

  @Override
  public void enableUserProfile(String email) {}

  @Override
  public void disableUserProfile(String email) {}

  @Override
  public void profileExistenceCheckByEmail(String email) {}

  private BackOfficeUserProfile retrieveProfile(String email) {
    return backOfficeUserProfileRepository
        .findFirstByEmailAndIsDeletedFalseAndBankProfileOrganizationIdOrderByCreatedDate(
            email, RequestContextHolder.get().getOrganizationId())
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getUserMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }

  private UserProfileDTO getUserProfileDTO(BackOfficeUserProfile backOfficeUserProfile) {
    UserProfileDTO profileDto = new UserProfileDTO();
    UserAuthProfileDTO userAuthProfileDTO =
        backOfficeUserAuthProfileService.retrieveAuthProfile(
            backOfficeUserProfile.getEmail(), false);
    BeanUtilWrapper.copyNonNullProperties(backOfficeUserProfile, profileDto);
    profileDto.setUsername(backOfficeUserProfile.getEmail());
    profileDto.setAssignedRole(userAuthProfileDTO.getAssignedRole());
    profileDto.setStatus(userAuthProfileDTO.getStatus());
    profileDto.setPassword(null);
    profileDto.setUserPermissions(userAuthProfileDTO.getPermissions());
    profileDto.setPin(null);
    profileDto.setPhoneNumber(
        ClientUtil.nullOrEmpty(backOfficeUserProfile.getPhoneNumber())
            ? "N/S"
            : backOfficeUserProfile.getPhoneNumber());

    return profileDto;
  }
}
