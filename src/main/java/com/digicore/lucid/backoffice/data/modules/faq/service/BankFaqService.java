/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.faq.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.DUPLICATE;
import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;
import static com.digicore.lucid.common.lib.util.ClientUtil.getPageable;
import static com.digicore.registhentication.util.PageableUtil.SORT_BY_CREATED_DATE;

import com.digicore.lucid.backoffice.data.modules.faq.model.BankFaq;
import com.digicore.lucid.backoffice.data.modules.faq.repository.BankFaqRepository;
import com.digicore.lucid.backoffice.data.modules.profile.model.BankProfile;
import com.digicore.lucid.backoffice.data.modules.profile.repository.BankProfileRepository;
import com.digicore.lucid.common.lib.faq.dto.BankFaqDTO;
import com.digicore.lucid.common.lib.faq.enums.FaqStatus;
import com.digicore.lucid.common.lib.faq.service.FaqService;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Mar-14(Fri)-2025
 */

@Service
@RequiredArgsConstructor
public class BankFaqService implements FaqService<BankFaqDTO> {
  private final BankFaqRepository bankFaqRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final BankProfileRepository bankProfileRepository;

  @Override
  public void createFaq(BankFaqDTO faqDTO) {
    BankProfile bankProfile =
        bankProfileRepository
            .findFirstByOrganizationIdAndIsDeletedFalseOrderByCreatedDateDesc(
                RequestContextHolder.get().getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getClientMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));

    BankFaq bankFaq = new BankFaq();
    BeanUtilWrapper.copyNonNullProperties(faqDTO, bankFaq);
    bankFaq.setBankProfile(bankProfile);
    bankFaq.setStatus(FaqStatus.ACTIVE);
    bankFaqRepository.save(bankFaq);
  }

  @Override
  public BankFaqDTO retrieveFaq(String organizationId, String feeId) {
    BankFaq bankFaq =
        bankFaqRepository
            .findByBankProfileOrganizationIdAndFaqIdAndStatusAndIsDeletedFalse(
                organizationId, feeId, FaqStatus.ACTIVE)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getFaqMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));

    return mapBankFaqEntityToDTO(bankFaq);
  }

  @Override
  public void editFaq(BankFaqDTO faqDTO) {
    BankFaq bankFaq = retrieveBankFaqEntity(faqDTO.getFaqId());
    BeanUtilWrapper.copyNonNullProperties(faqDTO, bankFaq);
    bankFaqRepository.save(bankFaq);
  }

  @Override
  public void validateFaq(BankFaqDTO faq, boolean validationForCreation) {
    Optional<BankFaq> optionalBankFaq =
        bankFaqRepository
            .findByBankProfileOrganizationIdAndQuestionContainsIgnoreCaseAndCategoryContainsIgnoreCaseAndIsDeletedFalse(
                RequestContextHolder.get().getOrganizationId(),
                faq.getQuestion(),
                faq.getCategory());
    if (validationForCreation) {
      if (optionalBankFaq.isPresent()) {
        exceptionHandler.processCustomException(
            messagePropertyConfig.getFaqMessage(DUPLICATE), HttpStatus.BAD_REQUEST);
      }
    }

    if (!validationForCreation) {
      if (optionalBankFaq.isPresent() && !optionalBankFaq.get().getFaqId().equals(faq.getFaqId())) {
        exceptionHandler.processCustomException(
            messagePropertyConfig.getFaqMessage(DUPLICATE), HttpStatus.BAD_REQUEST);
      }
    }
  }

  @Override
  public PaginatedResponseDTO<BankFaqDTO> retrieveAllFaqs(
      String organizationId, int pageNumber, int pageSize) {
    Pageable pageable = getPageable(pageNumber, pageSize, SORT_BY_CREATED_DATE);
    Page<BankFaq> bankFaqs =
        bankFaqRepository.findByBankProfileOrganizationIdAndStatusAndIsDeletedFalse(
            organizationId, FaqStatus.ACTIVE, pageable);
    return getFaqPaginatedResponseDTO(bankFaqs);
  }

  @Override
  public PaginatedResponseDTO<BankFaqDTO> retrieveAllFaqs(int pageNumber, int pageSize) {
    Pageable pageable = getPageable(pageNumber, pageSize, SORT_BY_CREATED_DATE);
    Page<BankFaq> bankFaqs =
        bankFaqRepository.findByBankProfileOrganizationIdAndIsDeletedFalse(
            RequestContextHolder.get().getOrganizationId(), pageable);
    return getFaqPaginatedResponseDTO(bankFaqs);
  }

  private PaginatedResponseDTO<BankFaqDTO> getFaqPaginatedResponseDTO(Page<BankFaq> bankFaqs) {
    return PaginatedResponseDTO.<BankFaqDTO>builder()
        .content(bankFaqs.getContent().stream().map(this::mapBankFaqEntityToDTO).toList())
        .currentPage(bankFaqs.getNumber() + 1)
        .totalPages(bankFaqs.getTotalPages())
        .totalItems(bankFaqs.getTotalElements())
        .isFirstPage(bankFaqs.isFirst())
        .isLastPage(bankFaqs.isLast())
        .build();
  }

  @Override
  public BankFaqDTO retrieveFaq(String faqId) {
    BankFaq bankFaq = retrieveBankFaqEntity(faqId);

    return mapBankFaqEntityToDTO(bankFaq);
  }

  private BankFaq retrieveBankFaqEntity(String faqId) {
    return bankFaqRepository
        .findByBankProfileOrganizationIdAndFaqIdAndIsDeletedFalse(
            RequestContextHolder.get().getOrganizationId(), faqId)
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getFaqMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }

  private BankFaqDTO mapBankFaqEntityToDTO(BankFaq bankFaq) {
    BankFaqDTO bankFaqDTO = new BankFaqDTO();
    BeanUtilWrapper.copyNonNullProperties(bankFaq, bankFaqDTO);
    return bankFaqDTO;
  }
}
