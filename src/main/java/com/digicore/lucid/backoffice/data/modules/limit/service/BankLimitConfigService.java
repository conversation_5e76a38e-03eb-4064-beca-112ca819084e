/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.limit.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;
import static com.digicore.lucid.common.lib.util.ClientUtil.getPageable;
import static com.digicore.registhentication.util.PageableUtil.SORT_BY_CREATED_DATE;

import com.digicore.lucid.backoffice.data.modules.limit.model.BankLimitConfig;
import com.digicore.lucid.backoffice.data.modules.limit.repository.BankLimitConfigRepository;
import com.digicore.lucid.common.lib.limit.dto.BankLimitConfigDTO;
import com.digicore.lucid.common.lib.limit.dto.LimitBucketDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.limit.service.LimitConfigService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.validator.enums.Currency;
import jakarta.transaction.Transactional;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Feb-25(Tue)-2025
 */

@Service
@RequiredArgsConstructor
@Transactional
public class BankLimitConfigService implements LimitConfigService<BankLimitConfigDTO> {
  private final BankLimitConfigRepository bankLimitConfigRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  @Override
  public PaginatedResponseDTO<BankLimitConfigDTO> retrieveLimitConfig(
      String organizationId, int pageNumber, int pageSize) {
    Pageable pageable = getPageable(pageNumber, pageSize, SORT_BY_CREATED_DATE);
    Page<BankLimitConfig> limitConfigs =
        bankLimitConfigRepository.findAllByBankProfileOrganizationId(organizationId, pageable);
    return getLimitPaginatedResponseDTO(limitConfigs);
  }

  @Override
  public BankLimitConfigDTO retrieveLimitConfig(
      String queryValue, LimitType limitType, Currency currency, boolean defaultLimit) {
    BankLimitConfig bankLimitConfig =
        bankLimitConfigRepository
            .findFirstByBankProfileOrganizationIdAndLimitTypeAndCurrencyAndDefaultLimit(
                queryValue, limitType, currency, defaultLimit)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getLimitMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
    return getLimitConfigDTO(bankLimitConfig);
  }

  @Override
  public List<BankLimitConfigDTO> retrieveLimitConfig(
      List<LimitBucketDTO> limitBucketDTOS, String bankOrganizationId) {
    List<BankLimitConfigDTO> limitConfigDTOS = new ArrayList<>();
    limitBucketDTOS.forEach(
        limitBucketDTO -> {
          List<BankLimitConfig> bankLimitConfigs =
              bankLimitConfigRepository.findAllByBankProfileOrganizationIdAndLimitTypeAndCurrency(
                  bankOrganizationId, limitBucketDTO.getLimitType(), limitBucketDTO.getCurrency());
          limitConfigDTOS.addAll(bankLimitConfigs.stream().map(this::getLimitConfigDTO).toList());
        });
    return limitConfigDTOS;
  }

  @Override
  public List<BankLimitConfigDTO> retrieveLimitConfigs(
      String queryValue, LimitType limitType, Currency currency) {
    List<BankLimitConfig> bankLimitConfigs =
        bankLimitConfigRepository.findAllByBankProfileOrganizationIdAndLimitTypeAndCurrency(
            queryValue, limitType, currency);
    return bankLimitConfigs.stream().map(this::getLimitConfigDTO).toList();
  }

  @Override
  public void updateLimitConfig(BankLimitConfigDTO updateRequest) {
    BankLimitConfig existingBankLimitConfig =
        getBankLimitConfig(
            updateRequest.getLimitType(),
            updateRequest.getCurrency(),
            updateRequest.getOrganizationId(),
            updateRequest.isDefaultLimit());

    BeanUtilWrapper.copyNonNullProperties(updateRequest, existingBankLimitConfig);
    bankLimitConfigRepository.save(existingBankLimitConfig);
  }

  @Override
  public BankLimitConfigDTO verifyLimitConfigExist(BankLimitConfigDTO limitConfigDTO) {

    return getLimitConfigDTO(
        getBankLimitConfig(
            limitConfigDTO.getLimitType(),
            limitConfigDTO.getCurrency(),
            limitConfigDTO.getOrganizationId(),
            limitConfigDTO.isDefaultLimit()));
  }

  private BankLimitConfigDTO getLimitConfigDTO(BankLimitConfig bankLimitConfig) {
    BankLimitConfigDTO dto = new BankLimitConfigDTO();
    dto.setOrganizationId(bankLimitConfig.getBankProfile().getOrganizationId());
    BeanUtilWrapper.copyNonNullProperties(bankLimitConfig, dto);
    return dto;
  }

  private BankLimitConfig getBankLimitConfig(
      LimitType limitType, Currency currency, String organizationId, boolean defaultLimit) {
    return bankLimitConfigRepository
        .findFirstByBankProfileOrganizationIdAndLimitTypeAndCurrencyAndDefaultLimit(
            organizationId, limitType, currency, defaultLimit)
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getLimitMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }

  private PaginatedResponseDTO<BankLimitConfigDTO> getLimitPaginatedResponseDTO(
      Page<BankLimitConfig> transactionLimits) {
    return PaginatedResponseDTO.<BankLimitConfigDTO>builder()
        .content(transactionLimits.getContent().stream().map(this::getLimitConfigDTO).toList())
        .currentPage(transactionLimits.getNumber() + 1)
        .totalPages(transactionLimits.getTotalPages())
        .totalItems(transactionLimits.getTotalElements())
        .isFirstPage(transactionLimits.isFirst())
        .isLastPage(transactionLimits.isLast())
        .build();
  }
}
