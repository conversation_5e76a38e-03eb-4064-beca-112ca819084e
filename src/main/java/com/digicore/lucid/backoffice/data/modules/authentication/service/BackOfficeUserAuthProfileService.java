/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.authentication.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.message.MessagePlaceHolderConstant.EMAIL;

import com.digicore.lucid.backoffice.data.modules.authentication.model.BackOfficeUserAuthProfile;
import com.digicore.lucid.backoffice.data.modules.authentication.repository.BackOfficeUserAuthProfileRepository;
import com.digicore.lucid.backoffice.data.modules.authorization.model.BackOfficeUserPermission;
import com.digicore.lucid.backoffice.data.modules.authorization.model.BackOfficeUserRole;
import com.digicore.lucid.backoffice.data.modules.profile.model.BackOfficeUserProfile;
import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.authorization.dto.PermissionDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleCreationDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleDTO;
import com.digicore.lucid.common.lib.authorization.service.PermissionService;
import com.digicore.lucid.common.lib.authorization.service.RoleService;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.profile.dto.UserProfileProjection;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.UserRegistrationDTO;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.enums.Status;
import jakarta.transaction.Transactional;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Jan-22(Wed)-2025
 */

@Service
@RequiredArgsConstructor
@Transactional
public class BackOfficeUserAuthProfileService implements AuthProfileService<UserAuthProfileDTO> {
  private final RoleService<RoleDTO, RoleCreationDTO> backOfficeUserRoleService;
  private final PermissionService<PermissionDTO> backOfficeUserPermissionService;
  private final BackOfficeUserAuthProfileRepository backOfficeUserAuthProfileRepository;
  private final PasswordEncoder passwordEncoder;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  @Override
  public <V, K> void saveNewAuthProfile(K registrationDTO, V userProfile) {
    UserRegistrationDTO registrationRequest = (UserRegistrationDTO) registrationDTO;
    BackOfficeUserRole backOfficeUserRole =
        backOfficeUserRoleService.retrieveRoleEntity(
            registrationRequest.getAssignedRole(),
            registrationRequest.isSystemInitiated()
                ? registrationRequest.getOrganizationId()
                : RequestContextHolder.get().getOrganizationId());

    BackOfficeUserProfile backOfficeUserProfile = (BackOfficeUserProfile) userProfile;
    doProfileCheck(registrationRequest.getUsername(), registrationRequest.getOrganizationId());
    BackOfficeUserAuthProfile backOfficeUserAuthProfile = new BackOfficeUserAuthProfile();
    BeanUtilWrapper.copyNonNullProperties(registrationRequest, backOfficeUserAuthProfile);
    backOfficeUserAuthProfile.setBackOfficeUserProfile(backOfficeUserProfile);
    backOfficeUserAuthProfile.setPin("N/A");
    backOfficeUserAuthProfile.setPassword(
        passwordEncoder.encode(registrationRequest.getPassword()));
    backOfficeUserAuthProfile.setDefaultPassword(true);
    backOfficeUserAuthProfile.setStatus(Status.PENDING_INVITE_ACCEPTANCE);
    backOfficeUserAuthProfile.setAssignedRole(backOfficeUserRole.getName());
    backOfficeUserAuthProfile.setPermissions(new HashSet<>(backOfficeUserRole.getPermissions()));
    backOfficeUserAuthProfileRepository.save(backOfficeUserAuthProfile);
  }

  @Override
  public <V, K> void saveNewAuthProfile(List<K> registrationDTO, List<V> userProfile) {
    List<UserRegistrationDTO> registrationRequest = (List<UserRegistrationDTO>) registrationDTO;
    List<BackOfficeUserAuthProfile> backOfficeUserAuthProfiles = new ArrayList<>();
    registrationRequest.forEach(
        request -> {
          BackOfficeUserRole backOfficeUserRole =
              backOfficeUserRoleService.retrieveRoleEntity(request.getAssignedRole());
          BackOfficeUserProfile backOfficeUserProfile = (BackOfficeUserProfile) userProfile;
          doProfileCheck(request.getUsername(), request.getOrganizationId());
          BackOfficeUserAuthProfile backOfficeUserAuthProfile = new BackOfficeUserAuthProfile();
          BeanUtilWrapper.copyNonNullProperties(request, backOfficeUserAuthProfile);
          backOfficeUserAuthProfile.setBackOfficeUserProfile(backOfficeUserProfile);
          backOfficeUserAuthProfile.setPin("N/A");
          backOfficeUserAuthProfile.setPassword(passwordEncoder.encode(request.getPassword()));
          backOfficeUserAuthProfile.setDefaultPassword(true);
          backOfficeUserAuthProfile.setStatus(Status.PENDING_INVITE_ACCEPTANCE);
          backOfficeUserAuthProfile.setAssignedRole(backOfficeUserRole.getName());
          backOfficeUserAuthProfile.setPermissions(
              new HashSet<>(backOfficeUserRole.getPermissions()));
          backOfficeUserAuthProfiles.add(backOfficeUserAuthProfile);
        });
    backOfficeUserAuthProfileRepository.saveAll(backOfficeUserAuthProfiles);
  }

  @Override
  public UserAuthProfileDTO retrieveAuthProfile(String email, boolean isForPasswordRest) {
    if (isForPasswordRest) {
      UserAuthProfileDTO userAuthProfileDTO = new UserAuthProfileDTO();
      UserProfileProjection backOfficeUserAuthProfile =
          backOfficeUserAuthProfileRepository
              .findFirstByUsername(email, RequestContextHolder.get().getOrganizationId())
              .orElseThrow(
                  () ->
                      exceptionHandler.processCustomException(
                          messagePropertyConfig.getUserMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
      userAuthProfileDTO.setUsername(email);
      userAuthProfileDTO.setStatus(backOfficeUserAuthProfile.getStatus());
      return userAuthProfileDTO;
    }
    BackOfficeUserAuthProfile backOfficeUserAuthProfile =
        backOfficeUserAuthProfileRepository
            .findFirstByUsernameAndIsDeletedFalseAndBackOfficeUserProfileBankProfileOrganizationIdOrderByCreatedDate(
                email, RequestContextHolder.get().getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getUserMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
    return mapbackOfficeUserAuthProfileEntityToDTO(backOfficeUserAuthProfile);
  }

  private void doProfileCheck(String username, String organizationId) {
    if (backOfficeUserAuthProfileRepository
        .existsByUsernameAndIsDeletedFalseAndBackOfficeUserProfileBankProfileOrganizationId(
            username, organizationId))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getUserMessage(DUPLICATE), HttpStatus.BAD_REQUEST);
  }

  private UserAuthProfileDTO mapbackOfficeUserAuthProfileEntityToDTO(
      BackOfficeUserAuthProfile backOfficeUserAuthProfile) {
    UserAuthProfileDTO userAuthProfileDTO = new UserAuthProfileDTO();
    userAuthProfileDTO.setPermissions(
        backOfficeUserAuthProfile.getPermissions().stream()
            .map(backOfficeUserPermissionService::mapEntityToDTO)
            .collect(Collectors.toSet()));
    BeanUtilWrapper.copyNonNullProperties(backOfficeUserAuthProfile, userAuthProfileDTO);
    UserProfileDTO userProfileDTO = new UserProfileDTO();
    BeanUtilWrapper.copyNonNullProperties(
        backOfficeUserAuthProfile.getBackOfficeUserProfile(), userProfileDTO);
    userAuthProfileDTO.setUserProfile(userProfileDTO);
    return userAuthProfileDTO;
  }

  @Override
  public void updateAuthProfile(UserAuthProfileDTO authProfile) {
    backOfficeUserRoleService.validateRole(
        authProfile.getAssignedRole(), authProfile.isSystemInitiated(), false);
    backOfficeUserAuthProfileRepository.save(ensureAuthProfileInfoRemainsValid(authProfile));
  }

  @Override
  public void updateAuthProfilePassword(UserAuthProfileDTO authProfile) {
    authProfile.setPassword(passwordEncoder.encode(authProfile.getPassword()));
    backOfficeUserAuthProfileRepository.updatePassword(
        authProfile.getStatus(),
        authProfile.getPassword(),
        authProfile.isDefaultPassword(),
        authProfile.getUsername(),
        RequestContextHolder.get().getOrganizationId());
  }

  @Override
  public <V> V retrieveAuthProfileEntity(String email) {
    return (V)
        backOfficeUserAuthProfileRepository
            .findFirstByUsernameAndIsDeletedFalseAndBackOfficeUserProfileBankProfileOrganizationIdOrderByCreatedDate(
                email, RequestContextHolder.get().getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getUserMessage(NOT_FOUND).replace(EMAIL, email),
                        HttpStatus.BAD_REQUEST));
  }

  private BackOfficeUserAuthProfile ensureAuthProfileInfoRemainsValid(
      UserAuthProfileDTO authProfile) {
    BackOfficeUserAuthProfile existingAuthProfile =
        backOfficeUserAuthProfileRepository
            .findFirstByUsernameAndIsDeletedFalseAndBackOfficeUserProfileBankProfileOrganizationIdOrderByCreatedDate(
                authProfile.getUsername(), RequestContextHolder.get().getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig
                            .getUserMessage(NOT_FOUND)
                            .replace(EMAIL, authProfile.getUsername()),
                        HttpStatus.BAD_REQUEST));

    if (null != authProfile.getAssignedRole()
        && !authProfile.getAssignedRole().isBlank()
        && (!existingAuthProfile
            .getAssignedRole()
            .equalsIgnoreCase(authProfile.getAssignedRole()))) {
      Set<BackOfficeUserPermission> permissions =
          backOfficeUserPermissionService.retrieveSystemPermissions(
              backOfficeUserRoleService
                  .retrieveRole(authProfile.getAssignedRole())
                  .getPermissions()
                  .stream()
                  .map(PermissionDTO::getName)
                  .collect(Collectors.toSet()));
      if (authProfile.getPermissions() != null && !authProfile.getPermissions().isEmpty()) {
        List<String> existingPermissionNames =
            permissions.stream().map(BackOfficeUserPermission::getName).toList();
        Set<BackOfficeUserPermission> extraPermissions =
            backOfficeUserPermissionService.retrieveSystemPermissions(
                authProfile.getPermissions().stream()
                    .map(PermissionDTO::getName)
                    .collect(Collectors.toSet()));
        // Filter out permissions that already exist
        List<BackOfficeUserPermission> permissionsToAdd =
            extraPermissions.stream()
                .filter(permission -> !existingPermissionNames.contains(permission.getName()))
                .toList();
        permissions.addAll(permissionsToAdd);
        existingAuthProfile.setPermissions(new HashSet<>(permissions));
      } else {
        existingAuthProfile.setPermissions(new HashSet<>(permissions));
      }
      existingAuthProfile.setAssignedRole(authProfile.getAssignedRole());
    } else if (authProfile.getPermissions() != null && !authProfile.getPermissions().isEmpty()) {
      Set<BackOfficeUserPermission> permissions =
          backOfficeUserPermissionService.retrieveSystemPermissions(
              authProfile.getPermissions().stream()
                  .map(PermissionDTO::getName)
                  .collect(Collectors.toSet()));
      existingAuthProfile.setPermissions(new HashSet<>(permissions));
    }

    if (null != authProfile.getUsername() && !authProfile.getUsername().isBlank())
      existingAuthProfile.setUsername(authProfile.getUsername());

    existingAuthProfile.setDefaultPassword(authProfile.isDefaultPassword());

    return existingAuthProfile;
  }
}
