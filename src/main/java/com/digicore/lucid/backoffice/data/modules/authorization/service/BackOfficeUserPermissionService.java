/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.authorization.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;

import com.digicore.lucid.backoffice.data.modules.authorization.model.BackOfficeUserPermission;
import com.digicore.lucid.backoffice.data.modules.authorization.repository.BackOfficeUserPermissionRepository;
import com.digicore.lucid.common.lib.authorization.dto.PermissionDTO;
import com.digicore.lucid.common.lib.authorization.service.PermissionService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Jan-22(Wed)-2025
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class BackOfficeUserPermissionService implements PermissionService<PermissionDTO> {
  private final BackOfficeUserPermissionRepository backOfficeUserPermissionRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  /**
   * Retrieves all system permissions.
   *
   * @return a set of system permissions
   */
  @Override
  public Set<PermissionDTO> retrieveSystemPermissions() {
    return backOfficeUserPermissionRepository.findAll().stream()
        .map(this::mapEntityToDTO)
        .collect(Collectors.toSet());
  }

  @Override
  public <K> Set<K> retrieveSystemPermissions(Set<String> selectedPermissions) {
    Set<BackOfficeUserPermission> permissionSet = new HashSet<>();
    boolean treatRequestAdded = false;
    for (String permission : selectedPermissions) {
      permissionSet.add(retrievePermission(permission));
      if (permission.startsWith("approve-")) {
        String remainingWords = permission.substring("approve-".length());
        if (selectedPermissions.contains(remainingWords)) {
          exceptionHandler.processCustomExceptions(
              messagePropertyConfig.getRoleMessage(CONFLICT), HttpStatus.BAD_REQUEST);
        }
        if (!selectedPermissions.contains("treat-requests") && !treatRequestAdded) {
          permissionSet.add(retrievePermission("treat-requests"));
          treatRequestAdded = true;
        }
      }
    }
    return (Set<K>) permissionSet;
  }

  /**
   * Retrieves a system permission by name.
   *
   * @param name the name of the permission
   * @return the system permission with the specified name
   */
  @Override
  public PermissionDTO retrieveSystemPermission(String name) {
    return null;
  }

  @Override
  public void validatePermissions(List<String> permissions) {

    // Retrieve all existing permission names from the repository
    List<String> existingPermissionNames =
        backOfficeUserPermissionRepository.retrieveAllPermissionName().stream()
            .map(PermissionDTO::getName)
            .toList();

    // Filter out permissions that do not exist
    String inValidPermissions =
        permissions.stream()
            .filter(permission -> !existingPermissionNames.contains(permission))
            .collect(Collectors.joining(","));

    if (!inValidPermissions.isEmpty())
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig
              .getRoleMessage(INVALID_PERMISSION)
              .replace(INVALID_PERMISSIONS, inValidPermissions),
          HttpStatus.BAD_REQUEST);
  }

  /**
   * Adds system permissions.
   *
   * @param newPermissions the set of new permissions to add
   */
  @Override
  public void addSystemPermissions(Set<PermissionDTO> newPermissions) {
    // Retrieve all existing permission names from the repository
    List<String> existingPermissionNames =
        backOfficeUserPermissionRepository.retrieveAllPermissionName().stream()
            .map(PermissionDTO::getName)
            .toList();

    // Filter out permissions that already exist
    List<PermissionDTO> permissionsToAdd =
        newPermissions.stream()
            .filter(permission -> !existingPermissionNames.contains(permission.getName()))
            .toList();

    // Convert to entities and save new permissions
    List<BackOfficeUserPermission> entitiesToAdd =
        permissionsToAdd.stream()
            .map(
                permissionDTO -> {
                  BackOfficeUserPermission permission = new BackOfficeUserPermission();
                  permission.setName(permissionDTO.getName());
                  permission.setDescription(permissionDTO.getDescription());
                  permission.setPermissionType(permissionDTO.getPermissionType());
                  return permission;
                })
            .toList();

    // Save all new permissions to the database
    backOfficeUserPermissionRepository.saveAll(entitiesToAdd);
  }

  @Override
  public <K> K mapDTOToEntity(PermissionDTO permissionDTO) {
    BackOfficeUserPermission permission = new BackOfficeUserPermission();
    BeanUtilWrapper.copyNonNullProperties(permissionDTO, permission);
    return (K) permission;
  }

  @Override
  public <K> PermissionDTO mapEntityToDTO(K permission) {
    PermissionDTO permissionDTO = new PermissionDTO();
    BeanUtilWrapper.copyNonNullProperties(permission, permissionDTO);
    return permissionDTO;
  }

  private BackOfficeUserPermission retrievePermission(String name) {
    return backOfficeUserPermissionRepository
        .findFirstByNameOrderByCreatedDate(name)
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getRoleMessage(DUPLICATE), HttpStatus.BAD_REQUEST));
  }

  public PermissionDTO mapEntityToDTO(BackOfficeUserPermission permission) {
    PermissionDTO permissionDTO = new PermissionDTO();
    BeanUtilWrapper.copyNonNullProperties(permission, permissionDTO);
    return permissionDTO;
  }
}
