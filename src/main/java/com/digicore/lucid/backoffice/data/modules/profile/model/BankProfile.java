/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.profile.model;

import com.digicore.lucid.backoffice.data.modules.faq.model.BankFaq;
import com.digicore.lucid.backoffice.data.modules.limit.model.BankLimitConfig;
import com.digicore.lucid.common.lib.util.RegistrationUtil;
import com.digicore.registhentication.registration.models.OrganizationProfile;
import jakarta.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/*
 * <AUTHOR>
 * @createdOn Feb-03(Mon)-2025
 */

@Entity
@Table(name = "bank_profile")
@Getter
@Setter
@ToString
public class BankProfile extends OrganizationProfile implements Serializable {
  private String subDomainName;
  private String cbaProvider;
  private String cbaToken;
  private String applicationId;
  private String bankCode;

  @Column(columnDefinition = "text")
  private String accountOfficers;

  @Column(columnDefinition = "text")
  private String productCodes;

  @OneToOne(mappedBy = "bankProfile")
  private BankPreference bankPreference;

  @OneToMany(mappedBy = "bankProfile", cascade = CascadeType.ALL)
  @ToString.Exclude
  private Set<BackOfficeUserProfile> backOfficeUserProfiles = new HashSet<>();

  @OneToMany(mappedBy = "bankProfile", cascade = CascadeType.ALL)
  @ToString.Exclude
  private Set<BankAccountOfficer> bankAccountOfficers = new HashSet<>();

  @OneToMany(mappedBy = "bankProfile", cascade = CascadeType.ALL)
  @ToString.Exclude
  private Set<BankLimitConfig> bankLimitConfigs = new HashSet<>();

  //  @OneToMany(mappedBy = "bankProfile", cascade = CascadeType.ALL)
  //  @ToString.Exclude
  //  private Set<BankFeeConfig> bankFeeConfigs = new HashSet<>();

  @OneToMany(mappedBy = "bankProfile", cascade = CascadeType.ALL)
  @ToString.Exclude
  private Set<BankFaq> bankFaqs = new HashSet<>();

  @PrePersist
  public void generateOrganizationId() {
    setOrganizationId("B".concat(RegistrationUtil.generateProfileId()));
  }
}
