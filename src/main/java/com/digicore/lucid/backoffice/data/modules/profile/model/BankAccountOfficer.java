/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.profile.model;

import com.digicore.registhentication.registration.models.BaseModel;
import jakarta.persistence.*;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/*
 * <AUTHOR>
 * @createdOn May-11(Sun)-2025
 */

@Entity
@Table(name = "bank_account_officer")
@Getter
@Setter
@ToString
public class BankAccountOfficer extends BaseModel implements Serializable {
  private String name;
  private String code;
  private String branch;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "bank_profile_id")
  private BankProfile bankProfile;
}
