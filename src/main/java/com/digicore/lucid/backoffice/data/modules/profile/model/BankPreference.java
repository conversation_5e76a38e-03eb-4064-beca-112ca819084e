/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.profile.model;

import com.digicore.registhentication.registration.models.BaseModel;
import jakarta.persistence.*;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/*
 * <AUTHOR>
 * @createdOn Feb-11(Tue)-2025
 */

@Entity
@Table(name = "bank_preference")
@Getter
@Setter
@ToString
public class BankPreference extends BaseModel implements Serializable {

  @Column(columnDefinition = "text")
  private String preferenceConfig;

  @OneToOne(cascade = CascadeType.ALL)
  @JoinColumn(name = "bank_profile_id", referencedColumnName = "id")
  @ToString.Exclude
  private BankProfile bankProfile;
}
