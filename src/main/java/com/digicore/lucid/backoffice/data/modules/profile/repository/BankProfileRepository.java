/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.profile.repository;

import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.BANK_PROFILE_DTO;

import com.digicore.lucid.backoffice.data.modules.profile.model.BankProfile;
import com.digicore.lucid.common.lib.profile.dto.BankProfileDTO;
import com.digicore.registhentication.registration.enums.Status;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

/*
 * <AUTHOR>
 * @createdOn Feb-03(Mon)-2025
 */

public interface BankProfileRepository extends JpaRepository<BankProfile, Long> {
  boolean existsByOrganizationNameOrOrganizationEmailOrOrganizationPhoneNumberAndIsDeletedFalse(
      String name, String email, String phoneNumber);

  Optional<BankProfile> findFirstByOrganizationIdAndIsDeletedFalseOrderByCreatedDateDesc(
      String organizationId);

  @Query(
      "SELECT new "
          + BANK_PROFILE_DTO
          + "(a.accountOfficers, a.productCodes) FROM BankProfile a WHERE a.organizationId = :organizationId")
  Optional<BankProfileDTO> retrieveAccountOfficersAndProductCodes(String organizationId);

  @Query(
      "SELECT new "
          + BANK_PROFILE_DTO
          + "(a.organizationId, a.organizationName, a.organizationEmail, a.organizationPhoneNumber, a.status, a.cbaProvider, a.applicationId, a.bankCode, a.subDomainName, a.createdDate) FROM BankProfile a")
  Page<BankProfileDTO> findAllBankProfiles(Pageable pageable);

  @Query(
      "SELECT new "
          + BANK_PROFILE_DTO
          + "(a.organizationId, a.cbaToken, a.cbaProvider,a.applicationId, a.bankCode, a.subDomainName) FROM BankProfile a WHERE a.status = :status AND a.subDomainName = :subDomainName")
  Optional<BankProfileDTO> retrieveOrganizationIdAndCbaTokenAndCbaProvider(
      Status status, String subDomainName);

  @Query(
      "SELECT new "
          + BANK_PROFILE_DTO
          + "(a.organizationId, a.cbaToken, a.cbaProvider,a.applicationId, a.bankCode, a.subDomainName) FROM BankProfile a WHERE a.status = :status AND a.applicationId = :applicationId")
  Optional<BankProfileDTO> retrieveOrganizationIdAndCbaTokenAndCbaProviderUsingApplicationId(
      Status status, String applicationId);

  @Query(
      "SELECT new "
          + BANK_PROFILE_DTO
          + "(a.organizationName) FROM BankProfile a WHERE a.organizationId = :organizationId")
  Optional<BankProfileDTO> retrieveBankNameByOrganizationId(String organizationId);

  @Query(
      "SELECT new "
          + BANK_PROFILE_DTO
          + "(a.organizationName) FROM BankProfile a WHERE a.subDomainName = :subDomainName")
  Optional<BankProfileDTO> retrieveBankNameBySubDomain(String subDomainName);
}
