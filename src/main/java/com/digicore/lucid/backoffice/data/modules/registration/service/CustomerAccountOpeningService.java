/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.registration.service;

/*
 * <AUTHOR>
 * @createdOn Feb-21(Fri)-2025
 */

import com.digicore.lucid.common.lib.activation.dto.CustomerActivationDTO;
import com.digicore.registhentication.registration.enums.Status;
import java.util.List;
import java.util.Optional;

public interface CustomerAccountOpeningService<T> {
  void saveCustomerAccountOpening(T customerAccountOpening);

  T initializeOrLoadTrackingRecord(CustomerActivationDTO customerActivationDTO);

  Optional<T> findByOrganizationId(String organizationId);

  List<T> findAllByStatusIn(List<Status> statuses);
}
