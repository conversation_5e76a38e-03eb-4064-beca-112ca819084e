/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.authorization.repository;

import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.ROLE_PROJECTION;

import com.digicore.lucid.backoffice.data.modules.authorization.model.BackOfficeUserRole;
import com.digicore.lucid.common.lib.authorization.dto.RoleProjection;
import com.digicore.lucid.common.lib.authorization.dto.RoleWorkFlowDTO;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/*
 * <AUTHOR>
 * @createdOn Jan-22(Wed)-2025
 */

public interface BackOfficeUserRoleRepository extends JpaRepository<BackOfficeUserRole, Long> {

  Optional<BackOfficeUserRole>
      findFirstByNameAndIsDeletedAndBankProfileOrganizationIdOrderByCreatedDateDesc(
          String name, boolean deleted, String organizationId);

  @Query(
      "SELECT new "
          + ROLE_PROJECTION
          + "(a.name) FROM BackOfficeUserRole a WHERE a.active = :status AND a.bankProfile.organizationId = :organizationId")
  List<RoleProjection> findAllByActive(boolean status, String organizationId);

  @Query(
      """
            SELECT new com.digicore.lucid.common.lib.authorization.dto.RoleWorkFlowDTO(
                r.name,
                CASE
                    WHEN COUNT(p) FILTER (WHERE p.name LIKE 'approve-%') > 0 THEN false
                    ELSE true
                END
            )
            FROM BackOfficeUserRole r
            JOIN r.permissions p
            JOIN r.bankProfile bp
            WHERE bp.organizationId = :organizationId
            GROUP BY r.name
        """)
  List<RoleWorkFlowDTO> findRolesWithInitiatorStatus(
      @Param("organizationId") String organizationId);

  Page<BackOfficeUserRole> findAllByIsDeletedAndBankProfileOrganizationId(
      boolean deleted, String organizationId, Pageable pageable);

  boolean existsByNameAndIsDeletedFalseAndBankProfileOrganizationId(
      String name, String organizationId);
}
