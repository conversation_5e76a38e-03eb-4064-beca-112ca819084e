/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.registration.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.message.MessagePlaceHolderConstant.EMAIL;
import static com.digicore.lucid.common.lib.constant.system.SystemConstant.*;
import static com.digicore.lucid.common.lib.util.RegistrationUtil.getUserProfileDTO;

import com.digicore.lucid.backoffice.data.modules.profile.model.BackOfficeUserProfile;
import com.digicore.lucid.backoffice.data.modules.profile.model.BankProfile;
import com.digicore.lucid.backoffice.data.modules.profile.repository.BackOfficeUserProfileRepository;
import com.digicore.lucid.backoffice.data.modules.profile.repository.BankProfileRepository;
import com.digicore.lucid.backoffice.data.modules.util.BankProfileUtil;
import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.authorization.dto.PermissionDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleCreationDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleDTO;
import com.digicore.lucid.common.lib.authorization.service.PermissionService;
import com.digicore.lucid.common.lib.authorization.service.RoleService;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.UserRegistrationDTO;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.services.RegistrationService;
import jakarta.transaction.Transactional;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Jan-22(Wed)-2025
 */

@Service
@RequiredArgsConstructor
@Transactional
public class BackOfficeUserRegistrationService
    implements RegistrationService<UserProfileDTO, UserRegistrationDTO> {
  private final BackOfficeUserProfileRepository backOfficeUserProfileRepository;
  private final RoleService<RoleDTO, RoleCreationDTO> backOfficeUserRoleService;
  private final AuthProfileService<UserAuthProfileDTO> backOfficeUserAuthProfileService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final BankProfileRepository bankProfileRepository;
  private final PermissionService<PermissionDTO> backOfficeUserPermissionService;

  @Override
  public UserProfileDTO createProfile(UserRegistrationDTO registrationRequest) {
    if (profileExistenceCheckByEmail(registrationRequest.getEmail()))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getUserMessage(DUPLICATE), HttpStatus.BAD_REQUEST);
    backOfficeUserRoleService.validateRole(
        registrationRequest.getAssignedRole(), registrationRequest.isSystemInitiated(), false);
    backOfficeUserAuthProfileService.saveNewAuthProfile(
        registrationRequest, createBackOfficeUserProfile(registrationRequest));
    return getUserProfileDTO(registrationRequest);
  }

  @Override
  public List<UserProfileDTO> createProfile(List<UserRegistrationDTO> registrationRequest) {
    final boolean[] makerCreated = {false};
    Permissions permissions = getPermissions();
    registrationRequest.forEach(
        request -> {
          if (profileExistenceCheckByEmail(request.getEmail()))
            exceptionHandler.processCustomExceptions(
                messagePropertyConfig.getUserMessage(DUPLICATE).replace(EMAIL, request.getEmail()),
                HttpStatus.BAD_REQUEST);
          if (!makerCreated[0]) {
            createRole(
                INITIATOR_ROLE_NAME,
                ADMIN_MAKER_ROLE_DESCRIPTION,
                permissions.existingMakerPermissions(),
                request.getOrganizationId());
            makerCreated[0] = true;
            request.setAssignedRole(INITIATOR_ROLE_NAME);
          } else {
            createRole(
                AUTHORIZER_ROLE_NAME,
                ADMIN_CHECKER_ROLE_DESCRIPTION,
                permissions.existingCheckerPermissions(),
                request.getOrganizationId());
            request.setAssignedRole(AUTHORIZER_ROLE_NAME);
          }
          backOfficeUserAuthProfileService.saveNewAuthProfile(
              request, createBackOfficeUserProfile(request));
        });
    return getUserProfileDTO(registrationRequest);
  }

  private Permissions getPermissions() {
    Set<PermissionDTO> existingPermissions =
        backOfficeUserPermissionService.retrieveSystemPermissions();
    Set<String> existingViewerPermissions =
        existingPermissions.stream()
            .map(PermissionDTO::getName)
            .filter(name -> name.startsWith(VIEW_PERMISSION_PREFIX))
            .collect(Collectors.toSet());
    Set<String> existingMakerPermissions =
        existingPermissions.stream()
            .map(PermissionDTO::getName)
            .filter(name -> !name.startsWith(APPROVE_PERMISSION_PREFIX))
            .collect(Collectors.toSet());
    existingMakerPermissions.remove("treat-requests");
    Set<String> existingCheckerPermissions =
        existingPermissions.stream()
            .map(PermissionDTO::getName)
            .filter(name -> name.startsWith(APPROVE_PERMISSION_PREFIX))
            .collect(Collectors.toSet());
    existingCheckerPermissions.add("treat-requests");
    existingCheckerPermissions.addAll(existingViewerPermissions);
    return new Permissions(existingMakerPermissions, existingCheckerPermissions);
  }

  private record Permissions(
      Set<String> existingMakerPermissions, Set<String> existingCheckerPermissions) {}

  @Override
  public boolean profileExistenceCheckByEmail(String email) {
    return backOfficeUserProfileRepository.existsByEmail(email);
  }

  @Override
  public boolean profileExistenceCheckByEmail(String email, String companyName) {
    return backOfficeUserProfileRepository
        .existsByEmailAndIsDeletedFalseAndBankProfileOrganizationName(email, companyName);
  }

  private BackOfficeUserProfile createBackOfficeUserProfile(
      UserRegistrationDTO registrationRequest) {
    BackOfficeUserProfile backOfficeUserProfile = new BackOfficeUserProfile();
    BeanUtilWrapper.copyNonNullProperties(registrationRequest, backOfficeUserProfile);
    BankProfile bankProfile =
        BankProfileUtil.getBankProfile(
            registrationRequest.isSystemInitiated()
                ? registrationRequest.getOrganizationId()
                : RequestContextHolder.get().getOrganizationId(),
            bankProfileRepository,
            exceptionHandler,
            messagePropertyConfig);
    backOfficeUserProfile.setBankProfile(bankProfile);
    backOfficeUserProfileRepository.save(backOfficeUserProfile);
    backOfficeUserProfile.setReferralCode(
        "REF-".concat(backOfficeUserProfile.getProfileId()).toUpperCase());
    return backOfficeUserProfile;
  }

  //  private List<BackOfficeUserProfile> createBackOfficeUserProfile(
  //      List<UserRegistrationDTO> registrationRequest) {
  //    BankProfile bankProfile =
  // BankProfileUtil.getBankProfile(registrationRequest.getFirst().getOrganizationId(),bankProfileRepository,exceptionHandler,messagePropertyConfig);
  //    List<BackOfficeUserProfile> backOfficeUserProfiles = new ArrayList<>();
  //    registrationRequest.forEach(
  //        request -> {
  //          BackOfficeUserProfile backOfficeUserProfile = new BackOfficeUserProfile();
  //          BeanUtilWrapper.copyNonNullProperties(request, backOfficeUserProfile);
  //          backOfficeUserProfile.setBankProfile(bankProfile);
  //          backOfficeUserProfileRepository.save(backOfficeUserProfile);
  //          backOfficeUserProfile.setReferralCode(
  //                  "REF-".concat(backOfficeUserProfile.getProfileId()).toUpperCase());
  //          backOfficeUserProfiles.add(backOfficeUserProfile);
  //        });
  //    return backOfficeUserProfiles;
  //  }

  private void createRole(
      String adminCheckerRoleName,
      String adminCheckerRoleDescription,
      Set<String> permissions,
      String organizationId) {
    // Create/Update admin role
    RoleCreationDTO roleCreationDTO = new RoleCreationDTO();
    roleCreationDTO.setName(adminCheckerRoleName);
    roleCreationDTO.setDescription(adminCheckerRoleDescription);
    roleCreationDTO.setPermissions(permissions);
    roleCreationDTO.setSystemInitiated(true);
    roleCreationDTO.setOrganizationId(organizationId);
    backOfficeUserRoleService.createRole(roleCreationDTO);
  }
}
