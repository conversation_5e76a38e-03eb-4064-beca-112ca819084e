/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.limit.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.LIMIT_VIOLATION;
import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;
import static com.digicore.lucid.common.lib.limit.constant.LimitConstant.*;
import static com.digicore.lucid.common.lib.util.ClientUtil.getPageable;
import static com.digicore.registhentication.util.PageableUtil.SORT_BY_CREATED_DATE;

import com.digicore.api.helper.response.ApiError;
import com.digicore.lucid.backoffice.data.modules.limit.model.BankLimitConfig;
import com.digicore.lucid.backoffice.data.modules.limit.repository.BankLimitConfigRepository;
import com.digicore.lucid.backoffice.data.modules.profile.model.BankProfile;
import com.digicore.lucid.backoffice.data.modules.profile.repository.BankProfileRepository;
import com.digicore.lucid.backoffice.data.modules.util.BankProfileUtil;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.limit.dto.LimitConfigDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.limit.service.LimitConfigService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.util.MoneyUtil;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.validator.enums.Currency;
import jakarta.transaction.Transactional;
import java.math.BigDecimal;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Feb-25(Tue)-2025
 */

@Service
@RequiredArgsConstructor
@Transactional
public class BackOfficeLimitConfigService implements LimitConfigService<LimitConfigDTO> {
  private final BankLimitConfigRepository bankLimitConfigRepository;
  private final BankProfileRepository bankProfileRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  @Override
  public void createLimitConfig(List<LimitConfigDTO> limitConfigDTOS) {
    BankProfile bankProfile =
        BankProfileUtil.getBankProfile(
            RequestContextHolder.get().getOrganizationId(),
            bankProfileRepository,
            exceptionHandler,
            messagePropertyConfig);
    List<BankLimitConfig> limitsToSave =
        limitConfigDTOS.stream()
            .map(
                limitConfigDTO -> {
                  BankLimitConfig bankLimitConfig = new BankLimitConfig();
                  BeanUtilWrapper.copyNonNullProperties(limitConfigDTO, bankLimitConfig);
                  bankLimitConfig.setBankProfile(bankProfile);
                  bankLimitConfig.setUnlimited(false);
                  return bankLimitConfig;
                })
            .toList();
    bankLimitConfigRepository.saveAll(limitsToSave);
  }

  @Override
  public PaginatedResponseDTO<LimitConfigDTO> retrieveLimitConfig(int pageNumber, int pageSize) {
    Pageable pageable = getPageable(pageNumber, pageSize, SORT_BY_CREATED_DATE);
    Page<BankLimitConfig> limitConfigs =
        bankLimitConfigRepository.findAllByBankProfileOrganizationId(
            RequestContextHolder.get().getOrganizationId(), pageable);
    return getLimitPaginatedResponseDTO(limitConfigs);
  }

  @Override
  public LimitConfigDTO retrieveLimitConfig(
      String queryValue, LimitType limitType, Currency currency, boolean defaultLimit) {
    BankLimitConfig bankLimitConfig =
        bankLimitConfigRepository
            .findFirstByBankProfileOrganizationIdAndLimitTypeAndCurrencyAndDefaultLimit(
                queryValue, limitType, currency, defaultLimit)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getLimitMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
    return getLimitConfigDTO(bankLimitConfig);
  }

  @Override
  public void updateLimitConfig(LimitConfigDTO updateRequest) {
    BankLimitConfig existingBankLimitConfig =
        getBankLimitConfig(
            updateRequest.getLimitType(),
            updateRequest.getCurrency(),
            updateRequest.isDefaultLimit());

    List<ApiError> apiErrors = new ArrayList<>();
    validateLimitConfig(updateRequest, apiErrors);

    if (!ClientUtil.nullOrEmpty(apiErrors))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getLimitMessage(LIMIT_VIOLATION),
          HttpStatus.BAD_REQUEST,
          apiErrors);

    BeanUtilWrapper.copyNonNullProperties(updateRequest, existingBankLimitConfig);
    existingBankLimitConfig.setUnlimited(false);
    bankLimitConfigRepository.save(existingBankLimitConfig);
  }

  @Override
  public LimitConfigDTO verifyLimitConfigExist(LimitConfigDTO limitConfigDTO) {
    LimitConfigDTO existingLimitConfig =
        getLimitConfigDTO(
            getBankLimitConfig(
                limitConfigDTO.getLimitType(),
                limitConfigDTO.getCurrency(),
                limitConfigDTO.isDefaultLimit()));
    List<ApiError> apiErrors = new ArrayList<>();
    validateLimitConfig(limitConfigDTO, apiErrors);

    if (!ClientUtil.nullOrEmpty(apiErrors))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getLimitMessage(LIMIT_VIOLATION),
          HttpStatus.BAD_REQUEST,
          apiErrors);

    return existingLimitConfig;
  }

  @Override
  public List<ApiError> validateLimitConfig(LimitConfigDTO updateRequest, boolean unlimited) {
    List<ApiError> apiErrors = new ArrayList<>();
    validateCustomerLimitConfig(updateRequest, apiErrors, unlimited);
    return apiErrors;
  }

  private LimitConfigDTO getLimitConfigDTO(BankLimitConfig bankLimitConfig) {
    LimitConfigDTO dto = new LimitConfigDTO();
    dto.setLimitType(bankLimitConfig.getLimitType());
    dto.setCurrency(bankLimitConfig.getCurrency());
    BeanUtilWrapper.copyNonNullProperties(bankLimitConfig, dto);
    return dto;
  }

  private PaginatedResponseDTO<LimitConfigDTO> getLimitPaginatedResponseDTO(
      Page<BankLimitConfig> transactionLimits) {
    return PaginatedResponseDTO.<LimitConfigDTO>builder()
        .content(transactionLimits.getContent().stream().map(this::getLimitConfigDTO).toList())
        .currentPage(transactionLimits.getNumber() + 1)
        .totalPages(transactionLimits.getTotalPages())
        .totalItems(transactionLimits.getTotalElements())
        .isFirstPage(transactionLimits.isFirst())
        .isLastPage(transactionLimits.isLast())
        .build();
  }

  private void validateCustomerLimitConfig(
      LimitConfigDTO updateRequest, List<ApiError> apiErrors, boolean unlimited) {
    LimitValidationResult limitValidationResult = getLimitValidationResult(updateRequest);
    for (Map.Entry<String, BigDecimal> entry : limitValidationResult.updateConfigs().entrySet()) {
      String key = entry.getKey();

      enforceLimitRule(apiErrors, entry, key, limitValidationResult);

      if (!unlimited
          && entry.getValue().compareTo(limitValidationResult.globalConfigs().get(key)) >= 0) {
        apiErrors.add(new ApiError(getPrettyLimitViolationMessage(key)));
      }
    }
  }

  private void validateLimitConfig(LimitConfigDTO updateRequest, List<ApiError> apiErrors) {
    LimitValidationResult limitValidationResult = getLimitValidationResult(updateRequest);
    for (Map.Entry<String, BigDecimal> entry : limitValidationResult.updateConfigs().entrySet()) {
      String key = entry.getKey();

      enforceLimitRule(apiErrors, entry, key, limitValidationResult);

      // Check global limit only if it's a default config
      if (updateRequest.isDefaultLimit()
          && entry.getValue().compareTo(limitValidationResult.globalConfigs().get(key)) >= 0) {
        apiErrors.add(new ApiError(getPrettyLimitViolationMessage(key)));
      }
    }
  }

  private static void enforceLimitRule(
      List<ApiError> apiErrors,
      Map.Entry<String, BigDecimal> entry,
      String key,
      LimitValidationResult limitValidationResult) {
    // Extract the prefix (Web/Mobile) and construct the cumulative key
    if (key.endsWith(SINGLE_CAP)) {
      String prefix = key.substring(0, key.indexOf(SINGLE_CAP)); // Extracts "Web" or "Mobile"
      String cumulativeKey =
          prefix + CUMULATIVE_CAP; // Constructs "WebCumulativeCap" or "MobileCumulativeCap"

      // Validate: Single Cap should not be greater than Cumulative Cap
      if (limitValidationResult
              .updateConfigs()
              .getOrDefault(cumulativeKey, BigDecimal.ZERO)
              .compareTo(entry.getValue())
          <= 0) {
        apiErrors.add(
            new ApiError(
                "The single "
                    + prefix.toLowerCase()
                    + " limit cannot be higher than the daily "
                    + prefix.toLowerCase()
                    + " limit."));
      }
    }
  }

  private LimitValidationResult getLimitValidationResult(LimitConfigDTO updateRequest) {
    BankLimitConfig globalBankLimitConfig =
        getBankLimitConfig(updateRequest.getLimitType(), updateRequest.getCurrency(), false);
    Map<String, BigDecimal> updateConfigs =
        Map.of(
            WEB_SINGLE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(updateRequest.getMinorWebSingleCap()),
            MOBILE_SINGLE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(updateRequest.getMinorMobileSingleCap()),
            WEB_CUMULATIVE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(updateRequest.getMinorWebCumulativeCap()),
            MOBILE_CUMULATIVE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(updateRequest.getMinorMobileCumulativeCap()));

    Map<String, BigDecimal> globalConfigs =
        Map.of(
            WEB_SINGLE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(globalBankLimitConfig.getMinorWebSingleCap()),
            MOBILE_SINGLE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(globalBankLimitConfig.getMinorMobileSingleCap()),
            WEB_CUMULATIVE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(globalBankLimitConfig.getMinorWebCumulativeCap()),
            MOBILE_CUMULATIVE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(
                globalBankLimitConfig.getMinorMobileCumulativeCap()));
    return new LimitValidationResult(updateConfigs, globalConfigs);
  }

  private record LimitValidationResult(
      Map<String, BigDecimal> updateConfigs, Map<String, BigDecimal> globalConfigs) {}

  private BankLimitConfig getBankLimitConfig(
      LimitType limitType, Currency currency, boolean defaultLimit) {
    return bankLimitConfigRepository
        .findFirstByBankProfileOrganizationIdAndLimitTypeAndCurrencyAndDefaultLimit(
            RequestContextHolder.get().getOrganizationId(), limitType, currency, defaultLimit)
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getLimitMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }
}
