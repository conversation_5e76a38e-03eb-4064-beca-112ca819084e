/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.location.branch.dto;

import com.digicore.lucid.backoffice.data.modules.location.dto.OperatingHours;
import com.digicore.lucid.common.lib.constant.enums.DayOfTheWeek;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.math.BigDecimal;
import java.util.Map;
import lombok.*;

/*
 * <AUTHOR>
 * @createdOn 19/03/2025
 */

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class BranchLocationDTO {
  private String branchName;
  private String branchCode;
  private String physicalAddress;
  private String state;
  private String city;
  private BigDecimal latitude;
  private BigDecimal longitude;
  private Map<DayOfTheWeek, OperatingHours> operatingHours;
}
