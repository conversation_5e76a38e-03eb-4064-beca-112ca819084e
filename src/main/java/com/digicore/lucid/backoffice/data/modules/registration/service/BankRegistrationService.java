/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.backoffice.data.modules.registration.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.DUPLICATE;
import static com.digicore.lucid.common.lib.constant.message.MessageConstant.INVALID;
import static com.digicore.lucid.common.lib.constant.message.MessagePlaceHolderConstant.NAME;
import static com.digicore.lucid.common.lib.util.RegistrationUtil.getUserProfileDTO;

import com.digicore.lucid.backoffice.data.modules.profile.model.BankProfile;
import com.digicore.lucid.backoffice.data.modules.profile.repository.BankProfileRepository;
import com.digicore.lucid.common.lib.activation.dto.FileUploadedDTO;
import com.digicore.lucid.common.lib.profile.dto.*;
import com.digicore.lucid.common.lib.profile.service.PreferenceService;
import com.digicore.lucid.common.lib.properties.FeaturePropertyConfig;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.UserRegistrationDTO;
import com.digicore.lucid.common.lib.registration.dto.backoffice.BankRegistrationDTO;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.common.lib.util.CsvUtil;
import com.digicore.lucid.common.lib.util.FileUtil;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.enums.Status;
import com.digicore.registhentication.registration.services.RegistrationService;
import com.digicore.registhentication.validator.enums.Currency;
import jakarta.transaction.Transactional;
import java.io.ByteArrayInputStream;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-04(Tue)-2025
 */

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class BankRegistrationService
    implements RegistrationService<UserProfileDTO, BankRegistrationDTO> {
  private final BankProfileRepository bankProfileRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final RegistrationService<UserProfileDTO, UserRegistrationDTO>
      backOfficeUserRegistrationService;
  private final PreferenceService<BankPreferenceDTO> bankPreferenceService;
  private final PreferenceService<List<BankAccountOfficerDTO>> bankAccountOfficerPreferenceService;
  private final FeaturePropertyConfig featurePropertyConfig;
  private final FileUtil fileUtil;
  private final CsvUtil csvUtil;

  @Override
  public UserProfileDTO createProfile(BankRegistrationDTO registrationRequest) {
    if (profileCheck(
        registrationRequest.getOrganizationName(),
        registrationRequest.getOrganizationEmail(),
        registrationRequest.getOrganizationPhoneNumber()))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig
              .getClientMessage(DUPLICATE)
              .replace(NAME, registrationRequest.getOrganizationName()),
          HttpStatus.BAD_REQUEST);
    BankProfile bankProfile = new BankProfile();
    BeanUtilWrapper.copyNonNullProperties(registrationRequest, bankProfile);
    bankProfile.setStatus(Status.ACTIVE);
    bankProfileRepository.save(bankProfile);
    List<UserRegistrationDTO> registrationRequests =
        registrationRequest.getBackOfficeUsers().stream()
            .map(
                x -> {
                  UserRegistrationDTO registration = new UserRegistrationDTO();
                  BeanUtilWrapper.copyNonNullProperties(x, registration);
                  registration.setOrganizationId(bankProfile.getOrganizationId());
                  registration.setSystemInitiated(true);
                  return registration;
                })
            .toList();
    backOfficeUserRegistrationService.createProfile(registrationRequests);
    createBankPreference(bankProfile, registrationRequest);
    setAccountOfficers(bankProfile, registrationRequest);
    return getUserProfileDTO(registrationRequests.getFirst());
  }

  private void createBankPreference(
      BankProfile bankProfile, BankRegistrationDTO registrationRequest) {
    BankPreferenceDTO bankPreferenceDTO = new BankPreferenceDTO();
    bankPreferenceDTO.setOrganizationId(bankProfile.getOrganizationId());
    BankPreferenceDTO.Theme theme = new BankPreferenceDTO.Theme();
    List<BankPreferenceDTO.AccountType> accountTypes =
        getAccountTypes(bankProfile, registrationRequest, bankPreferenceDTO);
    bankPreferenceDTO.setAccountTypes(accountTypes);
    theme.setBankName(bankProfile.getOrganizationName());
    theme.setLogo(featurePropertyConfig.getLogo());
    theme.setPrimaryColor(featurePropertyConfig.getPrimaryColor());
    theme.setSecondaryColor(featurePropertyConfig.getSecondaryColor());
    bankPreferenceDTO.setTheme(theme);
    bankPreferenceService.createPreference(bankPreferenceDTO, bankProfile.getOrganizationId());
  }

  private List<BankPreferenceDTO.AccountType> getAccountTypes(
      BankProfile bankProfile,
      BankRegistrationDTO registrationRequest,
      BankPreferenceDTO bankPreferenceDTO) {
    if (registrationRequest.getProductCodes() == null) return null;
    List<BankPreferenceDTO.AccountType> accountTypes;

    try {
      FileUploadedDTO fileUploadedDTO = new FileUploadedDTO();
      fileUploadedDTO.setFilePath(registrationRequest.getProductCodes());
      byte[] fileBytes = fileUtil.getSavedFile(fileUploadedDTO);

      List<BankProductCodeDTO> bankProductCodeDTOS =
          (List<BankProductCodeDTO>)
              csvUtil.parseCsv(new ByteArrayInputStream(fileBytes), BankProductCodeDTO.class);

      // Transform BankProductCodeDTO to BankPreferenceDTO.AccountType
      accountTypes =
          bankProductCodeDTOS.stream()
              .map(
                  productCode -> {
                    BankPreferenceDTO.AccountType accountType = new BankPreferenceDTO.AccountType();
                    accountType.setCode(productCode.getCode());
                    accountType.setName(productCode.getName());
                    accountType.setCurrencies(List.of(Currency.NGN.toString()));
                    return accountType;
                  })
              .toList();
      bankPreferenceDTO.setAccountTypes(accountTypes);

    } catch (Exception e) {
      log.error("unable to read product codes for bank {}", bankProfile.getSubDomainName(), e);
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getDocumentMessage(INVALID), HttpStatus.INTERNAL_SERVER_ERROR);
    }
    return accountTypes;
  }

  private void setAccountOfficers(
      BankProfile bankProfile, BankRegistrationDTO registrationRequest) {
    if (registrationRequest.getAccountOfficers() == null) return;

    try {
      FileUploadedDTO fileUploadedDTO = new FileUploadedDTO();
      fileUploadedDTO.setFilePath(registrationRequest.getAccountOfficers());
      byte[] fileBytes = fileUtil.getSavedFile(fileUploadedDTO);

      List<BankAccountOfficerDTO> accountOfficerDTOList =
          (List<BankAccountOfficerDTO>)
              csvUtil.parseCsv(new ByteArrayInputStream(fileBytes), BankAccountOfficerDTO.class);

      bankAccountOfficerPreferenceService.createPreference(
          accountOfficerDTOList, bankProfile.getOrganizationId());

    } catch (Exception e) {
      log.error(
          "unable to read account officer codes for bank {}", bankProfile.getSubDomainName(), e);
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getDocumentMessage(INVALID), HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Override
  public boolean profileCheck(String email, String companyName, String phoneNumber) {
    return bankProfileRepository
        .existsByOrganizationNameOrOrganizationEmailOrOrganizationPhoneNumberAndIsDeletedFalse(
            companyName, email, phoneNumber);
  }

  public static BankProfileDTO getBankProfileDTO(BankRegistrationDTO registrationRequest) {
    BankProfileDTO profileDto = new BankProfileDTO();
    BeanUtilWrapper.copyNonNullProperties(registrationRequest, profileDto);
    return profileDto;
  }
}
