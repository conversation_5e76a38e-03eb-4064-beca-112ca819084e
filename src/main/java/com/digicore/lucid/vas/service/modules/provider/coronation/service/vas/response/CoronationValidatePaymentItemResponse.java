/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.provider.coronation.service.vas.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @createdOn Jun-30(Mon)-2025
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoronationValidatePaymentItemResponse {
  private boolean flag;
  private String code;
  private String message;
  private ValidationData data;

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class ValidationData {
    private String customerId;
    private String customerName;
  }
}
