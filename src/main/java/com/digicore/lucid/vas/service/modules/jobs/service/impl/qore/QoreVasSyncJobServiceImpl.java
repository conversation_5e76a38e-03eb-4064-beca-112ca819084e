/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.jobs.service.impl.qore;

import com.digicore.lucid.vas.service.modules.jobs.service.VasSyncJobService;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @createdOn Jul-03(Thu)-2025
 */
@RequiredArgsConstructor
@Service
@ConditionalOnProperty(name = "lucid.biller.provider", havingValue = "qore")
@Profile("!enterprise")
public class QoreVasSyncJobServiceImpl implements VasSyncJobService {

  @Override
  public void syncBillerService() {}
}
