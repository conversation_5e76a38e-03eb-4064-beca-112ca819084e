/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.provider.coronation.service.vas.service;

import static com.digicore.lucid.integration.lib.modules.service.billspayment.request.BillsPaymentServiceType.FETCH_BILLER_CATEGORIES;

import com.digicore.lucid.customer.vas.data.lib.modules.category.dto.BillerCategoryApiResponse;
import com.digicore.lucid.customer.vas.data.lib.modules.category.model.VasBillerCategory;
import com.digicore.lucid.customer.vas.data.lib.modules.category.repository.VasBillerCategoryRepository;
import com.digicore.lucid.vas.service.modules.provider.vas.provider.BillerProviderSelector;
import com.digicore.lucid.vas.service.modules.service.billspayment.VasBillsPaymentService;
import com.digicore.registhentication.registration.enums.Status;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Ucheagwu
 * @createdOn Jun-30(Mon)-2025
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Profile("coronation-provider")
public class CoronationFetchBillerCategoryService
    implements VasBillsPaymentService<Object, Object> {
  private final BillerProviderSelector billerProviderSelector;
  private final VasBillerCategoryRepository vasBillerCategoryRepository;

  @Override
  public Object process(Object request, String token) { // categories
    List<VasBillerCategory> dbCategories =
        vasBillerCategoryRepository.findAllByVasProviderAndIsDeletedFalseAndStatus(
            getProviderName(), Status.ACTIVE);
    if (dbCategories.isEmpty()) {
      return new ArrayList<>();
    }

    return map(dbCategories);
  }

  public List<BillerCategoryApiResponse> map(List<VasBillerCategory> dbCategories) {
    return dbCategories.stream()
        .map(
            categoryData ->
                new BillerCategoryApiResponse(
                    categoryData.getCategoryId(),
                    categoryData.getName(),
                    getProviderName(),
                    categoryData.getDescription()))
        .toList();
  }

  @Override
  public String getServiceKey() {
    return billerProviderSelector.getProvider().getProviderName().concat(FETCH_BILLER_CATEGORIES);
  }

  private String getProviderName() {
    return billerProviderSelector.getProvider().getProviderName();
  }
}
