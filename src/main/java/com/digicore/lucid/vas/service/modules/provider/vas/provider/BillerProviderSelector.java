/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.provider.vas.provider;

import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createdOn Jun-30(Mon)-2025
 */
@Slf4j
@Component
public class BillerProviderSelector {
  private final BillerProvider activeProvider;

  public BillerProviderSelector(List<BillerProvider> providers) {
    if (providers.size() != 1) {
      String loaded =
          providers.stream().map(BillerProvider::getProviderName).collect(Collectors.joining(", "));
      log.error("Expected exactly 1 active BillerProvider, found: {}", loaded);
      throw new IllegalStateException("Expected exactly 1 active BillerProvider, found: " + loaded);
    }
    this.activeProvider = providers.getFirst();
  }

  public BillerProvider getProvider() {
    return activeProvider;
  }
}
