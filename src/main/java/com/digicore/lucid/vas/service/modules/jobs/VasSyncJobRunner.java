/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.jobs;

import com.digicore.lucid.vas.service.modules.jobs.service.VasSyncJobService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @createdOn Jul-03(Thu)-2025
 */
@Service
public class VasSyncJobRunner implements CommandLineRunner {
  private final VasSyncJobService vasSyncJobService;

  public VasSyncJobRunner(VasSyncJobService vasSyncJobService) {
    this.vasSyncJobService = vasSyncJobService;
  }

  @Scheduled(cron = "0 0 0 * * *")
  public void run() {
    vasSyncJobService.syncBillerService();
  }

  @Override
  public void run(String... args) throws Exception {
    run();
  }
}
