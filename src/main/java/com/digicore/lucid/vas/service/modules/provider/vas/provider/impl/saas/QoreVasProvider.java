/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.provider.vas.provider.impl.saas;

import com.digicore.lucid.vas.service.modules.provider.vas.provider.BillerProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @createdOn Jun-30(Mon)-2025
 */
@Service
@ConditionalOnProperty(name = "lucid.biller.provider", havingValue = "qore")
@Profile("!enterprise")
public class QoreVasProvider implements BillerProvider {
  public String getProviderName() {
    return "qore";
  }
}
