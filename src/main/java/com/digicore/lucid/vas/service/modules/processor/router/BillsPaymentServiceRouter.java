/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.processor.router;

import com.digicore.lucid.integration.lib.modules.config.properties.SecurityPropertyConfig;
import com.digicore.lucid.vas.service.modules.processor.locator.BillsPaymentServiceLocator;
import com.digicore.lucid.vas.service.modules.service.billspayment.VasBillsPaymentService;
import com.digicore.lucid.vas.service.modules.service.token.TokenHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/*
 * <AUTHOR> <PERSON>
 * @createdOn Apr-14(Mon)-2025
 */

@Component
@Slf4j
@RequiredArgsConstructor
public class BillsPaymentServiceRouter {
  private final BillsPaymentServiceLocator serviceLocator;
  private final SecurityPropertyConfig securityPropertyConfig;

  public Object process(Object request, String token, String serviceRequired) {
    log.info("<<< request : {} >>>", request);
    log.info("<<< token : {} >>>", token);
    log.info("<<< serviceRequired : {} >>>", serviceRequired);

    VasBillsPaymentService<Object, Object> service = serviceLocator.getService(serviceRequired);
    token = TokenHelper.of(token, securityPropertyConfig.getSystemKey()).getValue();

    if (StringUtils.isBlank(token)) {
      token = service.provideToken();
    }

    return service.process(request, token);
  }
}
