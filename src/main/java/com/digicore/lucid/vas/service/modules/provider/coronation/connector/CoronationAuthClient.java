/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.provider.coronation.connector;

import com.digicore.lucid.vas.service.modules.provider.coronation.service.auth.request.CoronationLoginRequest;
import com.digicore.lucid.vas.service.modules.provider.coronation.service.auth.response.CoronationLoginResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @createdOn May-12(Mon)-2025
 */
@Profile("coronation-provider")
@FeignClient(name = "coronationAuthClient", url = "${lucid.vas.provider.coronation.baseUrl}")
public interface CoronationAuthClient {

  @PostMapping("${lucid.vas.provider.coronation.authUrl}")
  CoronationLoginResponse login(@RequestBody CoronationLoginRequest coronationLoginRequest);
}
