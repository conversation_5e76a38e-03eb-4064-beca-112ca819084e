/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.provider.coronation.connector.config;

import com.digicore.lucid.vas.service.modules.provider.coronation.service.auth.service.CoronationTokenProvider;
import feign.RequestInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR>
 * @createdOn May-12(Mon)-2025
 */
@Configuration
@Profile("coronation-provider")
@RequiredArgsConstructor
public class CoronationFeignClientConfig {

  private final CoronationTokenProvider coronationTokenProvider;

  @Bean
  public RequestInterceptor bearerTokenInterceptor() {
    return requestTemplate ->
        requestTemplate.header("Authorization", "Bearer " + coronationTokenProvider.provideToken());
  }
}
