/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.service.token.impl;

import com.digicore.lucid.integration.lib.modules.util.AESUtil;
import com.digicore.lucid.vas.service.modules.service.token.Token;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @createdOn May-12(Mon)-2025
 */
public class DecryptedToken implements Token {
  private final String decryptedToken;

  public DecryptedToken(String encryptedToken, String systemKey) {
    if (StringUtils.isBlank(encryptedToken)) {
      throw new IllegalArgumentException("Encrypted token cannot be blank");
    }
    this.decryptedToken = AESUtil.decrypt(encryptedToken, systemKey);
  }

  @Override
  public String getValue() {
    return decryptedToken;
  }
}
