/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

/**
 * <AUTHOR>
 * @createdOn Jun-30(Mon)-2025
 */
public class ProviderUtil {
  private static final ObjectMapper objectMapper = new ObjectMapper();

  public static ObjectMapper getObjectMapper() {
    objectMapper.registerModule(new JavaTimeModule());
    return objectMapper;
  }
}
