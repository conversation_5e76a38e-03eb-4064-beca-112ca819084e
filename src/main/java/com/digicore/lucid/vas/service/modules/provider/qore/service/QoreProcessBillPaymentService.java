/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.provider.qore.service;

import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;
import static com.digicore.lucid.integration.lib.modules.service.billspayment.request.BillsPaymentServiceType.PROCESS_BILL_PAYMENT;
import static com.digicore.lucid.vas.service.modules.provider.qore.constant.QoreConstant.PROVIDER_NAME;

import com.digicore.lucid.integration.lib.modules.VasBaseResponse;
import com.digicore.lucid.integration.lib.modules.service.billspayment.response.ProcessBillPaymentResponse;
import com.digicore.lucid.vas.service.modules.provider.qore.config.QorePropertyConfig;
import com.digicore.lucid.vas.service.modules.provider.qore.connector.QoreApiService;
import com.digicore.lucid.vas.service.modules.provider.qore.service.request.QoreBillPaymentRequest;
import com.digicore.lucid.vas.service.modules.provider.qore.service.response.QoreProcessBillPaymentResponse;
import com.digicore.lucid.vas.service.modules.service.billspayment.VasBillsPaymentService;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class QoreProcessBillPaymentService implements VasBillsPaymentService<Object, Object> {
  private final QoreApiService qoreApiService;
  private final QorePropertyConfig qorePropertyConfig;

  @Override
  public Object process(Object request, String token) {
    try {
      log.info("Incoming request object: {}", request);
      QoreBillPaymentRequest requestBody =
          getObjectMapper().convertValue(request, QoreBillPaymentRequest.class);

      requestBody.setAccountNumber(qorePropertyConfig.getAccountNumber());
      requestBody.setToken(token);
      Object responseBody = qoreApiService.processBillPayment(requestBody).getBody();
      log.info("Data gotten from qore:{} ", responseBody);

      if (responseBody == null) {
        return VasBaseResponse.builder()
            .responseStatus(VasBaseResponse.ResponseStatus.FAILED)
            .provider(PROVIDER_NAME)
            .narration("Null response received")
            .serviceRequired(PROCESS_BILL_PAYMENT)
            .build();
      }

      QoreProcessBillPaymentResponse apiResponseJson =
          getObjectMapper().convertValue(responseBody, QoreProcessBillPaymentResponse.class);

      if (!apiResponseJson.getIsSuccessful()) {
        return VasBaseResponse.builder()
            .responseStatus(VasBaseResponse.ResponseStatus.FAILED)
            .provider(PROVIDER_NAME)
            .narration(apiResponseJson.getResponseMessage())
            .serviceRequired(PROCESS_BILL_PAYMENT)
            .build();
      }

      log.info(
          "Qore ProcessBillPayment Response: {}",
          getObjectMapper().writeValueAsString(apiResponseJson));

      return ProcessBillPaymentResponse.builder()
          .responseStatus(VasBaseResponse.ResponseStatus.COMPLETED)
          .serviceRequired(PROCESS_BILL_PAYMENT)
          .narration("bill payment processed successfully")
          .provider(PROVIDER_NAME)
          .qoreUniqueId(apiResponseJson.getUniqueIdentifier())
          .status(apiResponseJson.getStatus())
          .transactionStatus(apiResponseJson.getStatus())
          .responseCode(apiResponseJson.getResponseCode())
          .minorAmount(new BigDecimal(requestBody.getAmount()))
          .billerId(requestBody.getBillerId())
          .paymentItemId(requestBody.getPaymentItemId())
          .categoryId(requestBody.getBillerCategoryId())
          .dateCreated(LocalDateTime.now().toString())
          .build();

    } catch (Exception e) {
      log.error("Error during processing fetch customer detail", e);
      return VasBaseResponse.builder()
          .responseStatus(VasBaseResponse.ResponseStatus.FAILED)
          .narration(e.getLocalizedMessage())
          .provider(PROVIDER_NAME)
          .serviceRequired(PROCESS_BILL_PAYMENT)
          .build();
    }
  }

  @Override
  public String getServiceKey() {
    return PROVIDER_NAME.concat(PROCESS_BILL_PAYMENT);
  }
}
