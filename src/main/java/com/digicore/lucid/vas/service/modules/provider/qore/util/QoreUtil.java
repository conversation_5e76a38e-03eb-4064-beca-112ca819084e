/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.provider.qore.util;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class QoreUtil {
  public static String convertDate(String date) {
    DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
    LocalDate localDate = LocalDate.parse(date, inputFormatter);

    ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.of("UTC"));
    DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    return zonedDateTime.format(outputFormatter);
  }

  public static String revertConversionDate(String date) {
    if (date != null && !date.isBlank()) {
      DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
      LocalDate localDate = LocalDate.parse(date, inputFormatter);

      DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
      return localDate.format(outputFormatter);
    }
    return "";
  }
}
