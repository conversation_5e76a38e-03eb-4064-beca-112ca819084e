/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.provider.qore.service.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QoreProcessBillPaymentResponse {
  @JsonProperty("Status")
  private String status;

  @JsonProperty("StatusDescription")
  private String statusDescription;

  @JsonProperty("ReferenceID")
  private Long referenceId;

  @JsonProperty("UniqueIdentifier")
  private String uniqueIdentifier;

  @JsonProperty("IsSuccessFul")
  private Boolean isSuccessful;

  @JsonProperty("ResponseMessage")
  private String responseMessage;

  @JsonProperty("ResponseCode")
  private String responseCode;

  @JsonProperty("Reference")
  private String reference;

  @JsonProperty("SessionID")
  private String sessionId;

  @JsonProperty("RequestStatus")
  private Boolean requestStatus;

  @JsonProperty("ResponseDescription")
  private String responseDescription;

  @JsonProperty("ResponseStatus")
  private String responseStatus;
}
