/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.provider.coronation.service.auth.service;

import static com.digicore.lucid.vas.service.modules.util.ProviderUtil.getObjectMapper;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.lucid.vas.service.modules.provider.coronation.config.CoronationPropertyConfig;
import com.digicore.lucid.vas.service.modules.provider.coronation.connector.CoronationAuthClient;
import com.digicore.lucid.vas.service.modules.provider.coronation.service.auth.request.CoronationLoginRequest;
import com.digicore.lucid.vas.service.modules.provider.coronation.service.auth.response.CoronationLoginResponse;
import java.time.Instant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> Ucheagwu
 * @createdOn May-12(Mon)-2025
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Profile("coronation-provider")
public class CoronationTokenProvider {
  private final CoronationAuthClient coronationAuthClient;
  private final CoronationPropertyConfig coronationPropertyConfig;
  private volatile String token;
  private volatile long tokenCreationTime;
  private static final long THREE_MINUTES = 3 * 60;
  private final Object lock = new Object();

  private boolean isTokenExpired() {
    if (token == null) {
      return true;
    }
    long now = Instant.now().getEpochSecond();
    return (now - tokenCreationTime)
        >= (Long.parseLong(coronationPropertyConfig.getTokenExpiry()) - THREE_MINUTES);
  }

  private String generateNewToken() {
    try {
      CoronationLoginRequest coronationLoginRequest = new CoronationLoginRequest();
      coronationLoginRequest.setUsername(coronationPropertyConfig.getUsername());
      coronationLoginRequest.setPassword(coronationPropertyConfig.getPassword());

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);

      HttpEntity<?> httpEntityRequest = new HttpEntity<>(coronationLoginRequest, headers);
      String url = coronationPropertyConfig.getBaseUrl() + coronationPropertyConfig.getAuthUrl();

      CoronationLoginResponse coronationLoginResponse =
          new RestTemplate()
              .exchange(url, HttpMethod.POST, httpEntityRequest, CoronationLoginResponse.class)
              .getBody();

      //      CoronationLoginResponse coronationLoginResponse =
      //          coronationAuthClient.login(coronationLoginRequest);
      log.info(
          "<<< auth response : {} >>>",
          getObjectMapper().writeValueAsString(coronationLoginResponse));

      if (coronationLoginResponse == null || coronationLoginResponse.getData() == null) {
        log.error("response does not have a valid token");
        throw new IllegalArgumentException("response does not have a valid token");
      }
      return coronationLoginResponse.getData().getAccessToken();
    } catch (Exception e) {
      log.error("auth error : {}", e.getMessage());
      throw new ZeusRuntimeException(e.getMessage());
    }
  }

  public String provideToken() {
    if (isTokenExpired()) {
      synchronized (lock) {
        token = generateNewToken();
        tokenCreationTime = Instant.now().getEpochSecond();
      }
    }
    return token;
  }
}
