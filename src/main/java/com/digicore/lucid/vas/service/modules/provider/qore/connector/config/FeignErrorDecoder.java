/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.provider.qore.connector.config; /// *
// * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
// * Unauthorized use or distribution is strictly prohibited.
// * For details, see the LICENSE file.
// */
//
// package com.digicore.lucid.cba.service.modules.provider.qore.connector.config;
//
// import com.digicore.api.helper.exception.ZeusRuntimeException;
// import com.digicore.api.helper.response.ApiResponseJson;
// import feign.Response;
// import feign.codec.ErrorDecoder;
// import java.io.IOException;
// import org.springframework.context.annotation.Configuration;
// import org.springframework.http.HttpStatus;
//
// import static com.digicore.lucid.cba.service.modules.util.ProviderUtil.getObjectMapper;
//
/// *
// * <AUTHOR>
// * @createdOn Feb-04(Tue)-2025
// */
//
// @Configuration
// public class FeignErrorDecoder implements ErrorDecoder {
//  @Override
//  public Exception decode(String s, Response response) {
//    // Ignore successful responses (status 200)
//    if (response.status() >= 200 && response.status() < 300) {
//      return null;
//    }
//    try {
//      ApiResponseJson<?> apiResponseJson = getObjectMapper()
//              .readValue(response.body().asInputStream(), ApiResponseJson.class);
//      return new ZeusRuntimeException(
//          apiResponseJson.getMessage(),
//          HttpStatus.valueOf(response.status()),
//          apiResponseJson.getErrors());
//    } catch (IOException e) {
//      throw new RuntimeException(e);
//    }
//  }
// }
