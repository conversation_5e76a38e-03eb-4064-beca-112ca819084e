/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.vas.service.modules.jobs.service.impl.coronation.helper;

import com.digicore.lucid.customer.vas.data.lib.modules.category.model.VasBillerCategory;
import com.digicore.lucid.customer.vas.data.lib.modules.category.repository.VasBillerCategoryRepository;
import com.digicore.lucid.vas.service.modules.provider.coronation.config.CoronationPropertyConfig;
import com.digicore.lucid.vas.service.modules.provider.coronation.service.auth.service.CoronationTokenProvider;
import com.digicore.lucid.vas.service.modules.provider.coronation.service.vas.response.CoronationBillerCategoryResponse;
import com.digicore.lucid.vas.service.modules.provider.vas.provider.BillerProviderSelector;
import com.digicore.registhentication.registration.enums.Status;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> Ucheagwu
 * @createdOn Jul-03(Thu)-2025
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Profile("coronation-provider")
public class CoronationFetchBillerCategoryHelper {
  private final CoronationPropertyConfig coronationPropertyConfig;
  private final CoronationTokenProvider coronationTokenProvider;
  private final VasBillerCategoryRepository vasBillerCategoryRepository;
  private final BillerProviderSelector billerProviderSelector;

  public List<CoronationBillerCategoryResponse.Category> fetchBillerCategory() {
    CoronationBillerCategoryResponse billerCategoryResponse;
    try {
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      headers.set("Authorization", "Bearer " + coronationTokenProvider.provideToken());

      HttpEntity<?> httpEntityRequest = new HttpEntity<>(headers);
      String url =
          coronationPropertyConfig.getBaseUrl()
              + coronationPropertyConfig.getFetchBillerCategories();

      RestTemplate restTemplate = new RestTemplate();
      String body =
          restTemplate.exchange(url, HttpMethod.GET, httpEntityRequest, String.class).getBody();
      log.info("<<< response : {} >>>", body);
      billerCategoryResponse =
          restTemplate
              .exchange(
                  url, HttpMethod.GET, httpEntityRequest, CoronationBillerCategoryResponse.class)
              .getBody();
      log.info("<<< response : {} >>>", billerCategoryResponse);
      if (billerCategoryResponse == null
          || !billerCategoryResponse.isFlag()
          || billerCategoryResponse.getData() == null
          || billerCategoryResponse.getData().isEmpty()) {
        return new ArrayList<>();
      }

      return billerCategoryResponse.getData();
    } catch (Exception e) {
      log.error("Coronation, error parsing biller categories response: {}", e.getMessage());
    }
    return new ArrayList<>();
  }

  public List<VasBillerCategory> storeVasCategory(
      List<CoronationBillerCategoryResponse.Category> billerCategoryResponse) {
    try {
      String providerName = getProviderName();
      if (billerCategoryResponse == null || billerCategoryResponse.isEmpty()) {
        return new ArrayList<>();
      }

      // Fetch existing categories for the current provider
      List<VasBillerCategory> existingCategories =
          vasBillerCategoryRepository.findAllByVasProviderAndIsDeletedFalse(providerName);

      Map<String, VasBillerCategory> existingMap =
          existingCategories.stream()
              .collect(Collectors.toMap(VasBillerCategory::getName, Function.identity()));

      Set<String> incomingNames =
          billerCategoryResponse.stream()
              .map(CoronationBillerCategoryResponse.Category::getValue)
              .collect(Collectors.toSet());

      List<VasBillerCategory> toSave = new ArrayList<>();

      // Prepare new or updated categories
      for (CoronationBillerCategoryResponse.Category response : billerCategoryResponse) {
        VasBillerCategory existing = existingMap.get(response.getValue());

        if (existing != null) {
          if (existing.getStatus() != Status.ACTIVE
              || !Objects.equals(existing.getDescription(), response.getLabel())) {
            existing.setStatus(Status.ACTIVE);
            existing.setDescription(response.getLabel());
            toSave.add(existing);
          }
        } else {
          VasBillerCategory newCategory = new VasBillerCategory();
          newCategory.setName(response.getValue());
          newCategory.setDescription(response.getLabel());
          newCategory.setVasProvider(providerName);
          newCategory.setStatus(Status.ACTIVE);
          toSave.add(newCategory);
        }
      }

      // Prepare categories to deactivate
      for (VasBillerCategory oldCategory : existingCategories) {
        if (!incomingNames.contains(oldCategory.getName())
            && oldCategory.getStatus() == Status.ACTIVE) {
          oldCategory.setStatus(Status.INACTIVE);
          toSave.add(oldCategory);
        }
      }

      // Batch persist all changes if any
      if (!toSave.isEmpty()) {
        vasBillerCategoryRepository.saveAll(toSave);
      }

      // Return all active categories for the provider (fresh list)
      return vasBillerCategoryRepository.findAllByVasProviderAndIsDeletedFalseAndStatus(
          providerName, Status.ACTIVE);

    } catch (Exception e) {
      log.error("Error while storing vas category", e);
      return new ArrayList<>();
    }
  }

  private String getProviderName() {
    return billerProviderSelector.getProvider().getProviderName();
  }
}
