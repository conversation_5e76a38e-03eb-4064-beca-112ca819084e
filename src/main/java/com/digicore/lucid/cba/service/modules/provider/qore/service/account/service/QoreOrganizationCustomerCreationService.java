/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.qore.service.account.service;

import static com.digicore.lucid.cba.service.modules.provider.qore.constant.QoreConstant.PROVIDER_NAME;
import static com.digicore.lucid.cba.service.modules.util.ProviderUtil.getObjectMapper;
import static com.digicore.lucid.integration.lib.modules.service.account.request.AccountServiceType.CUSTOMER_CREATION;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.lucid.cba.service.modules.provider.qore.connector.QoreApiService;
import com.digicore.lucid.cba.service.modules.provider.qore.service.account.request.QoreOrganizationCustomerCreateRequest;
import com.digicore.lucid.cba.service.modules.provider.qore.service.account.response.QoreOrganizationCustomerCreateResponse;
import com.digicore.lucid.cba.service.modules.provider.qore.util.QoreUtil;
import com.digicore.lucid.cba.service.modules.service.account.CbaAccountService;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.account.request.OrganizationCustomerCreateRequest;
import com.digicore.lucid.integration.lib.modules.service.account.response.OrganizationCustomerCreateResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-18(Tue)-2025
 */

@RequiredArgsConstructor
@Service
@Slf4j
public class QoreOrganizationCustomerCreationService implements CbaAccountService<Object, Object> {
  private final QoreApiService qoreApiService;

  @Override
  public Object process(Object request, String token) {
    log.info("create customer");
    OrganizationCustomerCreateRequest customerCreateRequest =
        getObjectMapper().convertValue(request, OrganizationCustomerCreateRequest.class);
    QoreOrganizationCustomerCreateRequest qoreOrganizationCustomerCreateRequest =
        QoreOrganizationCustomerCreateRequest.builder()
            .lastName(customerCreateRequest.getLastName())
            .firstName(customerCreateRequest.getFirstName())
            .otherNames("P")
            .city(customerCreateRequest.getCity())
            .address(customerCreateRequest.getAddress())
            .gender(customerCreateRequest.getGender())
            .dateOfBirth(QoreUtil.convertDate(customerCreateRequest.getDateOfBirth()))
            .phoneNo(customerCreateRequest.getPhoneNo())
            .placeOfBirth(customerCreateRequest.getPlaceOfBirth())
            .nationalIdentityNo(customerCreateRequest.getNationalIdentityNo())
            .bankVerificationNumber(customerCreateRequest.getBankVerificationNumber())
            .email(customerCreateRequest.getEmail())
            .accountOfficerCode(customerCreateRequest.getAccountOfficerCode())
            .build();

    QoreOrganizationCustomerCreateResponse qoreOrganizationCustomerCreateResponse =
        qoreApiService
            .createOrganizationCustomer(token, qoreOrganizationCustomerCreateRequest)
            .getBody();
    Objects.requireNonNull(qoreOrganizationCustomerCreateResponse, "Response cannot be null");
    try {
      log.info(
          "<<< response : {} >>>",
          getObjectMapper().writeValueAsString(qoreOrganizationCustomerCreateResponse));
    } catch (JsonProcessingException e) {
      log.info("error : {}", e.getMessage());
      throw new ZeusRuntimeException(e.getMessage());
    }
    if (qoreOrganizationCustomerCreateResponse.getCustomerID() != null
        && !qoreOrganizationCustomerCreateResponse.getCustomerID().isBlank())
      return OrganizationCustomerCreateResponse.builder()
          .responseStatus(CbaProvider.ResponseStatus.COMPLETED)
          .narration(qoreOrganizationCustomerCreateResponse.getMessage())
          .customerId(qoreOrganizationCustomerCreateResponse.getCustomerID())
          .provider(PROVIDER_NAME)
          .serviceRequired(CUSTOMER_CREATION)
          .build();
    else
      return CbaProvider.builder()
          .responseStatus(CbaProvider.ResponseStatus.FAILED)
          .narration(qoreOrganizationCustomerCreateResponse.getMessage())
          .provider(PROVIDER_NAME)
          .serviceRequired(CUSTOMER_CREATION)
          .build();
  }

  @Override
  public String getServiceKey() {
    return PROVIDER_NAME.concat(CUSTOMER_CREATION);
  }
}
