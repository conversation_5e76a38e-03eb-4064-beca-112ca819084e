/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.qore.service.account.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn 27/02/2025
 */

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class QoreTransactionData {
  @JsonProperty("Id")
  private Long id;

  @JsonProperty("CurrentDate")
  private LocalDateTime currentDate;

  @JsonProperty("IsReversed")
  private boolean reversed;

  @JsonProperty("ReversalReferenceNo")
  private String reversalReferenceNo;

  @JsonProperty("WithdrawableAmount")
  private BigDecimal withdrawableAmount;

  @JsonProperty("UniqueIdentifier")
  private String uniqueIdentifier;

  @JsonProperty("InstrumentNo")
  private String instrumentNo;

  @JsonProperty("TransactionDate")
  private LocalDateTime transactionDate;

  @JsonProperty("TransactionDateString")
  private String transactionDateString;

  @JsonProperty("ReferenceID")
  private String referenceID;

  @JsonProperty("Narration")
  private String narration;

  @JsonProperty("Amount")
  private String amount;

  @JsonProperty("OpeningBalance")
  private String openingBalance;

  @JsonProperty("Balance")
  private String balance;

  @JsonProperty("PostingType")
  private String postingType;

  @JsonProperty("Debit")
  private String debit;

  @JsonProperty("Credit")
  private String credit;

  @JsonProperty("IsCardTransation") // todo they might correct this
  private boolean cardTransaction;

  @JsonProperty("AccountNumber")
  private String accountNumber;

  @JsonProperty("ServiceCode")
  private String serviceCode;

  @JsonProperty("RecordType")
  private String recordType;

  @JsonProperty("ProductInfo")
  private String productInfo;
}
