/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.qore.service.account.service;

import static com.digicore.lucid.cba.service.modules.provider.qore.constant.QoreConstant.PROVIDER_NAME;
import static com.digicore.lucid.cba.service.modules.util.ProviderUtil.getObjectMapper;
import static com.digicore.lucid.integration.lib.modules.service.account.request.AccountServiceType.*;

import com.digicore.lucid.cba.service.modules.provider.qore.config.QorePropertyConfig;
import com.digicore.lucid.cba.service.modules.provider.qore.connector.QoreApiService;
import com.digicore.lucid.cba.service.modules.provider.qore.service.account.response.QoreOrganizationFetchCustomerDetailResponse;
import com.digicore.lucid.cba.service.modules.provider.qore.util.QoreUtil;
import com.digicore.lucid.cba.service.modules.service.account.CbaAccountService;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.account.request.OrganizationFetchDetailRequest;
import com.digicore.lucid.integration.lib.modules.service.account.response.OrganizationFetchCustomerDetailResponse;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-25(Tue)-2025
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class QoreOrganizationFetchCustomerService implements CbaAccountService<Object, Object> {
  private final QoreApiService qoreApiService;
  private final QorePropertyConfig qorePropertyConfig;

  @Override
  public Object process(Object request, String token) {
    OrganizationFetchDetailRequest organizationFetchDetailRequest =
        getObjectMapper().convertValue(request, OrganizationFetchDetailRequest.class);
    QoreOrganizationFetchCustomerDetailResponse qoreOrganizationFetchCustomerDetailResponse = null;
    try {
      if (!StringUtils.isBlank(organizationFetchDetailRequest.getCustomerId())) {
        qoreOrganizationFetchCustomerDetailResponse =
            qoreApiService
                .fetchOrganizationCustomer(token, organizationFetchDetailRequest.getCustomerId())
                .getBody();
      } else if (!StringUtils.isBlank(organizationFetchDetailRequest.getAccountNumber())) {
        qoreOrganizationFetchCustomerDetailResponse =
            qoreApiService
                .fetchOrganizationCustomerByAccountNumber(
                    token, organizationFetchDetailRequest.getAccountNumber())
                .getBody();
      }
      Objects.requireNonNull(
          qoreOrganizationFetchCustomerDetailResponse, "Response cannot be null");
      log.info(
          "<<< response : {} >>>",
          getObjectMapper().writeValueAsString(qoreOrganizationFetchCustomerDetailResponse));
    } catch (Exception e) {
      log.info("error : {}", e.getMessage());
      return CbaProvider.builder()
          .responseStatus(CbaProvider.ResponseStatus.FAILED)
          .narration("Customer not found")
          .provider(PROVIDER_NAME)
          .serviceRequired(CUSTOMER_CREATION)
          .build();
    }
    if (qoreOrganizationFetchCustomerDetailResponse.isSuccessful()) {
      OrganizationFetchCustomerDetailResponse.Account accountData =
          new OrganizationFetchCustomerDetailResponse.Account();
      accountData.setCustomerId(
          qoreOrganizationFetchCustomerDetailResponse.getCustomerIDInString());

      OrganizationFetchCustomerDetailResponse organizationFetchCustomerDetailResponse =
          OrganizationFetchCustomerDetailResponse.builder()
              .responseStatus(CbaProvider.ResponseStatus.COMPLETED)
              .narration(qoreOrganizationFetchCustomerDetailResponse.getMessage())
              .lastName(qoreOrganizationFetchCustomerDetailResponse.getLastName())
              .otherNames(qoreOrganizationFetchCustomerDetailResponse.getOtherNames())
              .phoneNo(qoreOrganizationFetchCustomerDetailResponse.getPhoneNumber())
              .address(qoreOrganizationFetchCustomerDetailResponse.getAddress())
              .email(qoreOrganizationFetchCustomerDetailResponse.getEmail())
              .address(qoreOrganizationFetchCustomerDetailResponse.getAddress())
              .bankVerificationNumber(
                  qoreOrganizationFetchCustomerDetailResponse.getBankVerificationNumber())
              .nationalIdentityNo(
                  qoreOrganizationFetchCustomerDetailResponse.getIdentificationNumber())
              .dateOfBirth(
                  QoreUtil.revertConversionDate(
                      qoreOrganizationFetchCustomerDetailResponse.getDateOfBirth()))
              .provider(PROVIDER_NAME)
              .serviceRequired(CUSTOMER_CREATION)
              .build();

      organizationFetchCustomerDetailResponse.setAccounts(List.of(accountData));
      return organizationFetchCustomerDetailResponse;
    } else
      return CbaProvider.builder()
          .responseStatus(CbaProvider.ResponseStatus.FAILED)
          .narration(qoreOrganizationFetchCustomerDetailResponse.getMessage())
          .provider(PROVIDER_NAME)
          .serviceRequired(CUSTOMER_CREATION)
          .build();
  }

  @Override
  public String getServiceKey() {
    return PROVIDER_NAME.concat(FETCH_CUSTOMER_DETAIL);
  }
}
