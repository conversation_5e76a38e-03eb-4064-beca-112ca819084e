/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.coronation.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createdOn May-12(Mon)-2025
 */
@Component
@ConfigurationPropertiesScan
@ConfigurationProperties(prefix = "lucid.cba.provider.coronation")
@Getter
@Setter
@Profile("coronation-provider")
public class CoronationPropertyConfig {
  private String baseUrl;
  private String authUrl;
  private String tokenExpiry;
  private String username;
  private String password;
}
