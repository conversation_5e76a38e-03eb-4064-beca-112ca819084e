/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.qore.service.transfer.interbank.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

/*
 * <AUTHOR>
 * @createdOn Mar-12(Wed)-2025
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QoreInterBankNameEnquiryRequest {
  @JsonProperty("AccountNumber")
  private String accountNumber;

  @JsonProperty("BankCode")
  private String bankCode;

  @JsonProperty("Token")
  private String token;
}
