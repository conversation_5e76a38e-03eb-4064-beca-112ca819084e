/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.qore.service.account.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn Feb-19(Wed)-2025
 */

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class QoreOrganizationCreateResponse {
  @JsonProperty("IsSuccessful")
  private boolean isSuccessful;

  @JsonProperty("CustomerIDInString")
  private String customerIDInString;

  @JsonProperty("Message")
  private String message;
}
