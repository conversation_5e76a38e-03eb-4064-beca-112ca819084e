/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.qore.service.account.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Getter;

/*
 * <AUTHOR>
 * @createdOn Mar-06(Thu)-2025
 */
@Getter
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class QoreCustomerAccountsResponse {
  @JsonProperty("CustomerId")
  private String customerId;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("PhoneNo")
  private String phoneNo;

  @JsonProperty("PostalAddress")
  private String postalAddress;

  @JsonProperty("BusinessPhoneNo")
  private String businessPhoneNo;

  @JsonProperty("TaxIDNo")
  private String taxIdNo;

  @JsonProperty("BusinessName")
  private String businessName;

  @JsonProperty("TradeName")
  private String tradeName;

  @JsonProperty("IndustrialSector")
  private String industrialSector;

  @JsonProperty("Email")
  private String email;

  @JsonProperty("Address")
  private String address;

  @JsonProperty("CompanyRegDate")
  private String companyRegDate;

  @JsonProperty("BusinessType")
  private String businessType;

  @JsonProperty("BusinessNature")
  private String businessNature;

  @JsonProperty("RegistrationNumber")
  private String registrationNumber;

  @JsonProperty("CustomerMembers")
  private List<String> customerMembers;

  @JsonProperty("TheDirectors")
  private List<String> theDirectors;

  @JsonProperty("Accounts")
  private List<Account> accounts;

  @Getter
  @Builder
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Account {
    @JsonProperty("NUBAN")
    private String accountNumber;

    @JsonProperty("AccountStatus")
    private String accountStatus;
  }
}
