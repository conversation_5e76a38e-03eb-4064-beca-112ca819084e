/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.processor.transfer.locator;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.lucid.cba.service.modules.service.transfer.CbaTransferService;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/*
 * <AUTHOR>
 * @createdOn Feb-26(Wed)-2025
 */

@Component
@Slf4j
public class TransferServiceLocator {
  private final Map<String, CbaTransferService<Object, Object>> serviceMap;

  @Autowired
  public TransferServiceLocator(List<CbaTransferService<Object, Object>> services) {
    this.serviceMap =
        services.stream()
            .collect(
                Collectors.toMap(
                    service -> service.getServiceKey().toLowerCase(), Function.identity()));
  }

  public CbaTransferService<Object, Object> getService(String provider, String serviceRequired) {
    String key = provider.concat(serviceRequired).toLowerCase();
    CbaTransferService<Object, Object> service = serviceMap.get(key);

    if (service == null) {
      log.error("No implementation found for service: {}", key.toUpperCase());
      throw new ZeusRuntimeException("No implementation found for service: ".concat(key));
    }
    return service;
  }
}
