/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.coronation.service.account.service;

import static com.digicore.lucid.cba.service.modules.provider.coronation.constant.CoronationConstant.PROVIDER_NAME;
import static com.digicore.lucid.cba.service.modules.util.ProviderUtil.getObjectMapper;
import static com.digicore.lucid.integration.lib.modules.service.account.request.AccountServiceType.FETCH_FX_RATE;

import com.digicore.lucid.cba.service.modules.provider.coronation.connector.CoronationApiService;
import com.digicore.lucid.cba.service.modules.provider.coronation.exception.GlobalFailureResponse;
import com.digicore.lucid.cba.service.modules.provider.coronation.service.account.response.CoronationFxRateResponse;
import com.digicore.lucid.cba.service.modules.provider.coronation.service.auth.service.CoronationTokenProvider;
import com.digicore.lucid.cba.service.modules.service.account.CbaAccountService;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.account.response.OrganizationFetchFxRateResponse;
import com.digicore.lucid.integration.lib.modules.service.account.response.OrganizationFetchFxRateResponse.RateEntry;
import feign.FeignException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Ogunwuyi
 * @createdOn Jun-26(Thur)-2025
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Profile("coronation-provider")
public class CoronationOrganizationFetchFxRateService implements CbaAccountService<Object, Object> {

  private final CoronationApiService coronationApiService;
  private final CoronationTokenProvider coronationTokenProvider;
  private static final String OFFER = "offer";
  private static final String BID = "bid";

  @Override
  public Object process(Object request, String token) {
    CoronationFxRateResponse coronationCustomerAccountsResponse;
    try {
      coronationCustomerAccountsResponse = coronationApiService.fetchOrganizationFxRate().getBody();
      log.info(
          "<<< coronation fx rate response : {} >>>",
          getObjectMapper().writeValueAsString(coronationCustomerAccountsResponse));
    } catch (Exception e) {
      log.info("coronation fx rate error : {}", e.getMessage(), e);
      if (e instanceof FeignException feignException) {
        GlobalFailureResponse globalFailureResponse =
            GlobalFailureResponse.formatFeignError(feignException);
        if (globalFailureResponse != null) {
          return CbaProvider.builder()
              .responseStatus(CbaProvider.ResponseStatus.FAILED)
              .narration(globalFailureResponse.getMessage())
              .provider(PROVIDER_NAME)
              .serviceRequired(FETCH_FX_RATE)
              .build();
        }
      }
      return getFailureResponse();
    }

    if (coronationCustomerAccountsResponse == null
        || !coronationCustomerAccountsResponse.isFlag()
        || coronationCustomerAccountsResponse.getData() == null) {
      return getFailureResponse();
    }

    CoronationFxRateResponse.FxRates customerAccountsResponseData =
        coronationCustomerAccountsResponse.getData();

    return OrganizationFetchFxRateResponse.builder()
        .responseStatus(CbaProvider.ResponseStatus.COMPLETED)
        .narration(coronationCustomerAccountsResponse.getMessage())
        .rateEntries(splitRates(customerAccountsResponseData))
        .provider(PROVIDER_NAME)
        .serviceRequired(FETCH_FX_RATE)
        .build();
  }

  public Map<String, Map<String, RateEntry>> splitRates(CoronationFxRateResponse.FxRates fxRates) {
    Map<String, Map<String, RateEntry>> rateMap = new HashMap<>();

    Map<String, Supplier<BigDecimal>> offerSuppliers =
        Map.of(
            "eurNgn", fxRates::getEurNgnOffer,
            "gbpNgn", fxRates::getGbpNgnOffer,
            "usdNgn", fxRates::getUsdNgnOffer,
            "eurGbp", fxRates::getEurGbpOffer,
            "eurUsd", fxRates::getEurUsdOffer,
            "gbpUsd", fxRates::getGbpUsdOffer);

    Map<String, Supplier<BigDecimal>> bidSuppliers =
        Map.of(
            "eurNgn", fxRates::getEurNgnBid,
            "gbpNgn", fxRates::getGbpNgnBid,
            "usdNgn", fxRates::getUsdNgnBid,
            "eurGbp", fxRates::getEurGbpBid,
            "eurUsd", fxRates::getEurUsdBid,
            "gbpUsd", fxRates::getGbpUsdBid);

    for (Map.Entry<String, Supplier<BigDecimal>> offerEntry : offerSuppliers.entrySet()) {
      String currency = offerEntry.getKey();
      BigDecimal offerValue = offerEntry.getValue().get();
      BigDecimal bidValue = bidSuppliers.get(currency).get();

      Map<String, RateEntry> typeMap = new HashMap<>();
      typeMap.put(OFFER, new RateEntry(currency, OFFER, offerValue));
      typeMap.put(BID, new RateEntry(currency, BID, bidValue));

      rateMap.put(currency, typeMap);
    }

    return rateMap;
  }

  private static CbaProvider getFailureResponse() {
    return CbaProvider.builder()
        .responseStatus(CbaProvider.ResponseStatus.FAILED)
        .narration("Unable to retrieve customer account inflows")
        .provider(PROVIDER_NAME)
        .serviceRequired(FETCH_FX_RATE)
        .build();
  }

  @Override
  public String getServiceKey() {
    return PROVIDER_NAME.concat(FETCH_FX_RATE);
  }

  @Override
  public String provideToken() {
    return coronationTokenProvider.provideToken();
  }
}
