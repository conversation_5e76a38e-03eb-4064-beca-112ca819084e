/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.coronation.service.transfer.fx.sale.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> @createdOn May-15(Thu)-2025
 */
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class CoronationFxSaleResponse {
  private boolean flag;
  private String code;
  private String message;
  private CoronationFxSaleResponseData data;

  @AllArgsConstructor
  @NoArgsConstructor
  @Setter
  @Getter
  public static class CoronationFxSaleResponseData {
    private String convertedFrom;
    private String convertedTo;
    private String amountConverted;
    private String rate;
    private String conversionResult;
  }
}
