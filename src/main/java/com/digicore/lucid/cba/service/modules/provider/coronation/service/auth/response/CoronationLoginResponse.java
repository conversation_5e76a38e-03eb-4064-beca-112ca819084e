/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.coronation.service.auth.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @createdOn May-12(Mon)-2025
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoronationLoginResponse {
  private boolean flag;
  private String code;
  private String message;
  private Token data;

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Token {
    private String accessToken;
    private long duration;
  }
}
