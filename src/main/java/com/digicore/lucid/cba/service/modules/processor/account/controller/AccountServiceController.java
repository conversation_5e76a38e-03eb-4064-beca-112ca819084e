/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.processor.account.controller;

import static com.digicore.lucid.integration.lib.modules.api.AccountApiControllerConstant.ACCOUNT_API;
import static com.digicore.lucid.integration.lib.modules.api.AccountApiControllerConstant.REQUEST;
import static com.digicore.lucid.integration.lib.modules.api.ApiVersionConstant.API_V1;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.cba.service.modules.processor.account.router.AccountServiceRouter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR>
 * @createdOn Feb-13(Thu)-2025
 */

@RestController
@RequestMapping(API_V1 + ACCOUNT_API)
@RequiredArgsConstructor
@Slf4j
public class AccountServiceController {
  private final AccountServiceRouter accountServiceRouter;

  @PostMapping(REQUEST)
  public ResponseEntity<Object> process(
      @RequestBody Object request,
      @RequestHeader("CBAProvider") String provider,
      @RequestHeader(value = "CBAToken", required = false) String token,
      @RequestHeader String serviceRequired) {
    log.info("the request is : {}", request);
    return ControllerResponse.buildSuccessResponse(
        accountServiceRouter.process(request, provider, token, serviceRequired));
  }
}
