/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.qore.service.account.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn 24/02/2025
 */

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class QoreAccountDetails {
  @JsonProperty("NUBAN")
  private String accountNumber;

  @JsonProperty("accountStatus")
  private String accountStatus;

  @JsonProperty("accountName")
  private String accountName;

  @JsonProperty("accountType")
  private String accountType;

  @JsonProperty("withdrawableAmount")
  private String withdrawableAmount;

  @JsonProperty("availableBalance")
  private String availableBalance;

  @JsonProperty("ledgerBalance")
  private String ledgerBalance;
}
