/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.coronation.service.account.service;

import static com.digicore.lucid.cba.service.modules.provider.coronation.constant.CoronationConstant.PROVIDER_NAME;
import static com.digicore.lucid.cba.service.modules.util.ProviderUtil.getObjectMapper;
import static com.digicore.lucid.integration.lib.modules.service.account.request.AccountServiceType.FETCH_ACCOUNT_INFLOW;
import static com.digicore.lucid.integration.lib.modules.service.transfer.request.TransferServiceType.INTER_NAME_ENQUIRY;

import com.digicore.lucid.cba.service.modules.provider.coronation.connector.CoronationApiService;
import com.digicore.lucid.cba.service.modules.provider.coronation.exception.GlobalFailureResponse;
import com.digicore.lucid.cba.service.modules.provider.coronation.service.account.response.CoronationCustomerAccountInflowResponse;
import com.digicore.lucid.cba.service.modules.provider.coronation.service.auth.service.CoronationTokenProvider;
import com.digicore.lucid.cba.service.modules.service.account.CbaAccountService;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.account.request.OrganizationFetchDetailRequest;
import com.digicore.lucid.integration.lib.modules.service.account.response.OrganizationFetchDetailResponse;
import feign.FeignException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Ogunwuyi
 * @createdOn Jun-26(Thur)-2025
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Profile("coronation-provider")
public class CoronationOrganizationFetchAccountInflowService
    implements CbaAccountService<Object, Object> {

  private final CoronationApiService coronationApiService;
  private final CoronationTokenProvider coronationTokenProvider;

  @Override
  public Object process(Object request, String token) {
    OrganizationFetchDetailRequest organizationFetchDetailRequest =
        getObjectMapper().convertValue(request, OrganizationFetchDetailRequest.class);
    String accountNumber = organizationFetchDetailRequest.getAccountNumber();

    CoronationCustomerAccountInflowResponse coronationCustomerAccountsResponse;
    try {
      coronationCustomerAccountsResponse =
          coronationApiService.fetchOrganizationCustomerAccountInflow(accountNumber).getBody();
      log.info(
          "<<< coronation customer account inflow response : {} >>>",
          getObjectMapper().writeValueAsString(coronationCustomerAccountsResponse));
    } catch (Exception e) {
      log.info("coronation customer account inflow error : {}", e.getMessage(), e);
      if (e instanceof FeignException feignException) {
        GlobalFailureResponse globalFailureResponse =
            GlobalFailureResponse.formatFeignError(feignException);
        if (globalFailureResponse != null) {
          return CbaProvider.builder()
              .responseStatus(CbaProvider.ResponseStatus.FAILED)
              .narration(globalFailureResponse.getMessage())
              .provider(PROVIDER_NAME)
              .serviceRequired(INTER_NAME_ENQUIRY)
              .build();
        }
      }
      return getFailureResponse();
    }

    if (coronationCustomerAccountsResponse == null
        || !coronationCustomerAccountsResponse.isFlag()
        || coronationCustomerAccountsResponse.getData() == null) {
      return getFailureResponse();
    }

    CoronationCustomerAccountInflowResponse.AccountInflow customerAccountsResponseData =
        coronationCustomerAccountsResponse.getData();

    return OrganizationFetchDetailResponse.builder()
        .responseStatus(CbaProvider.ResponseStatus.COMPLETED)
        .narration(coronationCustomerAccountsResponse.getMessage())
        .accounts(List.of(buildAccount(customerAccountsResponseData)))
        .provider(PROVIDER_NAME)
        .serviceRequired(FETCH_ACCOUNT_INFLOW)
        .build();
  }

  private OrganizationFetchDetailResponse.Account buildAccount(
      CoronationCustomerAccountInflowResponse.AccountInflow customerAccounts) {
    OrganizationFetchDetailResponse.Account account = new OrganizationFetchDetailResponse.Account();
    account.setInflowTotal(customerAccounts.getInflow().toPlainString());
    account.setOutflowTotal(customerAccounts.getOutflow().toPlainString());
    return account;
  }

  private static CbaProvider getFailureResponse() {
    return CbaProvider.builder()
        .responseStatus(CbaProvider.ResponseStatus.FAILED)
        .narration("Unable to retrieve customer account inflows")
        .provider(PROVIDER_NAME)
        .serviceRequired(FETCH_ACCOUNT_INFLOW)
        .build();
  }

  @Override
  public String getServiceKey() {
    return PROVIDER_NAME.concat(FETCH_ACCOUNT_INFLOW);
  }

  @Override
  public String provideToken() {
    return coronationTokenProvider.provideToken();
  }
}
