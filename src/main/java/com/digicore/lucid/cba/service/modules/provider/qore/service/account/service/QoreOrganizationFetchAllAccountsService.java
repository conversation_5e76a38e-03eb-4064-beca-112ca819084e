/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.qore.service.account.service;

import static com.digicore.lucid.cba.service.modules.provider.qore.constant.QoreConstant.PROVIDER_NAME;
import static com.digicore.lucid.cba.service.modules.util.ProviderUtil.getObjectMapper;
import static com.digicore.lucid.integration.lib.modules.service.account.request.AccountServiceType.CUSTOMER_CREATION;
import static com.digicore.lucid.integration.lib.modules.service.account.request.AccountServiceType.FETCH_ALL_ACCOUNTS;

import com.digicore.lucid.cba.service.modules.provider.qore.connector.QoreApiService;
import com.digicore.lucid.cba.service.modules.provider.qore.service.account.response.QoreAccountDetails;
import com.digicore.lucid.cba.service.modules.provider.qore.service.account.response.QoreFetchAccountsResponse;
import com.digicore.lucid.cba.service.modules.service.account.CbaAccountService;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.account.request.OrganizationFetchDetailRequest;
import com.digicore.lucid.integration.lib.modules.service.account.response.OrganizationFetchAccountsResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ilori
 * @createdOn 24/02/2025
 */

@RequiredArgsConstructor
@Service
@Slf4j
public class QoreOrganizationFetchAllAccountsService implements CbaAccountService<Object, Object> {
  private final QoreApiService qoreApiService;

  @Override
  public Object process(Object request, String token) {
    OrganizationFetchDetailRequest organizationFetchAccountsRequest =
        getObjectMapper().convertValue(request, OrganizationFetchDetailRequest.class);
    QoreFetchAccountsResponse qoreFetchAccountsResponse;
    try {
      qoreFetchAccountsResponse =
          qoreApiService
              .fetchAccountsByCustomerId(token, organizationFetchAccountsRequest.getCustomerId())
              .getBody();

      Objects.requireNonNull(qoreFetchAccountsResponse, "Response cannot be null");
      log.info(
          "<<< response : {} >>>", getObjectMapper().writeValueAsString(qoreFetchAccountsResponse));
    } catch (Exception e) {
      log.info("error : {}", e.getMessage());
      return CbaProvider.builder()
          .responseStatus(CbaProvider.ResponseStatus.FAILED)
          .narration("Server Error")
          .provider(PROVIDER_NAME)
          .serviceRequired(CUSTOMER_CREATION)
          .build();
    }
    List<OrganizationFetchAccountsResponse.Accounts> accountsResponse = new ArrayList<>();

    if (qoreFetchAccountsResponse.getAccounts().isEmpty()) {
      return CbaProvider.builder()
          .responseStatus(CbaProvider.ResponseStatus.FAILED)
          .narration("Accounts not found")
          .provider(PROVIDER_NAME)
          .serviceRequired(FETCH_ALL_ACCOUNTS)
          .build();
    }
    for (QoreAccountDetails qoreAccountDetails : qoreFetchAccountsResponse.getAccounts()) {
      accountsResponse.add(
          new OrganizationFetchAccountsResponse.Accounts(
              qoreAccountDetails.getAccountName(),
              qoreAccountDetails.getAccountType(),
              qoreAccountDetails.getAccountStatus(),
              "NGN",
              qoreAccountDetails.getAccountNumber(),
              qoreAccountDetails.getWithdrawableAmount(),
              qoreAccountDetails.getAvailableBalance(),
              qoreAccountDetails.getLedgerBalance()));
    }
    return OrganizationFetchAccountsResponse.builder()
        .accounts(accountsResponse)
        .responseStatus(CbaProvider.ResponseStatus.COMPLETED)
        .provider(PROVIDER_NAME)
        .serviceRequired(FETCH_ALL_ACCOUNTS)
        .build();
  }

  @Override
  public String getServiceKey() {
    return PROVIDER_NAME.concat(FETCH_ALL_ACCOUNTS);
  }
}
