/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.interceptor;

import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

/*
 * <AUTHOR>
 * @createdOn Jan-30(Thu)-2025
 */

@Component
public class RequestContextHolder {
  private static final ThreadLocal<RequestContext> context = new ThreadLocal<>();

  private RequestContextHolder() {}

  public static RequestContext get() {
    return context.get();
  }

  public static void set(RequestContext requestContext) {
    context.set(requestContext);
  }

  public static void clear() {
    context.remove();
  }

  @Getter
  @Setter
  public static class RequestContext {
    private String cbaProvider;
    private String serviceRequired;
  }
}
