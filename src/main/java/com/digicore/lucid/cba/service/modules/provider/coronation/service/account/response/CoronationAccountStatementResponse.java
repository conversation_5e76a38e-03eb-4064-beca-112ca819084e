/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.provider.coronation.service.account.response;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @createdOn May-15(Thu)-2025
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class CoronationAccountStatementResponse {
  private boolean flag;
  private String code;
  private String message;
  private List<AccountStatementData> data;
  private long currentPage;
  private int size;
  private int totalElements;
  private int totalPages;
  private boolean isLast;

  @Setter
  @Getter
  @AllArgsConstructor
  @NoArgsConstructor
  public static class AccountStatementData {
    private String tranId;
    private String tranRemarks;
    private String traDate;
    private String valDate;
    private String currency;
    private String traType;
    private String narration;
    private String traBal;
    private String traAmt;
    private String pstdDate;
  }
}
