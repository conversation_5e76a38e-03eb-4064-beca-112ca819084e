/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.cba.service.modules.processor.account.router;

import com.digicore.lucid.cba.service.modules.processor.account.locator.AccountServiceLocator;
import com.digicore.lucid.cba.service.modules.service.account.CbaAccountService;
import com.digicore.lucid.cba.service.modules.service.token.TokenHelper;
import com.digicore.lucid.integration.lib.modules.config.properties.SecurityPropertyConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/*
 * <AUTHOR>
 * @createdOn Feb-13(Thu)-2025
 */

@Component
@Slf4j
@RequiredArgsConstructor
public class AccountServiceRouter {
  private final AccountServiceLocator serviceLocator;
  private final SecurityPropertyConfig securityPropertyConfig;

  public Object process(Object request, String provider, String token, String serviceRequired) {
    log.info("<<< request : {} >>>", request);
    log.info("<<< provider : {} >>>", provider);
    log.info("<<< serviceRequired : {} >>>", serviceRequired);

    CbaAccountService<Object, Object> service =
        serviceLocator.getService(provider, serviceRequired);
    token = TokenHelper.of(token, securityPropertyConfig.getSystemKey()).getValue();

    if (StringUtils.isBlank(token)) {
      token = service.provideToken();
    }

    return service.process(request, token);
  }
}
