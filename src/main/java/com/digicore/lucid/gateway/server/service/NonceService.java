/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.gateway.server.service;

import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Mar-24(Mon)-2025
 */

@RequiredArgsConstructor
@Service
public class NonceService {
  private final RedissonClient redissonClient;

  public boolean isNonceValid(String nonce) {
    String hashedNonce = hashNonce(nonce);
    RBucket<Boolean> bucket = redissonClient.getBucket("nonce:" + hashedNonce);

    // Set the nonce if it doesn't exist, return true if successfully stored
    return bucket.setIfAbsent(true);
  }

  private String hashNonce(String nonce) {
    return DigestUtils.sha256Hex(nonce); // Securely hash the nonce
  }
}
