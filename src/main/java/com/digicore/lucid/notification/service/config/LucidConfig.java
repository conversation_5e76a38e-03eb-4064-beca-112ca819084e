/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.notification.service.config;

import com.digicore.lucid.notification.service.lib.config.MailPropertyConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Description;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;
import org.thymeleaf.spring6.SpringTemplateEngine;

/*
 * <AUTHOR>
 * @createdOn Feb-10(Mon)-2025
 */

@Configuration
@RequiredArgsConstructor
public class LucidConfig {
  private final MailPropertyConfig mailPropertyConfig;

  @Bean
  public RestTemplate restTemplate() {
    return new RestTemplate();
  }

  @Bean
  public Queue lucidQueue() {
    return new Queue(mailPropertyConfig.getQueueName(), false);
  }

  @Bean
  @Primary
  @Description("Thymeleaf template engine with Spring integration")
  public SpringTemplateEngine springTemplateEngine() {
    return new SpringTemplateEngine();
  }
}
