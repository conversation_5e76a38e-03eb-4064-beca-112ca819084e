/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.notification.service.provider.coronation.service;

import com.digicore.lucid.notification.service.lib.SmsNotificationProvider;
import com.digicore.lucid.notification.service.lib.config.MailPropertyConfig;
import com.digicore.lucid.notification.service.lib.enums.NotificationResponseCodes;
import com.digicore.lucid.notification.service.lib.request.NotificationServiceRequest;
import com.digicore.lucid.notification.service.lib.response.NotificationServiceResponse;
import com.digicore.lucid.notification.service.provider.coronation.service.response.CoronationNotificationResponse;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @createdOn Feb-13(Thur)-2025
 */
@Service
@RequiredArgsConstructor
@Profile("coronation")
@Slf4j
public class CoronationSmsService implements SmsNotificationProvider {

  private final CoronationApiService coronationApiService;
  private final MailPropertyConfig mailPropertyConfig;

  @Override
  public NotificationServiceResponse sendSms(
      NotificationServiceRequest notificationServiceRequest) {
    try {
      if (notificationServiceRequest.getPhoneNumbers() == null
          || notificationServiceRequest.getPhoneNumbers().isEmpty()
          || StringUtils.isBlank(notificationServiceRequest.getPhoneNumbers().get(0))) {
        throw new IllegalArgumentException("phone number is not provided");
      }

      String message =
          StringUtils.isBlank(notificationServiceRequest.getMessage())
              ? notificationServiceRequest.getUserCode()
              : notificationServiceRequest.getMessage();

      message = "Your OTP is " + message;

      if (StringUtils.isBlank(message)) {
        throw new IllegalArgumentException("SMS message is not provided");
      }

      Map<String, Object> smsRequest = new HashMap<>();
      smsRequest.put("phoneNumber", notificationServiceRequest.getPhoneNumbers().get(0));
      smsRequest.put("message", message);

      if (mailPropertyConfig.isTest()) {
        log.info("This SMS is not sent because it is in test mode");
        return NotificationServiceResponse.fromCodeAndNarration(
            NotificationResponseCodes.COMPLETED, "message sent successfully");
      }

      CoronationNotificationResponse coronationNotificationResponse =
          coronationApiService.sendSms(smsRequest).getBody();
      log.info("<<< coronation sms response : {} >>>", coronationNotificationResponse);

      if (coronationNotificationResponse != null && !coronationNotificationResponse.isFlag()) {
        return NotificationServiceResponse.fromCodeAndNarration(
            NotificationResponseCodes.COMPLETED, "message sent successfully");
      }
    } catch (Exception e) {
      log.info("coronation sms error : {}", e.getMessage());
    }

    return NotificationServiceResponse.fromCodeAndNarration(
        NotificationResponseCodes.ERROR, "message send failed");
  }
}
