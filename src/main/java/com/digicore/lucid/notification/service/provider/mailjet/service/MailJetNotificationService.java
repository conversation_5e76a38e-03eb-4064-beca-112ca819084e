/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.notification.service.provider.mailjet.service;

import com.digicore.lucid.notification.service.lib.NotificationProvider;
import com.digicore.lucid.notification.service.lib.config.MailPropertyConfig;
import com.digicore.lucid.notification.service.lib.enums.NotificationResponseCodes;
import com.digicore.lucid.notification.service.lib.request.NotificationServiceRequest;
import com.digicore.lucid.notification.service.lib.response.NotificationServiceResponse;
import com.mailjet.client.MailjetClient;
import com.mailjet.client.transactional.*;
import com.mailjet.client.transactional.response.SendEmailsResponse;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Feb-13(Thur)-2025
 */

@Service
@RequiredArgsConstructor
@Profile("mailjet")
@Slf4j
public class MailJetNotificationService implements NotificationProvider {
  private final MailjetClient client;
  private final MailPropertyConfig mailPropertyConfig;

  @Override
  public NotificationServiceResponse sendEmail(
      NotificationServiceRequest notificationServiceRequest, boolean isHtml) {

    List<TransactionalEmail> messages = new ArrayList<>();

    for (String recipient : notificationServiceRequest.getRecipients()) {
      TransactionalEmail message1 =
          TransactionalEmail.builder()
              .to(new SendContact(recipient, notificationServiceRequest.getFirstName()))
              .from(
                  new SendContact(
                      mailPropertyConfig.getDefaultSenderMail(),
                      mailPropertyConfig.getDefaultSenderName()))
              .htmlPart(notificationServiceRequest.getNotificationContent())
              .subject(notificationServiceRequest.getSubject())
              .trackOpens(TrackOpens.DISABLED)
              .build();

      messages.add(message1);
    }

    SendEmailsRequest request = SendEmailsRequest.builder().messages(messages).build();

    SendEmailsResponse response;
    try {
      response = request.sendWith(client);
    } catch (Exception e) {
      log.error("error sending mail", e);
      return NotificationServiceResponse.fromCodeAndNarration(
          NotificationResponseCodes.ERROR, "message send failed");
    }
    return NotificationServiceResponse.fromCodeAndNarration(
        NotificationResponseCodes.COMPLETED, "message sent successfully");
  }

  @Override
  public NotificationServiceResponse sendSms(
      NotificationServiceRequest notificationServiceRequest) {
    // TODO implement sms integration
    return NotificationServiceResponse.fromCodeAndNarration(
        NotificationResponseCodes.COMPLETED, "message sent successfully");
  }
}
