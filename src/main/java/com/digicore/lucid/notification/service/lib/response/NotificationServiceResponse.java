/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.notification.service.lib.response;

import com.digicore.lucid.notification.service.lib.enums.NotificationResponseCodes;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;

/*
 * <AUTHOR>
 * @createdOn Feb-10(Mon)-2025
 */

@JsonInclude(JsonInclude.Include.NON_DEFAULT)
@Data
@NoArgsConstructor
public class NotificationServiceResponse {
  private String narration;
  private NotificationResponseCodes notificationServiceResponseCode;
  private Map<String, Object> data;

  public static NotificationServiceResponse fromCodeAndNarration(
      NotificationResponseCodes notificationResponseCodes, String msg) {
    NotificationServiceResponse notificationServiceResponse = new NotificationServiceResponse();
    notificationServiceResponse.setNarration(msg);
    notificationServiceResponse.setNotificationServiceResponseCode(notificationResponseCodes);
    return notificationServiceResponse;
  }
}
