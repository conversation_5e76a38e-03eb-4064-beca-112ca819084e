/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.notification.service.lib.service;

import com.digicore.lucid.notification.service.lib.NotificationService;
import com.digicore.lucid.notification.service.lib.enums.NotificationResponseCodes;
import com.digicore.lucid.notification.service.lib.request.NotificationServiceRequest;
import com.digicore.lucid.notification.service.lib.response.NotificationServiceResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Feb-10(Mon)-2025
 */

@Service
@RequiredArgsConstructor
@Profile("notification")
public class NotificationHandler implements NotificationService {
  private final NotificationServiceProcessor notificationServiceProcessor;

  @Override
  public NotificationServiceResponse sendNotification(
      NotificationServiceRequest notificationServiceRequest) throws JSONException {

    NotificationServiceResponse notificationServiceResponse;

    if (notificationServiceRequest == null
        || StringUtils.isEmpty(notificationServiceRequest.getNotificationRequestType())) {
      return NotificationServiceResponse.fromCodeAndNarration(
          NotificationResponseCodes.INVALID_REQUEST,
          "Invalid notification service request supplied");
    }

    if ("SMS".equalsIgnoreCase(notificationServiceRequest.getChannel()))
      notificationServiceResponse =
          notificationServiceProcessor.sendSms(notificationServiceRequest);
    else
      notificationServiceResponse =
          notificationServiceProcessor.sendEmail(notificationServiceRequest);

    return notificationServiceResponse;
  }
}
