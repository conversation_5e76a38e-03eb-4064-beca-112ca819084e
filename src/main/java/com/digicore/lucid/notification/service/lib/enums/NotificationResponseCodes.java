/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.notification.service.lib.enums;

/*
 * <AUTHOR>
 * @createdOn Feb-10(Mon)-2025
 */

public enum NotificationResponseCodes {
  COMPLETED,
  ERROR,
  INVALID_REQUEST,
  FAILED;

  public static NotificationResponseCodes fromString(String text) {

    if (text != null) {
      for (NotificationResponseCodes b : NotificationResponseCodes.values()) {
        if (text.equalsIgnoreCase(b.name())) {
          return b;
        }
      }
    }

    throw new IllegalArgumentException(String.format("Unknown enum type %s", text));
  }
}
