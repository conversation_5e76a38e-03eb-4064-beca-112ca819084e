/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.notification.service.provider.coronation.service;

import com.digicore.lucid.notification.service.lib.MailNotificationProvider;
import com.digicore.lucid.notification.service.lib.config.MailPropertyConfig;
import com.digicore.lucid.notification.service.lib.enums.NotificationResponseCodes;
import com.digicore.lucid.notification.service.lib.request.NotificationRequestFileDTO;
import com.digicore.lucid.notification.service.lib.request.NotificationServiceRequest;
import com.digicore.lucid.notification.service.lib.response.NotificationServiceResponse;
import com.digicore.lucid.notification.service.provider.coronation.service.response.CoronationNotificationResponse;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @createdOn Feb-13(Thur)-2025
 */
@Service
@RequiredArgsConstructor
@Profile("coronation")
@Slf4j
public class CoronationMailService implements MailNotificationProvider {

  private final CoronationApiService coronationApiService;
  private final MailPropertyConfig mailPropertyConfig;

  @Override
  public NotificationServiceResponse sendEmail(
      NotificationServiceRequest notificationServiceRequest, boolean isHtml) {
    try {
      List<String> emails = notificationServiceRequest.getRecipients();

      if (emails == null || emails.isEmpty() || StringUtils.isBlank(emails.get(0))) {
        log.error("Emails list is null or empty");
        return NotificationServiceResponse.fromCodeAndNarration(
            NotificationResponseCodes.ERROR, "message send failed");
      }

      String html = notificationServiceRequest.getNotificationContent();

      if (StringUtils.isBlank(html)) {
        log.error("Message is blank");
        return NotificationServiceResponse.fromCodeAndNarration(
            NotificationResponseCodes.ERROR, "message send failed");
      }

      if (StringUtils.isBlank(mailPropertyConfig.getEmailSender())) {
        log.error("Email sender is blank");
        return NotificationServiceResponse.fromCodeAndNarration(
            NotificationResponseCodes.ERROR, "message send failed");
      }

      List<NotificationRequestFileDTO> emailAttachments =
          notificationServiceRequest.getAttachments();
      List<Map<String, String>> attachments = null;

      if (emailAttachments != null && !emailAttachments.isEmpty()) {
        attachments = new ArrayList<>();
        for (NotificationRequestFileDTO emailAttachment : emailAttachments) {
          try {
            String fileName = emailAttachment.getFileName();
            String base64 = Base64.getEncoder().encodeToString(emailAttachment.getFileContent());

            Map<String, String> attachmentMap = new HashMap<>();
            attachmentMap.put("fileName", fileName);
            attachmentMap.put("base64Str", base64);

            attachments.add(attachmentMap);
          } catch (Exception e) {
            log.error("Unable to convert to base64 {}", emailAttachment.getFileName());
          }
        }
      }

      Map<String, Object> emailRequest = new HashMap<>();
      emailRequest.put(
          "subject",
          notificationServiceRequest.getNotificationSubject() == null
              ? "no reply"
              : notificationServiceRequest.getNotificationSubject());
      emailRequest.put("message", html);
      emailRequest.put("from", mailPropertyConfig.getEmailSender());
      emailRequest.put("toEmails", notificationServiceRequest.getRecipients());

      if (notificationServiceRequest.getCcList() != null
          && !notificationServiceRequest.getCcList().isEmpty()) {
        emailRequest.put("ccEmails", notificationServiceRequest.getCcList());
      }
      if (notificationServiceRequest.getBccList() != null
          && !notificationServiceRequest.getBccList().isEmpty()) {
        emailRequest.put("bccEmails", notificationServiceRequest.getBccList());
      }
      if (attachments != null && !attachments.isEmpty()) {
        emailRequest.put("attachments", attachments);
      }

      CoronationNotificationResponse coronationNotificationResponse =
          coronationApiService.sendEmail(emailRequest).getBody();
      log.info(
          "<<< coronation email notification response : {} >>>", coronationNotificationResponse);

      if (coronationNotificationResponse != null && coronationNotificationResponse.isFlag()) {
        return NotificationServiceResponse.fromCodeAndNarration(
            NotificationResponseCodes.COMPLETED, "message sent successfully");
      }
    } catch (Exception e) {
      log.info("coronation email notification error : {}", e.getMessage(), e);
    }

    return NotificationServiceResponse.fromCodeAndNarration(
        NotificationResponseCodes.ERROR, "message send failed");
  }
}
