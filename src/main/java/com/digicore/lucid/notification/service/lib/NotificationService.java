/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.notification.service.lib;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.lucid.notification.service.lib.request.NotificationServiceRequest;
import com.digicore.lucid.notification.service.lib.response.NotificationServiceResponse;
import org.json.JSONException;

/*
 * <AUTHOR>
 * @createdOn Feb-10(Mon)-2025
 */

public interface NotificationService {
  default NotificationServiceResponse sendNotification(
      NotificationServiceRequest notificationServiceRequest) throws JSONException {
    throw new ZeusRuntimeException("Not supported");
  }
}
