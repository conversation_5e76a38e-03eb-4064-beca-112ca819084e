/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.notification.service.provider.coronation.token.service;

import com.digicore.lucid.notification.service.provider.coronation.token.request.CoronationLoginRequest;
import com.digicore.lucid.notification.service.provider.coronation.token.response.CoronationLoginResponse;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @createdOn May-12(Mon)-2025
 */
@FeignClient(name = "coronationAuthClient", url = "${lucid.notification.mail.coronationBaseUrl}")
@ConditionalOnProperty(prefix = "lucid.notification.mail", name = "coronationBaseUrl")
public interface CoronationAuthClient {

  @PostMapping("${lucid.notification.mail.authUrl}")
  CoronationLoginResponse login(@RequestBody CoronationLoginRequest coronationLoginRequest);
}
