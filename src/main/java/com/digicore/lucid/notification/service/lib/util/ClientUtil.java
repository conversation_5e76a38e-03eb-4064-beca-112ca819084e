/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.notification.service.lib.util;

import com.digicore.lucid.notification.service.lib.config.LocalDateTimeTypeAdapter;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import java.time.LocalDateTime;
import lombok.Getter;

/*
 * <AUTHOR>
 * @createdOn Feb-10(Mon)-2025
 */

public class ClientUtil {

  @Getter
  private static final Gson gsonMapper =
      (new GsonBuilder())
          .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeTypeAdapter())
          .create();
}
