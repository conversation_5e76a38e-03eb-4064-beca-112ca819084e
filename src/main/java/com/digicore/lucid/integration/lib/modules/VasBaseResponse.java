package com.digicore.lucid.integration.lib.modules;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class VasBaseResponse {
  private ResponseStatus responseStatus;
  private String narration;
  private String provider;
  private String serviceRequired;

  public enum ResponseStatus {
    COMPLETED,
    FAILED
  }
}
