/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.integration.lib.modules.service.account.response;

import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.SuperBuilder;

/*
 * <AUTHOR>
 * @createdOn Feb-13(Thu)-2025
 */

@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BvnValidationResponse extends CbaProvider {
  private String bvn;
  private String phoneNumber;
  private String firstName;
  private String lastName;
  private String otherNames;
  private String dob;
  private String address;
  private String validationRef;
}
