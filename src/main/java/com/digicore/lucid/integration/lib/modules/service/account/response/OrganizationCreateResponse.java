/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.integration.lib.modules.service.account.response;

import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/*
 * <AUTHOR>
 * @createdOn Feb-18(Tue)-2025
 */

@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class OrganizationCreateResponse extends CbaProvider {

  private String organizationId;
}
