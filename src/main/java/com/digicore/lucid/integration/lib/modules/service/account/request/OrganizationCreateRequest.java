/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.integration.lib.modules.service.account.request;

import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/*
 * <AUTHOR>
 * @createdOn Feb-18(Tue)-2025
 */

@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrganizationCreateRequest extends CbaProvider {
  private String name;
  private String phoneNo;
  private String postalAddress;
  private String businessPhoneNo;
  private String taxIDNo;
  private String businessName;
  private String tradeName;
  private String industrialSector;
  private String email;
  private String address;
  private String companyRegDate;
  private String contactPersonName;
  private String businessType;
  private String businessNature;
  private String webAddress;
  private String dateIncorporated;
  private String businessCommencementDate;
  private String registrationNumber;
  private String customerId;
  private List<String> customerMembers;
  private List<String> theDirectors;
}
