/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.integration.lib.modules.service;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/*
 * <AUTHOR>
 * @createdOn Feb-12(Wed)-2025
 */

@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CbaProvider {
  private ResponseStatus responseStatus;
  private String narration;
  private String provider;
  private String serviceRequired;

  public enum ResponseStatus {
    COMPLETED,
    COMPLETED_WITH_ERROR,
    FAILED
  }
}
