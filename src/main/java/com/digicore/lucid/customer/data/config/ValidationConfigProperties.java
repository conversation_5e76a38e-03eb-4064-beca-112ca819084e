/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.config;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.api.helper.response.ApiError;
import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @createdOn Jun-10(Tue)-2025
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "validation")
public class ValidationConfigProperties {
  private List<ValidationConfig> configs;

  public ValidationConfig getConfigBySubDomain(String subDomain) {
    return configs.stream()
        .filter(config -> config.getSubDomain().equalsIgnoreCase(subDomain))
        .findFirst()
        .orElse(getDefaultConfig());
  }

  private ValidationConfig getDefaultConfig() {
    return configs.stream()
        .filter(config -> "default".equals(config.getSubDomain()))
        .findFirst()
        .orElseThrow(
            () ->
                new ZeusRuntimeException(
                    HttpStatus.BAD_REQUEST,
                    List.of(new ApiError("Default validation config not found"))));
  }

  @Data
  public static class ValidationConfig {
    private String subDomain;
    private List<ValidationType> validationType;
  }

  @Data
  public static class ValidationType {
    private String type;
    private Integer requiredSize;
    private String sourcePath;
    private List<String> action;
  }
}
