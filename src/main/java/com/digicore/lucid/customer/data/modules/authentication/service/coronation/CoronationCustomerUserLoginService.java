/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.authentication.service.coronation;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.system.SystemConstant.*;

import com.digicore.lucid.common.lib.activation.dto.FileUploadedDTO;
import com.digicore.lucid.common.lib.authentication.service.LoginServiceHelper;
import com.digicore.lucid.common.lib.authorization.dto.PermissionDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleCreationDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleDTO;
import com.digicore.lucid.common.lib.authorization.service.PermissionService;
import com.digicore.lucid.common.lib.authorization.service.RoleService;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.util.ImageBase64Util;
import com.digicore.lucid.customer.data.modules.activation.model.CustomerUploadedDocument;
import com.digicore.lucid.customer.data.modules.activation.repository.CustomerDocumentRepository;
import com.digicore.lucid.customer.data.modules.authentication.model.CustomerUserAuthProfile;
import com.digicore.lucid.customer.data.modules.authentication.repository.CustomerUserAuthProfileRepository;
import com.digicore.lucid.customer.data.modules.onboarding.model.CustomerPhishingImage;
import com.digicore.lucid.customer.data.modules.onboarding.repository.CustomerPhishingImageRepository;
import com.digicore.registhentication.authentication.dtos.request.LoginAttemptDTO;
import com.digicore.registhentication.authentication.dtos.request.LoginRequestDTO;
import com.digicore.registhentication.authentication.dtos.response.LoginResponse;
import com.digicore.registhentication.authentication.services.LoginAttemptService;
import com.digicore.registhentication.authentication.services.LoginService;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.enums.Status;
import com.google.gson.reflect.TypeToken;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-11(Tue)-2025
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class CoronationCustomerUserLoginService
    implements UserDetailsService, LoginService<LoginResponse, LoginRequestDTO> {
  private final CustomerUserAuthProfileRepository customerUserAuthProfileRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final PasswordEncoder passwordEncoder;
  private final MessagePropertyConfig messagePropertyConfig;
  private final LoginAttemptService loginAttemptService;
  private final RoleService<RoleDTO, RoleCreationDTO> customerUserRoleService;
  private final LoginServiceHelper loginServiceHelper;
  private final PermissionService<PermissionDTO> customerUserPermissionService;
  private final CustomerPhishingImageRepository customerPhishingImageRepository;
  private final CustomerDocumentRepository customerDocumentRepository;

  @Override
  public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
    CustomerUserAuthProfile userFoundInDB =
        customerUserAuthProfileRepository
            .findFirstByUsernameAndIsDeletedFalseAndCustomerUserProfileCustomerProfileOrganizationIdOrderByCreatedDate(
                username, RequestContextHolder.get().getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getLoginMessage(LOGIN_FAILED),
                        HttpStatus.UNAUTHORIZED));
    if (Status.INACTIVE.equals(userFoundInDB.getStatus()))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getLoginMessage(INACTIVE), HttpStatus.UNAUTHORIZED);

    return getUserProfileDTO(username, userFoundInDB);
  }

  @Override
  public LoginResponse authenticate(LoginRequestDTO loginRequestDTO) {
    UserProfileDTO userDetails = (UserProfileDTO) loadUserByUsername(loginRequestDTO.getUsername());
    LoginAttemptDTO loginAttemptDTO =
        LoginAttemptDTO.builder()
            .name(userDetails.getFirstName().concat(" ").concat(userDetails.getLastName()))
            .role(userDetails.getAssignedRole())
            .username(userDetails.getUsername())
            .authenticationType(loginRequestDTO.getAuthenticationType().toString())
            .build();
    if (passwordEncoder.matches(loginRequestDTO.getPassword(), userDetails.getPassword())) {
      Optional<CustomerPhishingImage> optionalCustomerPhishingImage =
          customerPhishingImageRepository
              .findFirstByUsernameAndBankOrganizationIdAndOrganizationIdAndIsDeletedFalseOrderByCreatedDateDesc(
                  loginRequestDTO.getUsername(),
                  userDetails.getBankOrganizationId(),
                  userDetails.getOrganizationId());
      if (optionalCustomerPhishingImage.isPresent()) {
        String targetImageBase64 =
            loginRequestDTO.getPhishingImage().replaceAll("data:image/[^;]+;base64,", "");
        CustomerPhishingImage customerPhishingImage = optionalCustomerPhishingImage.get();
        String phishingImage = getPhishingImage(customerPhishingImage);
        if (StringUtils.isNotBlank(phishingImage) && targetImageBase64.equals(phishingImage)) {
          loginAttemptService.verifyLoginAccess(loginAttemptDTO, true);
          updateLastLogin(loginRequestDTO.getUsername());
          return loginServiceHelper.getLoginResponse(loginRequestDTO, userDetails);
        } else {
          log.error("Phishing Image Mismatch For User {}", loginRequestDTO.getUsername());
        }
      } else {
        log.error("Phishing Image Not Found For User {}", loginRequestDTO.getUsername());
      }
    }

    loginAttemptService.verifyLoginAccess(loginAttemptDTO, false);
    exceptionHandler.processCustomExceptions(
        messagePropertyConfig.getLoginMessage(LOGIN_FAILED), HttpStatus.UNAUTHORIZED);
    return null;
  }

  private void updateLastLogin(String username) {
    CustomerUserAuthProfile user =
        customerUserAuthProfileRepository
            .findFirstByUsernameAndIsDeletedFalseAndCustomerUserProfileCustomerProfileOrganizationIdOrderByCreatedDate(
                username, RequestContextHolder.get().getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getUserMessage(NOT_FOUND), HttpStatus.UNAUTHORIZED));

    user.setLastLoginDate(LocalDateTime.now());
    customerUserAuthProfileRepository.save(user);
  }

  private UserProfileDTO getUserProfileDTO(String username, CustomerUserAuthProfile userFoundInDB) {
    UserProfileDTO userProfileDTO = new UserProfileDTO();
    userProfileDTO.setUsername(username);
    userProfileDTO.setFirstName(userFoundInDB.getCustomerUserProfile().getFirstName());
    userProfileDTO.setLastName(userFoundInDB.getCustomerUserProfile().getLastName());
    userProfileDTO.setAssignedRole(userFoundInDB.getAssignedRole());
    userProfileDTO.setEmail(userFoundInDB.getCustomerUserProfile().getEmail());
    userProfileDTO.setProfileId(userFoundInDB.getCustomerUserProfile().getProfileId());
    userProfileDTO.setOrganizationId(
        userFoundInDB.getCustomerUserProfile().getCustomerProfile().getOrganizationId());
    userProfileDTO.setBankOrganizationId(
        userFoundInDB.getCustomerUserProfile().getCustomerProfile().getBankOrganizationId());
    userProfileDTO.setReferralCode(userFoundInDB.getCustomerUserProfile().getReferralCode());
    userProfileDTO.setPassword(userFoundInDB.getPassword());
    userProfileDTO.setPin(userFoundInDB.getPin());
    userProfileDTO.setDefaultPassword(userFoundInDB.isDefaultPassword());
    userProfileDTO.setProfileActivated(
        userFoundInDB
            .getCustomerUserProfile()
            .getCustomerProfile()
            .getStatus()
            .equals(Status.ACTIVE));
    userProfileDTO.setPermissions(getGrantedAuthorities(userFoundInDB.getAssignedRole()));
    userProfileDTO.setRequired(
        userFoundInDB.getCustomerUserProfile().getCustomerProfile().getCustomerUserProfiles().size()
            == 1);
    userProfileDTO.setSubDomain(
        userFoundInDB.getCustomerUserProfile().getCustomerProfile().getSubDomainName());
    userProfileDTO.setLastLoginDate(userFoundInDB.getLastLoginDate());
    return userProfileDTO;
  }

  private Set<SimpleGrantedAuthority> getGrantedAuthorities(String assignedRole) {
    RoleDTO roleDTO = customerUserRoleService.retrieveRole(assignedRole);
    List<String> rolePermissions;
    if (roleDTO.getName().equalsIgnoreCase(CUSTODIAN_ROLE_NAME)
        || roleDTO.getName().equalsIgnoreCase(AUTHORIZER_ROLE_NAME)
        || roleDTO.getName().equalsIgnoreCase(INITIATOR_ROLE_NAME)) {
      rolePermissions = updateRolePermission(roleDTO);
    } else {
      rolePermissions = roleDTO.getPermissions().stream().map(PermissionDTO::getName).toList();
    }
    Set<String> consolidatedPermissions = new HashSet<>(rolePermissions);
    return consolidatedPermissions.stream()
        .map(SimpleGrantedAuthority::new)
        .collect(Collectors.toSet());
  }

  private List<String> updateRolePermission(RoleDTO roleDTO) {
    Permissions permissions = getPermissions();
    RoleCreationDTO roleCreationDTO = new RoleCreationDTO();
    roleCreationDTO.setName(roleDTO.getName());
    roleCreationDTO.setDescription(roleDTO.getDescription());
    if (CUSTODIAN_ROLE_NAME.equalsIgnoreCase(roleDTO.getName())) {
      roleCreationDTO.setPermissions(permissions.existingMaxPermissions);
      roleCreationDTO.setMax(true);
    } else {
      roleCreationDTO.setPermissions(
          AUTHORIZER_ROLE_NAME.equalsIgnoreCase(roleDTO.getName())
              ? permissions.existingCheckerPermissions
              : permissions.existingMakerPermissions);
      roleCreationDTO.setMax(false);
    }

    roleCreationDTO.setSystemInitiated(true);
    customerUserRoleService.editRole(roleCreationDTO);
    return roleCreationDTO.getPermissions().stream().toList();
  }

  private record Permissions(
      Set<String> existingMakerPermissions,
      Set<String> existingCheckerPermissions,
      Set<String> existingMaxPermissions) {}

  private Permissions getPermissions() {
    Set<PermissionDTO> existingPermissions =
        customerUserPermissionService.retrieveSystemPermissions();
    Set<String> existingViewerPermissions =
        existingPermissions.stream()
            .map(PermissionDTO::getName)
            .filter(name -> name.startsWith(VIEW_PERMISSION_PREFIX))
            .collect(Collectors.toSet());
    Set<String> existingMakerPermissions =
        existingPermissions.stream()
            .map(PermissionDTO::getName)
            .filter(name -> !name.startsWith(APPROVE_PERMISSION_PREFIX))
            .collect(Collectors.toSet());
    existingMakerPermissions.remove("treat-requests");
    Set<String> existingCheckerPermissions =
        existingPermissions.stream()
            .map(PermissionDTO::getName)
            .filter(name -> name.startsWith(APPROVE_PERMISSION_PREFIX))
            .collect(Collectors.toSet());
    existingCheckerPermissions.add("treat-requests");
    existingCheckerPermissions.addAll(existingViewerPermissions);
    return new Permissions(
        existingMakerPermissions,
        existingCheckerPermissions,
        existingPermissions.stream().map(PermissionDTO::getName).collect(Collectors.toSet()));
  }

  private String getPhishingImage(CustomerPhishingImage customerPhishingImage) {
    Optional<CustomerUploadedDocument> optionalCustomerUploadedDocument =
        customerDocumentRepository.findFirstByOrganizationIdOrderByCreatedDateDesc(null);
    if (optionalCustomerUploadedDocument.isPresent()) {
      CustomerUploadedDocument customerUploadedDocument = optionalCustomerUploadedDocument.get();
      List<FileUploadedDTO> fileUploadedDTOS = new ArrayList<>();

      if (!ClientUtil.nullOrEmpty(customerUploadedDocument.getCustomerDocuments())) {
        fileUploadedDTOS =
            ClientUtil.getGsonMapper()
                .fromJson(
                    customerUploadedDocument.getCustomerDocuments(),
                    new TypeToken<List<FileUploadedDTO>>() {}.getType());
      }

      Optional<FileUploadedDTO> fileUploadedDTOOptional =
          fileUploadedDTOS.stream()
              .filter(
                  fileUploadedDTO ->
                      fileUploadedDTO
                          .getFileId()
                          .equalsIgnoreCase(customerPhishingImage.getFileId()))
              .findFirst();

      if (fileUploadedDTOOptional.isPresent()) {
        String filePath = fileUploadedDTOOptional.get().getFilePath();
        try {
          return ImageBase64Util.getImageAsBase64(filePath);
        } catch (Exception e) {
          log.error("Error retrieving phishing image {}", e.getMessage());
        }
      }
    }
    return null;
  }
}
