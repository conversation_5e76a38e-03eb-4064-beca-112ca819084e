/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.onboarding.repository;

import com.digicore.lucid.customer.data.modules.onboarding.model.CustomerSecurityQuestion;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * <AUTHOR> <PERSON>
 * @createdOn Jun-11(Wed)-2025
 */

public interface CustomerSecurityQuestionRepository
    extends JpaRepository<CustomerSecurityQuestion, Long> {
  Optional<CustomerSecurityQuestion>
      findFirstByAccountNumberAndIsDeletedFalseOrderByCreatedDateDesc(String accountNumber);

  Optional<CustomerSecurityQuestion>
      findFirstByUsernameAndBankOrganizationIdAndOrganizationIdAndIsDeletedFalseOrderByCreatedDateDesc(
          String username, String bankOrganizationId, String organizationId);
}
