/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.employee.repository;

import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.CUSTOMER_EMPLOYEE_DTO;

import com.digicore.lucid.customer.data.modules.employee.dto.CustomerEmployeeDTO;
import com.digicore.lucid.customer.data.modules.employee.model.CustomerEmployee;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/*
 * <AUTHOR>
 * @createdOn 11/03/2025
 */

@Repository
public interface CustomerEmployeeRepository extends JpaRepository<CustomerEmployee, Long> {

  @Query(
      "SELECT new "
          + CUSTOMER_EMPLOYEE_DTO
          + "(ce.employeeId, ce.firstName, ce.lastName, ce.email, ce.phoneNumber, \n"
          + "            ce.pfaName, ce.pfaCode, ce.tin, \n"
          + "            ce.minorSalaryAmount, ce.minorTaxAmount, ce.minorPensionAmount, \n"
          + "            ce.accountNumber, ce.accountProviderName, ce.accountProviderCode, \n"
          + "            ce.validatedAccountName) "
          + "FROM CustomerEmployee ce WHERE ce.employeeId = :employeeId AND ce.customerProfile.organizationId = :organizationId AND ce.isDeleted = false")
  Optional<CustomerEmployeeDTO> findByEmployeeIdAndOrganizationId(
      String employeeId, String organizationId);

  @Query(
      "SELECT new "
          + CUSTOMER_EMPLOYEE_DTO
          + "(ce.employeeId, ce.firstName, ce.lastName, ce.email, ce.phoneNumber, \n"
          + "            ce.pfaName, ce.pfaCode, ce.tin, \n"
          + "            ce.minorSalaryAmount, ce.minorTaxAmount, ce.minorPensionAmount, \n"
          + "            ce.accountNumber, ce.accountProviderName, ce.accountProviderCode, \n"
          + "            ce.validatedAccountName) "
          + "FROM CustomerEmployee ce WHERE ce.customerProfile.organizationId = :organizationId AND ce.isDeleted = false")
  Page<CustomerEmployeeDTO> findByAllByOrganizationId(String organizationId, Pageable pageable);

  boolean existsByEmployeeIdAndIsDeletedFalse(String employeeId);

  Optional<CustomerEmployee> findFirstBySystemIdAndCustomerProfileOrganizationIdAndIsDeletedFalse(
      String employeeId, String organizationId);

  Optional<CustomerEmployee> findFirstByEmployeeIdAndCustomerProfileOrganizationIdAndIsDeletedFalse(
      String employeeId, String organizationId);

  boolean existsByAccountNumberAndCustomerProfileOrganizationIdAndIsDeletedFalse(
      String accountNumber, String organizationId);
}
