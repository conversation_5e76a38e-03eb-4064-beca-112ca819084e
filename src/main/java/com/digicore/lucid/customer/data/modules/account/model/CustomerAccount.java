/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.account.model;

import com.digicore.lucid.customer.data.modules.limit.model.CustomerLimitConfig;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerProfile;
import com.digicore.registhentication.registration.models.BaseModel;
import jakarta.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/*
 * <AUTHOR>
 * @createdOn Feb-20(Thu)-2025
 */
@Entity
@Table(name = "customer_account")
@Getter
@Setter
@ToString
public class CustomerAccount extends BaseModel implements Serializable {
  private String accountNumber;
  private String accountName;
  private String accountType;
  private String currency;
  private boolean active;
  private boolean viewable;

  @OneToMany(mappedBy = "customerAccount", cascade = CascadeType.ALL)
  @ToString.Exclude
  private Set<CustomerLimitConfig> customerLimitConfigs = new HashSet<>();

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "customer_profile_id")
  private CustomerProfile customerProfile;
}
