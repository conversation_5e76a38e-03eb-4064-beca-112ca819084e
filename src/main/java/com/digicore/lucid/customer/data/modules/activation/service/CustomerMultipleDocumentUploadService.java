/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.activation.service;

import static com.digicore.lucid.common.lib.constant.file.FileConstant.CUSTOMER_UPLOAD_PARENT_DIRECTORY;

import com.digicore.lucid.common.lib.activation.dto.CustomerDocumentUploadDTO;
import com.digicore.lucid.common.lib.activation.dto.FileUploadedDTO;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.util.FileUtil;
import com.digicore.lucid.customer.data.modules.activation.model.CustomerUploadedDocument;
import com.digicore.lucid.customer.data.modules.activation.repository.CustomerDocumentRepository;
import com.digicore.registhentication.registration.services.DocumentUploadService;
import com.google.gson.reflect.TypeToken;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-17(Mon)-2025
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerMultipleDocumentUploadService
    implements DocumentUploadService<List<FileUploadedDTO>, CustomerDocumentUploadDTO> {
  private final FileUtil fileUtil;
  private final CustomerDocumentRepository customerDocumentRepository;

  public List<FileUploadedDTO> uploadMultipleDocument(
      CustomerDocumentUploadDTO customerDocumentUploadDTO) {
    CustomerUploadedDocument customerUploadedDocument =
        customerDocumentRepository
            .findFirstByOrganizationId(RequestContextHolder.get().getOrganizationId())
            .orElse(new CustomerUploadedDocument());

    List<FileUploadedDTO> fileUploadedDTOS = new ArrayList<>();

    if (!ClientUtil.nullOrEmpty(customerUploadedDocument.getCustomerDocuments())) {
      fileUploadedDTOS =
          ClientUtil.getGsonMapper()
              .fromJson(
                  customerUploadedDocument.getCustomerDocuments(),
                  new TypeToken<List<FileUploadedDTO>>() {}.getType());
    }

    for (CustomerDocumentUploadDTO.CustomerDocument customerDocument :
        customerDocumentUploadDTO.getFiles()) {
      MultipartFile multipartFile = (MultipartFile) customerDocument.getFile();
      if (multipartFile != null) {
        String documentType = customerDocument.getDocumentType();
        String email = customerDocument.getEmail();

        // Check if the document type already exists
        Optional<FileUploadedDTO> existingFileOpt =
            fileUploadedDTOS.stream()
                .filter(
                    file ->
                        file.getDocumentType().equalsIgnoreCase(documentType)
                            && file.getEmail().equalsIgnoreCase(email))
                .findFirst();

        List<FileUploadedDTO> finalFileUploadedDTOS = fileUploadedDTOS;
        existingFileOpt.ifPresent(
            existingFile -> {
              // Delete the existing file
              fileUtil.deleteFile(existingFile);
              // Remove from the list
              finalFileUploadedDTOS.remove(existingFile);
            });

        // todo not perfect yet, still needs rework because customer can upload multiple documents
        // of same type for different users
        String fileId = CUSTOMER_UPLOAD_PARENT_DIRECTORY.concat(UUID.randomUUID().toString());
        // Save new file
        //        String fileName =
        //            fileUtil.fileName(
        //                RequestContextHolder.get().getOrganizationId(), LocalDate.now(),
        // multipartFile);
        String pathToFile = fileUtil.saveFile(documentType, multipartFile, fileId);

        // Create new file entry
        FileUploadedDTO newFile = new FileUploadedDTO();
        newFile.setFilePath(pathToFile);
        newFile.setFileId(fileId); // Generate unique file ID
        newFile.setDocumentType(documentType);
        newFile.setEmail(email);

        // Add to list
        fileUploadedDTOS.add(newFile);
      }
    }

    // Update entity and save
    customerUploadedDocument.setOrganizationId(RequestContextHolder.get().getOrganizationId());
    customerUploadedDocument.setBankOrganizationId(
        ClientUtil.getValueFromAccessToken("bankOrganizationId"));
    customerUploadedDocument.setCustomerDocuments(
        ClientUtil.getGsonMapper().toJson(fileUploadedDTOS));
    customerDocumentRepository.save(customerUploadedDocument);
    fileUploadedDTOS.forEach(fileUploadedDTO -> fileUploadedDTO.setFilePath(null));
    return fileUploadedDTOS;
  }
}
