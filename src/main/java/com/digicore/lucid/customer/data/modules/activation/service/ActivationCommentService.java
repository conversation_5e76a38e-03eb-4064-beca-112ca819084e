/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.activation.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;

import com.digicore.lucid.common.lib.activation.dto.CustomerActivationComment;
import com.digicore.lucid.common.lib.activation.service.ActivationService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.activation.model.CustomerActivation;
import com.digicore.lucid.customer.data.modules.activation.repository.CustomerActivationRepository;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.google.gson.reflect.TypeToken;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Feb-18(Tue)-2025
 */

@RequiredArgsConstructor
@Service
public class ActivationCommentService implements ActivationService<CustomerActivationComment> {
  private final CustomerActivationRepository customerActivationRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  @Override
  public List<CustomerActivationComment> retrieveComment(String organizationId) {
    CustomerActivation customerActivation =
        customerActivationRepository
            .findFirstByOrganizationId(organizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getActivationMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    if (!ClientUtil.nullOrEmpty(customerActivation.getComments())) {
      return ClientUtil.getGsonMapper()
          .fromJson(
              customerActivation.getComments(),
              new TypeToken<List<CustomerActivationComment>>() {}.getType());
    }

    return List.of();
  }

  @Override
  public List<CustomerActivationComment> retrieveComment(
      String bankOrganizationId, String organizationId) {
    CustomerActivation customerActivation =
        customerActivationRepository
            .findFirstByBankOrganizationIdAndOrganizationId(bankOrganizationId, organizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getActivationMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    if (!ClientUtil.nullOrEmpty(customerActivation.getComments())) {
      return ClientUtil.getGsonMapper()
          .fromJson(
              customerActivation.getComments(),
              new TypeToken<List<CustomerActivationComment>>() {}.getType());
    }

    return List.of();
  }
}
