/// *
// * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
// * Unauthorized use or distribution is strictly prohibited.
// * For details, see the LICENSE file.
// */
//
// package com.digicore.lucid.customer.data.lib.modules.profile.repository;
//
/// *
// * <AUTHOR> John
// * @createdOn Mar-13(Thu)-2025
// */
//
// import static
// com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.PREFERENCE_DTO;
//
// import com.digicore.lucid.common.lib.profile.dto.PreferenceDTO;
// import com.digicore.lucid.customer.data.lib.modules.profile.model.CustomerUserPreference;
// import java.util.Optional;
// import org.springframework.data.jpa.repository.JpaRepository;
// import org.springframework.data.jpa.repository.Query;
// import org.springframework.data.repository.query.Param;
//
// public interface CustomerUserPreferenceRepository
//    extends JpaRepository<CustomerUserPreference, Long> {
//  Optional<CustomerUserPreference>
//
// findFirstByCustomerUserProfileEmailAndCustomerUserProfileCustomerProfileOrganizationIdAndCustomerUserProfileCustomerProfileBankOrganizationIdAndIsDeletedFalse(
//          String email, String organizationId, String bankOrganizationId);
//
//  boolean
//
// existsByCustomerUserProfileEmailAndCustomerUserProfileCustomerProfileOrganizationIdAndIsDeletedFalse(
//          String email, String organizationId);
//
//  @Query(
//      "SELECT new "
//          + PREFERENCE_DTO
//          + "(a.preferenceConfig) FROM CustomerUserPreference a WHERE a.customerUserProfile.email
// = :email AND a.customerUserProfile.customerProfile.organizationId = :organizationId")
//  Optional<PreferenceDTO> findFirstByCustomerUserProfileEmailAndOrganizationId(
//      @Param("email") String email, @Param("organizationId") String organizationId);
//
//  Optional<CustomerUserPreference>
//
// findFirstByCustomerUserProfileEmailAndCustomerUserProfileCustomerProfileOrganizationIdAndIsDeletedFalse(
//          String email, String organizationId);
// }
