/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.profile.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn 24/02/2025
 */

@Getter
@Setter
@NoArgsConstructor
public class CustomerProfileDetailsDTO {
  private String organizationId;
  private String cbaOrganizationId;

  public CustomerProfileDetailsDTO(String organizationId, String cbaOrganizationId) {
    this.organizationId = organizationId;
    this.cbaOrganizationId = cbaOrganizationId;
  }
}
