/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.onboarding.validation.impl;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.api.helper.response.ApiError;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.customer.data.config.SecurityQuestionsProperties;
import com.digicore.lucid.customer.data.config.ValidationConfigProperties;
import com.digicore.lucid.customer.data.modules.onboarding.dto.CustomerOnboardingFlowDTO;
import com.digicore.lucid.customer.data.modules.onboarding.model.CustomerFlow;
import com.digicore.lucid.customer.data.modules.onboarding.model.CustomerSecurityQuestion;
import com.digicore.lucid.customer.data.modules.onboarding.repository.CustomerFlowRepository;
import com.digicore.lucid.customer.data.modules.onboarding.repository.CustomerSecurityQuestionRepository;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationAction;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationService;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationServiceRegistry;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationType;
import com.digicore.lucid.customer.data.modules.onboarding.validation.util.ValidationUtil;
import com.digicore.lucid.integration.lib.modules.config.properties.SecurityPropertyConfig;
import com.digicore.lucid.integration.lib.modules.util.AESUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.digicore.lucid.customer.data.modules.onboarding.validation.util.ValidationUtil.setNextFlow;

/**
 * <AUTHOR> Ucheagwu
 * @createdOn Jun-10(Tue)-2025
 */
@Service
@Qualifier("SECURITY_QUESTION") @RequiredArgsConstructor
public class SecurityQuestionValidationServiceImpl implements ValidationService {

  private final CustomerFlowRepository customerFlowRepository;
  private final ValidationConfigProperties validationConfigProperties;
  private final SecurityPropertyConfig securityPropertyConfig;
  private final SecurityQuestionsProperties securityQuestionsProperties;
  private final CustomerSecurityQuestionRepository customerSecurityQuestionRepository;
  private final ValidationServiceRegistry validationServiceRegistry;

  @Override
  public Object process(
      CustomerOnboardingFlowDTO customerOnboardingFlowDTO,
      String subDomain,
      String action,
      String validationTypes) {
    CustomerFlow customerFlow =
        ValidationUtil.getCustomerFlow(
            customerFlowRepository, customerOnboardingFlowDTO.getSessionId());
    ValidationUtil.ensureOnboardingNotCompleted(customerFlow);

    if (!action.equalsIgnoreCase(ValidationAction.INITIATE.getAction())) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST,
          List.of(new ApiError("Invalid action for security question validation")));
    }

    if (customerOnboardingFlowDTO.getSecurityQuestion() == null
        || customerOnboardingFlowDTO.getSecurityQuestion().isEmpty()) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST,
          List.of(new ApiError("Security question and answer is required")));
    }

    ValidationConfigProperties.ValidationConfig validationConfig =
        validationConfigProperties.getConfigBySubDomain(subDomain);
    Integer securityQuestionSize =
        validationConfig.getValidationType().stream()
            .filter(v -> v.getType().equalsIgnoreCase(ValidationType.SECURITY_QUESTION.getType()))
            .map(ValidationConfigProperties.ValidationType::getRequiredSize)
            .findFirst()
            .orElse(2);

    if (customerOnboardingFlowDTO.getSecurityQuestion().size() != securityQuestionSize) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Invalid number of security questions")));
    }

    // Check security questions exists
    List<String> questions =
        checkSecurityQuestionExists(
            customerOnboardingFlowDTO.getSecurityQuestion(), securityQuestionSize);

    // Check security question answers are not null and the same
    List<String> hashedAnswers =
        checkSecurityQuestionAnswers(
            customerOnboardingFlowDTO.getSecurityQuestion(), securityQuestionSize);

    // Save security questions and answers
    saveSecurityQuestions(
        customerOnboardingFlowDTO.getAccountNumber(),
        questions,
        hashedAnswers,
        customerFlow.getEmail());

    customerFlow =
        setNextFlow(customerFlowRepository, customerFlow, ValidationType.SECURITY_QUESTION, null);

    // Return response without calling next validation process to avoid nested transactions
    return OnboardingResponseDTO.builder()
        .sessionId(customerFlow.getSessionId())
        .currentFlow(ValidationType.SECURITY_QUESTION.getType().concat(".").concat(ValidationAction.INITIATE.getAction()))
        .message("Security questions saved successfully.")
        .nextFlow(customerFlow.getNextStep())
        .build();
  }

  private void saveSecurityQuestions(
      String accountNumber, List<String> questions, List<String> hashAnswers, String email) {
    if (questions.size() != hashAnswers.size()) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST,
          List.of(new ApiError("Security question and answers should be the same size")));
    }

    CustomerSecurityQuestion customerSecurityQuestion = new CustomerSecurityQuestion();
    customerSecurityQuestion.setAccountNumber(accountNumber);
    customerSecurityQuestion.setEmail(email);
    customerSecurityQuestion.setBankOrganizationId(
        RequestContextHolder.get().getBankOrganizationId());
    for (int i = 0; i < questions.size(); i++) {
      String question = questions.get(i);
      String answer = hashAnswers.get(i);
      if (i == 0) {
        customerSecurityQuestion.setQuestionOne(question);
        customerSecurityQuestion.setHashedAnswerOne(answer);
      }
      if (i == 1) {
        customerSecurityQuestion.setQuestionTwo(question);
        customerSecurityQuestion.setHashedAnswerTwo(answer);
      }
      if (i == 2) {
        customerSecurityQuestion.setQuestionThree(question);
        customerSecurityQuestion.setHashedAnswerThree(answer);
      }
      if (i == 3) {
        customerSecurityQuestion.setQuestionFour(question);
        customerSecurityQuestion.setHashedAnswerFour(answer);
      }
      if (i == 4) {
        customerSecurityQuestion.setQuestionFive(question);
        customerSecurityQuestion.setHashedAnswerFive(answer);
      }
    }
    customerSecurityQuestionRepository.saveAndFlush(customerSecurityQuestion);
  }

  private List<String> checkSecurityQuestionAnswers(
      List<CustomerOnboardingFlowDTO.SecurityQuestion> securityQuestions,
      Integer securityQuestionSize) {
    // Validate if security questions are the same

    List<String> listOfSecurityAnswer =
        securityQuestions.stream()
            .map(CustomerOnboardingFlowDTO.SecurityQuestion::getAnswer)
            .toList();

    Set<String> setOfSecurityAnswer = new HashSet<>(listOfSecurityAnswer);

    // Validate if security question answers are not null
    if (setOfSecurityAnswer.size() != securityQuestionSize) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Security answers must be unique")));
    }

    List<String> hashedAnswers = new ArrayList<>();

    for (var answer : listOfSecurityAnswer) {
      hashedAnswers.add(
          AESUtil.encrypt(answer.toLowerCase(), securityPropertyConfig.getSystemKey()));
    }

    return hashedAnswers;
  }

  private List<String> checkSecurityQuestionExists(
      List<CustomerOnboardingFlowDTO.SecurityQuestion> securityQuestions,
      Integer securityQuestionSize) {

    // Validate if security questions are the same
    List<String> listOfSecurityQuestions =
        securityQuestions.stream()
            .map(CustomerOnboardingFlowDTO.SecurityQuestion::getQuestion)
            .toList();
    Set<String> setOfSecurityQuestions = new HashSet<>(listOfSecurityQuestions);
    // Validate if security question answers are not null
    if (setOfSecurityQuestions.size() != securityQuestionSize) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST,
          List.of(new ApiError("Security questions must be " + securityQuestionSize)));
    }
    // Fetch security questions from source DB or config
    List<String> allQuestions = securityQuestionsProperties.getAllQuestions();

    // Validate if security question exists from source
    if (!new HashSet<>(allQuestions).containsAll(setOfSecurityQuestions)) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST,
          List.of(new ApiError("Use only system defined security questions")));
    }

    return listOfSecurityQuestions;
  }
}
