/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.profile.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.INVALID;
import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;

import com.digicore.lucid.common.lib.activation.dto.FileDownloadDTO;
import com.digicore.lucid.common.lib.activation.dto.FileUploadedDTO;
import com.digicore.lucid.common.lib.profile.service.DocumentService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.util.FileUtil;
import com.digicore.lucid.customer.data.modules.activation.model.CustomerUploadedDocument;
import com.digicore.lucid.customer.data.modules.activation.repository.CustomerDocumentRepository;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.google.gson.reflect.TypeToken;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Jun-25(Wed)-2025
 */

@Service
@RequiredArgsConstructor
public class CustomerDocumentService implements DocumentService<FileDownloadDTO> {
  private final CustomerDocumentRepository customerDocumentRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final FileUtil fileUtil;

  @Override
  public FileDownloadDTO retrieveDocument(String fileId, String organizationId) {
    FileUploadedDTO matchingDocument =
        getFileUploadedDTO(
            customerDocumentRepository.findFirstByOrganizationId(organizationId), fileId);

    FileDownloadDTO fileDownloadDTO = new FileDownloadDTO();
    try {
      byte[] fileBytes = fileUtil.getSavedFile(matchingDocument);

      fileDownloadDTO.setFileContent(fileBytes);
      fileDownloadDTO.setFileName(matchingDocument.getFileId());
      fileDownloadDTO.setDocumentType(matchingDocument.getDocumentType());

      return fileDownloadDTO;
    } catch (Exception e) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getDocumentMessage(INVALID), HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private FileUploadedDTO getFileUploadedDTO(
      Optional<CustomerUploadedDocument> customerDocumentRepository, String fileId) {
    CustomerUploadedDocument customerUploadedDocument =
        customerDocumentRepository.orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getDocumentMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));

    List<FileUploadedDTO> fileUploadedDTOS = new ArrayList<>();

    if (!ClientUtil.nullOrEmpty(customerUploadedDocument.getCustomerDocuments())) {
      fileUploadedDTOS =
          ClientUtil.getGsonMapper()
              .fromJson(
                  customerUploadedDocument.getCustomerDocuments(),
                  new TypeToken<List<FileUploadedDTO>>() {}.getType());
    }

    FileUploadedDTO matchingDocument =
        fileUploadedDTOS.stream()
            .filter(doc -> doc.getFileId().equalsIgnoreCase(fileId))
            .findFirst()
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getDocumentMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    return matchingDocument;
  }
}
