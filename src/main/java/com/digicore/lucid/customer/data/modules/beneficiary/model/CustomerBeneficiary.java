/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.beneficiary.model;

import com.digicore.lucid.common.lib.beneficiary.model.BeneficiaryBaseModel;
import com.digicore.lucid.common.lib.util.RegistrationUtil;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerProfile;
import jakarta.persistence.*;
import lombok.*;

/*
 * <AUTHOR>
 * @createdOn 11/03/2025
 */

@Entity
@Table(name = "customer_beneficiary")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CustomerBeneficiary extends BeneficiaryBaseModel {
  @Column(nullable = false)
  private String beneficiaryId;

  private String firstName;
  private String lastName;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "customer_profile_id")
  private CustomerProfile customerProfile;

  private boolean sameBank;

  @PrePersist
  public void generateBeneficiaryId() {
    setBeneficiaryId("b".concat(RegistrationUtil.generateProfileId()));
  }
}
