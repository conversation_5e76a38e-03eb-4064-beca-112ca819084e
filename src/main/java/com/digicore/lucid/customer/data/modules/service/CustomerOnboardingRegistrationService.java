/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.INVALID;
import static com.digicore.lucid.common.lib.constant.message.MessagePlaceHolderConstant.ROLE_NAME;
import static com.digicore.lucid.common.lib.constant.system.SystemConstant.*;
import static com.digicore.lucid.common.lib.util.RegistrationUtil.getUserProfileDTO;

import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.authorization.dto.RoleCreationDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleDTO;
import com.digicore.lucid.common.lib.authorization.service.RoleService;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.UserRegistrationDTO;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerPersonalDetailDTO;
import com.digicore.lucid.common.lib.registration.enums.SignUpType;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerProfile;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerUserProfile;
import com.digicore.lucid.customer.data.modules.profile.repository.CustomerProfileRepository;
import com.digicore.lucid.customer.data.modules.profile.repository.CustomerUserProfileRepository;
import com.digicore.lucid.customer.data.modules.util.CustomerProfileUtil;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.services.RegistrationService;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-11(Tue)-2025
 */

@Service
@RequiredArgsConstructor
@Transactional
public class CustomerOnboardingRegistrationService
    implements RegistrationService<UserProfileDTO, CustomerPersonalDetailDTO> {
  private final CustomerUserProfileRepository customerUserProfileRepository;
  private final RoleService<RoleDTO, RoleCreationDTO> customerUserRoleService;
  private final AuthProfileService<UserAuthProfileDTO> customerUserAuthProfileService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final CustomerProfileRepository customerProfileRepository;

  @Override
  public UserProfileDTO createProfile(CustomerPersonalDetailDTO customerPersonalDetailDTO) {
    createRole(
        getRoleName(customerPersonalDetailDTO.getSignUpType()),
        customerPersonalDetailDTO.getOrganizationId());
    customerPersonalDetailDTO.setSystemInitiated(true);
    UserRegistrationDTO userRegistrationDTO = new UserRegistrationDTO();
    userRegistrationDTO.setAssignedRole(getRoleName(customerPersonalDetailDTO.getSignUpType()));
    CustomerUserProfile customerUserProfile = createcustomerUserProfile(customerPersonalDetailDTO);
    BeanUtilWrapper.copyNonNullProperties(customerPersonalDetailDTO, userRegistrationDTO);
    customerUserAuthProfileService.saveNewAuthProfile(userRegistrationDTO, customerUserProfile);
    UserProfileDTO userProfileDTO = getUserProfileDTO(customerPersonalDetailDTO);
    userProfileDTO.setSubDomain(customerUserProfile.getCustomerProfile().getSubDomainName());
    return userProfileDTO;
  }

  @Override
  public boolean profileExistenceCheckByEmail(String email) {
    return customerUserProfileRepository.existsByEmail(email);
  }

  @Override
  public boolean profileCheck(String email, String companyName, String bankOrganizationId) {
    return customerUserProfileRepository
        .existsByEmailAndIsDeletedFalseAndCustomerProfileOrganizationNameAndCustomerProfileBankOrganizationId(
            email, companyName, bankOrganizationId);
  }

  private CustomerUserProfile createcustomerUserProfile(
      CustomerPersonalDetailDTO registrationRequest) {
    CustomerUserProfile customerUserProfile = new CustomerUserProfile();
    BeanUtilWrapper.copyNonNullProperties(registrationRequest, customerUserProfile);
    CustomerProfile customerProfile =
        CustomerProfileUtil.getCustomerProfile(
            registrationRequest.getOrganizationId(),
            customerProfileRepository,
            exceptionHandler,
            messagePropertyConfig);
    customerUserProfile.setCustomerProfile(customerProfile);
    customerUserProfile.setBvnValidated(true);
    customerUserProfileRepository.save(customerUserProfile);
    customerUserProfile.setReferralCode(
        "REF-".concat(customerUserProfile.getProfileId()).toUpperCase());
    return customerUserProfile;
  }

  private String getRoleName(SignUpType signUpType) {
    return SignUpType.COMPANY_REP.equals(signUpType) ? INITIATOR_ROLE_NAME : CUSTODIAN_ROLE_NAME;
  }

  private void createRole(String roleName, String organizationId) {
    if (!roleName.equalsIgnoreCase(INITIATOR_ROLE_NAME)
        && !roleName.equalsIgnoreCase(CUSTODIAN_ROLE_NAME)
        && !roleName.equalsIgnoreCase(AUTHORIZER_ROLE_NAME)) {

      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getRoleMessage(INVALID).replace(ROLE_NAME, roleName),
          HttpStatus.BAD_REQUEST);
    }
    if (customerUserRoleService.roleExists(roleName, organizationId)) return;
    // Create/Update admin role
    RoleCreationDTO roleCreationDTO = new RoleCreationDTO();
    roleCreationDTO.setName(roleName);
    roleCreationDTO.setDescription(SIGNUP_ROLE_DESCRIPTION);
    roleCreationDTO.setPermissions(null);
    roleCreationDTO.setSystemInitiated(true);
    roleCreationDTO.setOrganizationId(organizationId);
    customerUserRoleService.createRole(roleCreationDTO);
  }
}
