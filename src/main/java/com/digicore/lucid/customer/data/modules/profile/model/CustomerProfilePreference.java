/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.profile.model;

import com.digicore.registhentication.registration.models.Auditable;
import jakarta.persistence.*;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/*
 * <AUTHOR>
 * @createdOn Mar-15(Sat)-2025
 */

@Entity
@Table(name = "customer_profile_preference")
@Getter
@Setter
@ToString
public class CustomerProfilePreference extends Auditable<String> implements Serializable {
  @Column(columnDefinition = "text")
  private String preferenceConfig;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "customer_profile_id")
  private CustomerProfile customerProfile;
}
