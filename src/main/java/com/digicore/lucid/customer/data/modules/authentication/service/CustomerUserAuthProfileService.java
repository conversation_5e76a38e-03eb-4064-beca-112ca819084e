/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.authentication.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;
import static com.digicore.lucid.common.lib.constant.message.MessagePlaceHolderConstant.EMAIL;
import static com.digicore.lucid.common.lib.constant.message.MessagePlaceHolderConstant.ROLE_NAME;

import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.authorization.dto.PermissionDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleCreationDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleDTO;
import com.digicore.lucid.common.lib.authorization.service.PermissionService;
import com.digicore.lucid.common.lib.authorization.service.RoleService;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.profile.dto.UserProfileProjection;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.UserRegistrationDTO;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.authentication.model.CustomerUserAuthProfile;
import com.digicore.lucid.customer.data.modules.authentication.repository.CustomerUserAuthProfileRepository;
import com.digicore.lucid.customer.data.modules.authorization.model.CustomerUserRole;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerUserProfile;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.enums.Status;
import jakarta.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-11(Tue)-2025
 */

@Service
@RequiredArgsConstructor
@Transactional
public class CustomerUserAuthProfileService implements AuthProfileService<UserAuthProfileDTO> {
  private final RoleService<RoleDTO, RoleCreationDTO> customerUserRoleService;
  private final PermissionService<PermissionDTO> customerUserPermissionService;
  private final CustomerUserAuthProfileRepository customerUserAuthProfileRepository;
  private final PasswordEncoder passwordEncoder;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  @Override
  public <V, K> void saveNewAuthProfile(K registrationDTO, V userProfile) {
    UserRegistrationDTO registrationRequest = (UserRegistrationDTO) registrationDTO;
    CustomerUserRole customerUserRole =
        customerUserRoleService.retrieveRoleEntity(
            registrationRequest.getAssignedRole(), registrationRequest.getOrganizationId());

    CustomerUserProfile customerUserProfile = (CustomerUserProfile) userProfile;
    doProfileCheck(registrationRequest.getUsername(), registrationRequest.getOrganizationId());
    CustomerUserAuthProfile customerUserAuthProfile = new CustomerUserAuthProfile();
    BeanUtilWrapper.copyNonNullProperties(registrationRequest, customerUserAuthProfile);
    customerUserAuthProfile.setCustomerUserProfile(customerUserProfile);
    customerUserAuthProfile.setPin("N/A");
    customerUserAuthProfile.setPassword(passwordEncoder.encode(registrationRequest.getPassword()));
    customerUserAuthProfile.setDefaultPassword(!registrationRequest.isSystemInitiated());
    customerUserAuthProfile.setStatus(
        registrationRequest.isSystemInitiated()
            ? Status.PENDING
            : Status.PENDING_INVITE_ACCEPTANCE);
    customerUserAuthProfile.setAssignedRole(customerUserRole.getName());
    //    customerUserAuthProfile.setPermissions(new HashSet<>(customerUserRole.getPermissions()));
    customerUserAuthProfileRepository.save(customerUserAuthProfile);
  }

  @Override
  public <V, K> void saveNewAuthProfile(List<K> registrationDTO, List<V> userProfile) {
    List<UserRegistrationDTO> registrationRequests = (List<UserRegistrationDTO>) registrationDTO;
    List<CustomerUserProfile> customerUserProfiles = (List<CustomerUserProfile>) userProfile;
    Map<String, CustomerUserProfile> profileMap =
        customerUserProfiles.stream()
            .map(p -> Map.entry(p.getEmail(), p))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    List<CustomerUserAuthProfile> customerUserAuthProfiles = new ArrayList<>();
    registrationRequests.forEach(
        request -> {
          CustomerUserRole customerUserRole =
              customerUserRoleService.retrieveRoleEntity(
                  request.getAssignedRole(), request.getOrganizationId());
          doProfileCheck(request.getUsername(), request.getOrganizationId());
          CustomerUserAuthProfile customerUserAuthProfile = new CustomerUserAuthProfile();
          BeanUtilWrapper.copyNonNullProperties(request, customerUserAuthProfile);
          customerUserAuthProfile.setCustomerUserProfile(profileMap.get(request.getEmail()));
          customerUserAuthProfile.setPin("N/A");
          customerUserAuthProfile.setPassword(passwordEncoder.encode(request.getPassword()));
          customerUserAuthProfile.setDefaultPassword(true);
          customerUserAuthProfile.setStatus(Status.PENDING_INVITE_ACCEPTANCE);
          customerUserAuthProfile.setAssignedRole(customerUserRole.getName());
          //          customerUserAuthProfile.setPermissions(new
          // HashSet<>(customerUserRole.getPermissions()));
          customerUserAuthProfiles.add(customerUserAuthProfile);
        });
    customerUserAuthProfileRepository.saveAll(customerUserAuthProfiles);
  }

  @Override
  public UserAuthProfileDTO retrieveAuthProfile(String username, boolean isForPasswordRest) {
    if (isForPasswordRest) {
      UserAuthProfileDTO userAuthProfileDTO = new UserAuthProfileDTO();
      UserProfileProjection customerUserAuthProfile =
          customerUserAuthProfileRepository
              .findFirstByUsername(username, RequestContextHolder.get().getOrganizationId())
              .orElseThrow(
                  () ->
                      exceptionHandler.processCustomException(
                          messagePropertyConfig.getUserMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
      userAuthProfileDTO.setUsername(username);
      userAuthProfileDTO.setStatus(customerUserAuthProfile.getStatus());
      return userAuthProfileDTO;
    }
    CustomerUserAuthProfile customerUserAuthProfile =
        customerUserAuthProfileRepository
            .findFirstByUsernameAndIsDeletedFalseAndCustomerUserProfileCustomerProfileOrganizationIdOrderByCreatedDate(
                username, RequestContextHolder.get().getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getUserMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
    return mapCustomerUserAuthProfileEntityToDTO(customerUserAuthProfile);
  }

  @Override
  public UserAuthProfileDTO retrieveAuthProfile(String userName, String organizationId) {
    CustomerUserAuthProfile customerUserAuthProfile =
        customerUserAuthProfileRepository
            .findFirstByUsernameAndIsDeletedFalseAndCustomerUserProfileCustomerProfileOrganizationIdOrderByCreatedDate(
                userName, organizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getUserMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
    return mapCustomerUserAuthProfileEntityToDTO(customerUserAuthProfile, organizationId);
  }

  @Override
  public PaginatedResponseDTO<UserAuthProfileDTO> retrieveUserProfile(
      String organizationId, String bankOrganizationId, int pageNumber, int pageSize) {
    Pageable pageable = PageRequest.of(pageNumber, pageSize, Sort.by("createdDate").descending());
    Page<UserProfileProjection> profiles =
        customerUserAuthProfileRepository.findAllByAndOrganizationIdAndBankOrganizationId(
            bankOrganizationId, organizationId, pageable);
    return PaginatedResponseDTO.<UserAuthProfileDTO>builder()
        .content(
            profiles.getContent().stream()
                .map(this::mapCustomerUserAuthProfileEntityToDTO)
                .toList())
        .currentPage(profiles.getNumber() + 1)
        .totalPages(profiles.getTotalPages())
        .totalItems(profiles.getTotalElements())
        .isFirstPage(profiles.isFirst())
        .isLastPage(profiles.isLast())
        .build();
  }

  @Override
  public UserAuthProfileDTO retrieveUserProfile(
      String userName, String bankOrganizationId, String organizationId) {
    UserProfileProjection customerUserAuthProfile =
        customerUserAuthProfileRepository
            .findFirstByUserNameAndOrganizationIdAndBankOrganizationId(
                userName, organizationId, bankOrganizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getUserMessage(NOT_FOUND).replace(EMAIL, userName),
                        HttpStatus.BAD_REQUEST));

    return mapCustomerUserAuthProfileEntityToDTO(customerUserAuthProfile);
  }

  @Override
  public boolean authProfileExists(String username, String organizationId) {
    return customerUserAuthProfileRepository
        .existsByUsernameAndIsDeletedFalseAndCustomerUserProfileCustomerProfileOrganizationId(
            username, organizationId);
  }

  @Override
  public boolean authProfileExist(String username, String bankOrganizationId) {
    return customerUserAuthProfileRepository
        .existsByUsernameAndIsDeletedFalseAndCustomerUserProfileCustomerProfileBankOrganizationId(
            username, bankOrganizationId);
  }

  private void doProfileCheck(String username, String organizationId) {
    if (authProfileExists(username, organizationId))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getUserMessage(DUPLICATE).replace(EMAIL, username),
          HttpStatus.BAD_REQUEST);
  }

  private UserAuthProfileDTO mapCustomerUserAuthProfileEntityToDTO(
      CustomerUserAuthProfile customerUserAuthProfile) {
    UserAuthProfileDTO userAuthProfileDTO = new UserAuthProfileDTO();
    userAuthProfileDTO.setPermissions(
        customerUserRoleService
            .retrieveRole(customerUserAuthProfile.getAssignedRole())
            .getPermissions());
    BeanUtilWrapper.copyNonNullProperties(customerUserAuthProfile, userAuthProfileDTO);
    UserProfileDTO userProfileDTO = new UserProfileDTO();
    BeanUtilWrapper.copyNonNullProperties(
        customerUserAuthProfile.getCustomerUserProfile(), userProfileDTO);
    userAuthProfileDTO.setUserProfile(userProfileDTO);
    return userAuthProfileDTO;
  }

  private UserAuthProfileDTO mapCustomerUserAuthProfileEntityToDTO(
      CustomerUserAuthProfile customerUserAuthProfile, String organizationId) {
    UserAuthProfileDTO userAuthProfileDTO = new UserAuthProfileDTO();
    userAuthProfileDTO.setPermissions(
        customerUserRoleService
            .retrieveRole(customerUserAuthProfile.getAssignedRole(), organizationId)
            .getPermissions());
    BeanUtilWrapper.copyNonNullProperties(customerUserAuthProfile, userAuthProfileDTO);
    UserProfileDTO userProfileDTO = new UserProfileDTO();
    BeanUtilWrapper.copyNonNullProperties(
        customerUserAuthProfile.getCustomerUserProfile(), userProfileDTO);
    userAuthProfileDTO.setUserProfile(userProfileDTO);
    return userAuthProfileDTO;
  }

  private UserAuthProfileDTO mapCustomerUserAuthProfileEntityToDTO(
      UserProfileProjection userProfileProjection) {
    UserAuthProfileDTO userAuthProfileDTO = new UserAuthProfileDTO();
    UserProfileDTO userProfileDTO = new UserProfileDTO();
    BeanUtilWrapper.copyNonNullProperties(userProfileProjection, userAuthProfileDTO);
    BeanUtilWrapper.copyNonNullProperties(userProfileProjection, userProfileDTO);
    userProfileDTO.setFirstName(userProfileProjection.getFirstName());
    userProfileDTO.setLastName(userProfileProjection.getLastName());
    userProfileDTO.setEmail(userProfileProjection.getEmail());
    userProfileDTO.setPhoneNumber(userProfileProjection.getPhoneNumber());
    userAuthProfileDTO.setUserProfile(userProfileDTO);
    return userAuthProfileDTO;
  }

  @Override
  public void updateAuthProfile(UserAuthProfileDTO authProfile) {
    customerUserRoleService.validateRole(
        authProfile.getAssignedRole(), authProfile.isSystemInitiated(), false);
    customerUserAuthProfileRepository.save(ensureAuthProfileInfoRemainsValid(authProfile));
  }

  @Override
  public void updateUsernameAndRole(
      String username, String newUsername, String assignedRole, String organizationId) {
    if (!customerUserRoleService.roleExists(assignedRole, organizationId))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getRoleMessage(NOT_FOUND).replace(ROLE_NAME, assignedRole),
          HttpStatus.BAD_REQUEST);
    customerUserAuthProfileRepository.updateUsernameAndRole(
        username, newUsername, assignedRole, organizationId);
  }

  @Override
  public void updateAuthProfilePassword(UserAuthProfileDTO authProfile) {
    authProfile.setPassword(passwordEncoder.encode(authProfile.getPassword()));
    customerUserAuthProfileRepository.updatePassword(
        authProfile.getStatus(),
        authProfile.getPassword(),
        authProfile.isDefaultPassword(),
        authProfile.getUsername(),
        RequestContextHolder.get().getOrganizationId());
  }

  @Override
  public void updateAuthProfilePassword(UserAuthProfileDTO authProfile, String organizationId) {
    authProfile.setPassword(passwordEncoder.encode(authProfile.getPassword()));
    customerUserAuthProfileRepository.updatePassword(
        authProfile.getStatus(),
        authProfile.getPassword(),
        authProfile.isDefaultPassword(),
        authProfile.getUsername(),
        organizationId);
  }

  @Override
  public void enableUserProfile(String userName, String organizationId) {
    customerUserAuthProfileRepository.updateStatus(Status.ACTIVE, userName, organizationId);
  }

  @Override
  public void disableUserProfile(String userName, String organizationId) {
    customerUserAuthProfileRepository.updateStatus(Status.DEACTIVATED, userName, organizationId);
  }

  @Override
  public <V> V retrieveAuthProfileEntity(String email) {
    return (V)
        customerUserAuthProfileRepository
            .findFirstByUsernameAndIsDeletedFalseAndCustomerUserProfileCustomerProfileOrganizationIdOrderByCreatedDate(
                email, RequestContextHolder.get().getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getUserMessage(NOT_FOUND).replace(EMAIL, email),
                        HttpStatus.BAD_REQUEST));
  }

  @Override
  public <V> V retrieveAuthProfileEntity(String organizationId, String email) {
    return (V)
        customerUserAuthProfileRepository
            .findFirstByUsernameAndIsDeletedFalseAndCustomerUserProfileCustomerProfileOrganizationIdOrderByCreatedDate(
                email, organizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getUserMessage(NOT_FOUND).replace(EMAIL, email),
                        HttpStatus.BAD_REQUEST));
  }

  private CustomerUserAuthProfile ensureAuthProfileInfoRemainsValid(
      UserAuthProfileDTO authProfile) {
    CustomerUserAuthProfile existingAuthProfile =
        customerUserAuthProfileRepository
            .findFirstByUsernameAndIsDeletedFalseAndCustomerUserProfileCustomerProfileOrganizationIdOrderByCreatedDate(
                authProfile.getUsername(), RequestContextHolder.get().getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig
                            .getUserMessage(NOT_FOUND)
                            .replace(EMAIL, authProfile.getUsername()),
                        HttpStatus.BAD_REQUEST));

    if (!ClientUtil.nullOrEmpty(authProfile.getAssignedRole())
        && (!existingAuthProfile
            .getAssignedRole()
            .equalsIgnoreCase(authProfile.getAssignedRole()))) {
      existingAuthProfile.setAssignedRole(authProfile.getAssignedRole());
    }
    existingAuthProfile.setStatus(authProfile.getStatus());
    existingAuthProfile.setDefaultPassword(authProfile.isDefaultPassword());

    return existingAuthProfile;
  }
}
