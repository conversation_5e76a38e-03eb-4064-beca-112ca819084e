/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.profile.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;

import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.profile.dto.UserEditDTO;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.profile.service.UserProfileService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerUserProfile;
import com.digicore.lucid.customer.data.modules.profile.repository.CustomerUserProfileRepository;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Mar-05(Wed)-2025
 */

@Service
@RequiredArgsConstructor
public class CustomerUserProfileService implements UserProfileService<UserProfileDTO> {
  private final CustomerUserProfileRepository customerUserProfileRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final AuthProfileService<UserAuthProfileDTO> customerUserAuthProfileService;

  private CustomerUserProfile getCustomerUserProfile(String organizationId, String email) {
    return customerUserProfileRepository
        .findFirstByEmailAndIsDeletedFalseAndCustomerProfileOrganizationIdOrderByCreatedDate(
            email, organizationId)
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getUserMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }

  @Override
  public void editUserProfile(UserEditDTO userEditDTO) {
    CustomerUserProfile customerUserProfileToUpdate =
        getCustomerUserProfile(userEditDTO.getOrganizationId(), userEditDTO.getEmail());
    BeanUtilWrapper.copyNonNullProperties(userEditDTO, customerUserProfileToUpdate);
    if (!ClientUtil.nullOrEmpty(userEditDTO.getNewEmail())) {
      customerUserProfileToUpdate.setEmail(userEditDTO.getNewEmail());
    }
    customerUserProfileToUpdate.setReferralCode(customerUserProfileToUpdate.getReferralCode());
    customerUserProfileToUpdate.setProfileId(customerUserProfileToUpdate.getProfileId());
    customerUserProfileRepository.save(customerUserProfileToUpdate);
    if (!ClientUtil.nullOrEmpty(userEditDTO.getNewUsername()))
      customerUserAuthProfileService.updateUsernameAndRole(
          userEditDTO.getUsername(),
          userEditDTO.getNewUsername(),
          userEditDTO.getAssignedRole(),
          userEditDTO.getOrganizationId());
  }

  @Override
  public UserProfileDTO retrieveUserProfile(String userName, String organizationId, String email) {
    CustomerUserProfile customerUserProfile = getCustomerUserProfile(organizationId, email);
    return mapCustomerUserProfileToDTO(customerUserProfile, userName);
  }

  private UserProfileDTO mapCustomerUserProfileToDTO(
      CustomerUserProfile customerUserProfile, String userName) {
    UserProfileDTO profileDto = new UserProfileDTO();
    UserAuthProfileDTO userAuthProfileDTO =
        customerUserAuthProfileService.retrieveAuthProfile(
            userName, customerUserProfile.getCustomerProfile().getOrganizationId());
    BeanUtilWrapper.copyNonNullProperties(customerUserProfile, profileDto);
    profileDto.setUsername(customerUserProfile.getEmail());
    profileDto.setAssignedRole(userAuthProfileDTO.getAssignedRole());
    profileDto.setStatus(userAuthProfileDTO.getStatus());
    profileDto.setPassword(null);
    profileDto.setUserPermissions(userAuthProfileDTO.getPermissions());
    profileDto.setPin(null);
    profileDto.setSubDomain(customerUserProfile.getCustomerProfile().getSubDomainName());
    profileDto.setPhoneNumber(
        ClientUtil.nullOrEmpty(customerUserProfile.getPhoneNumber())
            ? "N/S"
            : customerUserProfile.getPhoneNumber());

    return profileDto;
  }

  @Override
  public UserProfileDTO retrieveLoggedInUserProfile() {
    UserAuthProfileDTO userAuthProfileDTO =
        customerUserAuthProfileService.retrieveUserProfile(
            ClientUtil.getLoggedInUsername(),
            RequestContextHolder.get().getBankOrganizationId(),
            RequestContextHolder.get().getOrganizationId());
    return getUserProfileDTO(retrieveProfile(userAuthProfileDTO.getEmail()), userAuthProfileDTO);
  }

  private CustomerUserProfile retrieveProfile(String email) {
    return customerUserProfileRepository
        .findFirstByEmailAndIsDeletedFalseAndCustomerProfileOrganizationIdOrderByCreatedDate(
            email, RequestContextHolder.get().getOrganizationId())
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getUserMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }

  private UserProfileDTO getUserProfileDTO(
      CustomerUserProfile customerUserProfile, UserAuthProfileDTO userAuthProfileDTO) {
    UserProfileDTO profileDto = new UserProfileDTO();
    BeanUtilWrapper.copyNonNullProperties(customerUserProfile, profileDto);
    profileDto.setUsername(userAuthProfileDTO.getUsername());
    profileDto.setAssignedRole(userAuthProfileDTO.getAssignedRole());
    profileDto.setStatus(userAuthProfileDTO.getStatus());
    profileDto.setPassword(null);
    profileDto.setUserPermissions(userAuthProfileDTO.getPermissions());
    profileDto.setPin(null);
    profileDto.setPhoneNumber(
        ClientUtil.nullOrEmpty(customerUserProfile.getPhoneNumber())
            ? "N/S"
            : customerUserProfile.getPhoneNumber());
    profileDto.setLastLoginDate(userAuthProfileDTO.getLastLoginDate());

    return profileDto;
  }
}
