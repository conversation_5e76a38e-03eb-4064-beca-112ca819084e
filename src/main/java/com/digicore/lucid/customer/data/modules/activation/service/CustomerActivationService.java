/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.activation.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.system.SystemConstant.*;
import static com.digicore.lucid.common.lib.registration.enums.SignUpType.COMPANY_REP;
import static com.digicore.lucid.common.lib.util.ClientUtil.getPageable;
import static com.digicore.lucid.common.lib.util.ClientUtil.nullOrEmpty;

import com.digicore.api.helper.response.ApiError;
import com.digicore.lucid.common.lib.activation.dto.BackOfficeCustomerActivationDTO;
import com.digicore.lucid.common.lib.activation.dto.CustomerActivationComment;
import com.digicore.lucid.common.lib.activation.dto.CustomerActivationDTO;
import com.digicore.lucid.common.lib.activation.dto.CustomerUserDetailDTO;
import com.digicore.lucid.common.lib.activation.enums.CustomerActivationStatus;
import com.digicore.lucid.common.lib.activation.service.ActivationService;
import com.digicore.lucid.common.lib.approval.enums.ApprovalStatus;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerSignatoryInviteRequestDTO;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.util.FieldValidatorUtil;
import com.digicore.lucid.customer.data.modules.activation.model.CustomerActivation;
import com.digicore.lucid.customer.data.modules.activation.repository.CustomerActivationRepository;
import com.digicore.lucid.customer.data.modules.profile.repository.CustomerUserProfileRepository;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.google.gson.reflect.TypeToken;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/**
 * Service class for managing customer activation processes. Provides methods for saving progress,
 * adding signatories, retrieving progress, and processing activations. Implements the
 * ActivationService interface for CustomerActivationDTO.
 *
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-17(Mon)-2025
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerActivationService implements ActivationService<CustomerActivationDTO> {
  private final CustomerActivationRepository customerActivationRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final CustomerProfileActivationService customerProfileActivationService;
  private final CustomerUserProfileRepository customerUserProfileRepository;

  /**
   * Saves the progress of customer activation. Validates the activation data and updates the
   * activation status.
   *
   * @param customerActivationDTO The customer activation data transfer object.
   */
  @Override
  public void saveProgress(CustomerActivationDTO customerActivationDTO) {
    String organizationId = RequestContextHolder.get().getOrganizationId();
    customerActivationDTO.setOrganizationId(organizationId);

    CustomerActivation customerActivation =
        customerActivationRepository
            .findFirstByOrganizationId(organizationId)
            .orElse(new CustomerActivation());

    CustomerActivationStatus status = customerActivation.getCustomerActivationStatus();
    if (status != null) {
      switch (status) {
        case APPROVED -> {
          log.info("Customer {} profile already approved", organizationId);
          return;
        }
        case REVIEWING -> {
          log.info("Customer {} profile is being reviewed", organizationId);
          return;
        }
        case PROCESSING -> {
          log.info("Customer {} profile is being processed", organizationId);
          return;
        }
        case SUBMITTED -> {
          log.info("Customer {} profile is pending review", organizationId);
          return;
        }
      }
    }

    if (customerActivation.getId() == null) {
      customerActivation.setBankOrganizationId(customerActivationDTO.getBankOrganizationId());
      customerActivation.setOrganizationId(organizationId);
    }

    CustomerActivationStatus newStatus = customerActivationDTO.getCustomerActivationStatus();
    boolean isSubmitted = CustomerActivationStatus.SUBMITTED.equals(newStatus);

    customerActivation.setCustomerActivationStatus(
        isSubmitted ? CustomerActivationStatus.REVIEWING : CustomerActivationStatus.IN_PROGRESS);

    if (isSubmitted) {
      validateAssignedRole(customerActivationDTO);
      List<String> unverifiedUsers = retrieveUnValidatedEmail(customerActivationDTO);
      if (!ClientUtil.nullOrEmpty(unverifiedUsers)) {
        exceptionHandler.processCustomExceptions(
            messagePropertyConfig.getActivationMessage(INVALID),
            HttpStatus.BAD_REQUEST,
            unverifiedUsers.stream().map(ApiError::new).toList());
      }
      validateSubmittedActivationData(customerActivationDTO);
      setComment(customerActivationDTO.getComment(), customerActivation);
    }

    customerActivationDTO.setComment(null);
    String activationData = ClientUtil.getGsonMapper().toJson(customerActivationDTO);
    customerActivation.setCustomerData(activationData);
    customerActivationRepository.save(customerActivation);
  }

  /**
   * Adds a signatory to the customer activation. Updates existing user details or adds a new user
   * to the activation data.
   *
   * @param customerUserDetailDTO The customer user detail data transfer object.
   * @param organizationId The organization ID.
   */
  @Override
  public void addSignatory(CustomerUserDetailDTO customerUserDetailDTO, String organizationId) {
    CustomerActivation customerActivation =
        customerActivationRepository
            .findFirstByOrganizationId(organizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getActivationMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));

    CustomerActivationDTO activationData =
        ClientUtil.getGsonMapper()
            .fromJson(customerActivation.getCustomerData(), CustomerActivationDTO.class);

    List<CustomerUserDetailDTO> userDetails = activationData.getCustomerUserDetailDTO();

    Optional<CustomerUserDetailDTO> existingUserOpt =
        userDetails.stream()
            .filter(user -> customerUserDetailDTO.getEmail().equalsIgnoreCase(user.getEmail()))
            .findFirst();

    if (existingUserOpt.isPresent()) {
      // Update the existing user
      BeanUtilWrapper.copyNonNullProperties(customerUserDetailDTO, existingUserOpt.get());
    } else {
      // Add new user
      CustomerUserDetailDTO newUser = new CustomerUserDetailDTO();
      BeanUtilWrapper.copyNonNullProperties(customerUserDetailDTO, newUser);
      userDetails.add(newUser);
    }
    activationData.setCustomerUserDetailDTO(userDetails);
    saveProgress(activationData, organizationId);
  }

  /**
   * Saves the progress of customer activation for a specific organization.
   *
   * @param customerActivationDTO The customer activation data transfer object.
   * @param organizationId The organization ID.
   */
  public void saveProgress(CustomerActivationDTO customerActivationDTO, String organizationId) {
    customerActivationDTO.setOrganizationId(organizationId);
    CustomerActivation customerActivation =
        customerActivationRepository
            .findFirstByOrganizationId(organizationId)
            .orElse(new CustomerActivation());
    if (customerActivation.getCustomerActivationStatus() != null
        && customerActivation
            .getCustomerActivationStatus()
            .equals(CustomerActivationStatus.APPROVED)) {
      log.info("Customer {} profile already approved", organizationId);
      return;
    }
    String activationData = ClientUtil.getGsonMapper().toJson(customerActivationDTO);
    customerActivation.setCustomerData(activationData);
    customerActivationRepository.save(customerActivation);
  }

  /**
   * Saves the progress of customer activation based on approval decision. Updates the activation
   * status and account officer code if provided.
   *
   * @param approvalDecisionDTO The back-office customer activation data transfer object.
   * @return The updated customer activation data transfer object.
   */
  @Override
  public CustomerActivationDTO saveProgress(BackOfficeCustomerActivationDTO approvalDecisionDTO) {
    CustomerActivation customerActivation =
        customerActivationRepository
            .findFirstByOrganizationId(approvalDecisionDTO.getRequestId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getActivationMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));

    if (approvalDecisionDTO.getStatus() != null) {
      if (ApprovalStatus.APPROVED.equals(approvalDecisionDTO.getStatus()))
        customerActivation.setCustomerActivationStatus(CustomerActivationStatus.PROCESSING);
      else customerActivation.setCustomerActivationStatus(CustomerActivationStatus.REJECTED);

      if (!nullOrEmpty(approvalDecisionDTO.getAccountOfficerCode())) {
        CustomerActivationDTO customerActivationDTO =
            ClientUtil.getGsonMapper()
                .fromJson(customerActivation.getCustomerData(), CustomerActivationDTO.class);
        customerActivationDTO.setAccountOfficerCode(approvalDecisionDTO.getAccountOfficerCode());
        String activationData = ClientUtil.getGsonMapper().toJson(customerActivationDTO);
        customerActivation.setCustomerData(activationData);
      }

      setComment(approvalDecisionDTO.getReason(), customerActivation);
      customerActivationRepository.save(customerActivation);
    }
    return ClientUtil.getGsonMapper()
        .fromJson(customerActivation.getCustomerData(), CustomerActivationDTO.class);
  }

  /**
   * Saves the progress of customer activation for a specific organization and officer code.
   *
   * @param organizationId The organization ID.
   * @param officerCode The officer code.
   * @param productCode The product code.
   */
  @Override
  public void saveProgress(String organizationId, String officerCode, String productCode) {
    CustomerActivation customerActivation =
        customerActivationRepository
            .findFirstByOrganizationId(organizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getActivationMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    customerActivationRepository.save(customerActivation);
  }

  /**
   * Saves a signatory to the customer activation. Updates existing user details or adds a new user
   * to the activation data.
   *
   * @param requestDTO The customer signatory invite request data transfer object.
   */
  @Override
  public void saveSignatory(CustomerSignatoryInviteRequestDTO requestDTO) {
    CustomerActivationDTO activationDTO = retrieveProgress(requestDTO.getOrganizationId());

    List<CustomerUserDetailDTO> userDetails = activationDTO.getCustomerUserDetailDTO();

    Optional<CustomerUserDetailDTO> existingUserOpt =
        userDetails.stream()
            .filter(user -> requestDTO.getEmail().equalsIgnoreCase(user.getEmail()))
            .findFirst();

    if (existingUserOpt.isPresent()) {
      // Update the existing user
      BeanUtilWrapper.copyNonNullProperties(requestDTO, existingUserOpt.get());
    } else {
      // Add new user
      CustomerUserDetailDTO newUser = new CustomerUserDetailDTO();
      BeanUtilWrapper.copyNonNullProperties(requestDTO, newUser);
      userDetails.add(newUser);
    }
    activationDTO.setCustomerUserDetailDTO(userDetails);
    saveProgress(activationDTO, requestDTO.getOrganizationId());
  }

  /**
   * Sets a comment for the customer activation. Adds the comment to the list of comments in the
   * activation data.
   *
   * @param comment The comment to add.
   * @param customerActivation The customer activation entity.
   */
  private static void setComment(String comment, CustomerActivation customerActivation) {
    List<CustomerActivationComment> comments =
        ClientUtil.getGsonMapper()
            .fromJson(
                customerActivation.getComments(),
                new TypeToken<List<CustomerActivationComment>>() {}.getType());
    if (ClientUtil.nullOrEmpty(comments)) comments = new ArrayList<>();
    if (!ClientUtil.nullOrEmpty(comment)) {
      comments.add(
          CustomerActivationComment.builder()
              .name(ClientUtil.getValueFromAccessToken("name"))
              .email(ClientUtil.getValueFromAccessToken("email"))
              .comment(comment)
              .createdDate(LocalDateTime.now())
              .build());
      customerActivation.setComments(ClientUtil.getGsonMapper().toJson(comments));
    }
  }

  /**
   * Retrieves the progress of customer activation for a specific organization.
   *
   * @param organizationId The organization ID.
   * @return The customer activation data transfer object.
   */
  @Override
  public CustomerActivationDTO retrieveProgress(String organizationId) {
    CustomerActivation customerActivation =
        customerActivationRepository
            .findFirstByOrganizationId(organizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getActivationMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    CustomerActivationDTO customerActivationDTO =
        ClientUtil.getGsonMapper()
            .fromJson(customerActivation.getCustomerData(), CustomerActivationDTO.class);
    customerActivationDTO.setComment(null);
    customerActivationDTO.setCustomerActivationStatus(
        customerActivation.getCustomerActivationStatus());
    return customerActivationDTO;
  }

  /**
   * Retrieves paginated progress of customer activation for a specific bank organization ID and
   * status.
   *
   * @param bankOrganizationId The bank organization ID.
   * @param customerActivationStatus The customer activation status.
   * @param page The page number.
   * @param size The page size.
   * @return The paginated response containing customer activation data transfer objects.
   */
  @Override
  public PaginatedResponseDTO<CustomerActivationDTO> retrieveProgress(
      String bankOrganizationId,
      CustomerActivationStatus customerActivationStatus,
      int page,
      int size) {
    Page<CustomerActivation> customerActivationPage =
        customerActivationRepository.findAllByBankOrganizationIdAndCustomerActivationStatus(
            bankOrganizationId, customerActivationStatus, getPageable(page, size));
    return PaginatedResponseDTO.<CustomerActivationDTO>builder()
        .content(
            customerActivationPage.getContent().stream()
                .map(
                    customerActivation -> {
                      CustomerActivationDTO customerActivationDTO =
                          ClientUtil.getGsonMapper()
                              .fromJson(
                                  customerActivation.getCustomerData(),
                                  CustomerActivationDTO.class);
                      customerActivationDTO.setComment(null);
                      customerActivationDTO.setCustomerActivationStatus(
                          customerActivation.getCustomerActivationStatus());
                      return customerActivationDTO;
                    })
                .toList())
        .currentPage(customerActivationPage.getNumber() + 1)
        .totalPages(customerActivationPage.getTotalPages())
        .totalItems(customerActivationPage.getTotalElements())
        .isFirstPage(customerActivationPage.isFirst())
        .isLastPage(customerActivationPage.isLast())
        .build();
  }

  /**
   * Processes the activation of a customer. Updates the activation status to APPROVED and delegates
   * further processing to the profile activation service.
   *
   * @param customerActivationDTO The customer activation data transfer object.
   */
  @Override
  public void processActivation(CustomerActivationDTO customerActivationDTO) {
    CustomerActivation customerActivation =
        customerActivationRepository
            .findFirstByOrganizationId(customerActivationDTO.getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getActivationMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    customerProfileActivationService.processActivation(customerActivationDTO);
    customerActivation.setCustomerActivationStatus(CustomerActivationStatus.APPROVED);
    customerActivationRepository.save(customerActivation);
  }

  /**
   * Saves the activation data for a customer. Updates the activation status to REVIEWING.
   *
   * @param customerActivationDTO The customer activation data transfer object.
   */
  @Override
  public void saveActivation(CustomerActivationDTO customerActivationDTO) {
    CustomerActivation customerActivation =
        customerActivationRepository
            .findFirstByOrganizationId(customerActivationDTO.getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getActivationMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    String activationData = ClientUtil.getGsonMapper().toJson(customerActivationDTO);
    customerActivation.setCustomerData(activationData);
    customerActivation.setCustomerActivationStatus(CustomerActivationStatus.REVIEWING);
    customerActivationRepository.save(customerActivation);
  }

  /**
   * Retrieves a list of unvalidated email addresses from the customer activation data.
   *
   * @param customerActivationDTO The customer activation data transfer object.
   * @return A list of unvalidated email addresses.
   */
  @Override
  public List<String> retrieveUnValidatedEmail(CustomerActivationDTO customerActivationDTO) {
    List<String> unValidatedEmails = new ArrayList<>();
    if (!ClientUtil.nullOrEmpty(customerActivationDTO.getCustomerUserDetailDTO())) {
      customerActivationDTO
          .getCustomerUserDetailDTO()
          .forEach(
              customerUserDetailDTO -> {
                if (!ClientUtil.nullOrEmpty(customerUserDetailDTO.getEmail())
                    && !COMPANY_REP.equals(customerUserDetailDTO.getSignUpType())
                    && !customerUserProfileRepository
                        .existsByEmailAndBvnValidatedTrueAndCustomerProfileOrganizationId(
                            customerUserDetailDTO.getEmail(),
                            customerActivationDTO.getOrganizationId())) {
                  unValidatedEmails.add(customerUserDetailDTO.getEmail());
                }
              });
    }
    return unValidatedEmails;
  }

  /**
   * Validates the submitted activation data against predefined rules. Throws an exception if
   * required fields are missing.
   *
   * @param customerActivationDTO The customer activation data transfer object.
   */
  private void validateSubmittedActivationData(CustomerActivationDTO customerActivationDTO) {
    Map<String, Object> accountActivationRules = new HashMap<>();
    buildValidationRule(accountActivationRules);
    List<String> requiredFields =
        FieldValidatorUtil.validateRules(customerActivationDTO, accountActivationRules);
    if (!ClientUtil.nullOrEmpty(requiredFields)) {
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getActivationMessage(INVALID_PAYLOAD),
          HttpStatus.BAD_REQUEST,
          requiredFields.stream().map(ApiError::new).toList());
    }
  }

  /**
   * Validates assigned roles for customer users. - Ensures assigned role is not null or empty. - If
   * only one user, role must be CUSTODIAN. - If multiple users: - COMPANY_REP can only have
   * INITIATOR role. - Other users can only have INITIATOR or AUTHORIZER role. - There must be at
   * least one INITIATOR and one AUTHORIZER.
   *
   * @param customerActivationDTO The customer activation data transfer object.
   */
  private void validateAssignedRole(CustomerActivationDTO customerActivationDTO) {
    List<CustomerUserDetailDTO> users = customerActivationDTO.getCustomerUserDetailDTO();
    if (ClientUtil.nullOrEmpty(users)) return;

    if (users.size() == 1) {
      CustomerUserDetailDTO user = users.getFirst();
      String role = user.getAssignedRole();
      if (ClientUtil.nullOrEmpty(role)) {
        throw exceptionHandler.processCustomException(
            messagePropertyConfig.getActivationMessage(ASSIGNED_ROLE_REQUIRED),
            HttpStatus.BAD_REQUEST);
      }
      if (!CUSTODIAN_ROLE_NAME.equalsIgnoreCase(role)) {
        throw exceptionHandler.processCustomException(
            CUSTODIAN_ROLE_REQUIRED, HttpStatus.BAD_REQUEST);
      }
      return;
    }

    // Validate and collect roles in one pass
    Map<String, Long> roleCounts =
        users.stream()
            .map(
                user -> {
                  String role = user.getAssignedRole();
                  if (ClientUtil.nullOrEmpty(role)) {
                    throw exceptionHandler.processCustomException(
                        messagePropertyConfig.getActivationMessage(ASSIGNED_ROLE_REQUIRED),
                        HttpStatus.BAD_REQUEST);
                  }

                  boolean isInitiator = INITIATOR_ROLE_NAME.equalsIgnoreCase(role);
                  boolean isAuthorizer = AUTHORIZER_ROLE_NAME.equalsIgnoreCase(role);

                  if (COMPANY_REP.equals(user.getSignUpType()) && !isInitiator) {
                    throw exceptionHandler.processCustomException(
                        messagePropertyConfig.getActivationMessage(COMPANY_REP_INVALID_ROLE),
                        HttpStatus.BAD_REQUEST);
                  } else if (!COMPANY_REP.equals(user.getSignUpType())
                      && !(isInitiator || isAuthorizer)) {
                    throw exceptionHandler.processCustomException(
                        INVALID_ROLE, HttpStatus.BAD_REQUEST);
                  }

                  return role.toUpperCase();
                })
            .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

    if (!roleCounts.containsKey(INITIATOR_ROLE_NAME.toUpperCase())
        || !roleCounts.containsKey(AUTHORIZER_ROLE_NAME.toUpperCase())) {
      throw exceptionHandler.processCustomException(
          "There must be at least one INITIATOR and one AUTHORIZER", HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Builds validation rules for customer activation data.
   *
   * @param accountActivationRules The map to store validation rules.
   */
  private static void buildValidationRule(Map<String, Object> accountActivationRules) {
    accountActivationRules.put("customerBusinessDetailDTO.organizationName.notEmpty", true);
    accountActivationRules.put("customerBusinessDetailDTO.organizationEmail.notEmpty", true);
    accountActivationRules.put("customerBusinessDetailDTO.organizationPhoneNumber.notEmpty", true);
    accountActivationRules.put("customerBusinessDetailDTO.registrationNumber.notEmpty", true);
    accountActivationRules.put("customerBusinessDetailDTO.tin.notEmpty", true);
    accountActivationRules.put("customerBusinessDetailDTO.registrationDate.notEmpty", true);
  }
}
