/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.util;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;

import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerProfile;
import com.digicore.lucid.customer.data.modules.profile.repository.CustomerProfileRepository;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import org.springframework.http.HttpStatus;

/*
 * <AUTHOR>
 * @createdOn Feb-11(Tue)-2025
 */

public class CustomerProfileUtil {
  public static CustomerProfile getCustomerProfile(
      String organizationId,
      CustomerProfileRepository customerProfileRepository,
      ExceptionHandler<String, String, HttpStatus, String> exceptionHandler,
      MessagePropertyConfig messagePropertyConfig) {
    organizationId =
        ClientUtil.nullOrEmpty(organizationId)
            ? RequestContextHolder.get().getOrganizationId()
            : organizationId;
    return customerProfileRepository
        .findFirstByOrganizationIdAndIsDeletedFalseOrderByCreatedDateDesc(organizationId)
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getClientMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }
}
