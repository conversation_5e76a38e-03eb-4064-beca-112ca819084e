/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.onboarding.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @createdOn Jun-10(Tue)-2025
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CustomerOnboardingFlowDTO {
  private String otp;
  private String username;
  private String firstName;
  private String password;
  private String email;
  private String accountNumber;
  private String phoneNumber;
  private String sessionId;
  private String dateOfBirth;
  private String cbaCustomerId;
  private List<SecurityQuestion> securityQuestion;
  private MultipartFile phishingImage;
  private boolean resendOTP;

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class SecurityQuestion {
    private String question;
    private String answer;
  }
}
