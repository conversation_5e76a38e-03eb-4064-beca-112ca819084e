/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.DUPLICATE;
import static com.digicore.lucid.common.lib.util.RegistrationUtil.getUserProfileDTO;

import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.authorization.dto.RoleCreationDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleDTO;
import com.digicore.lucid.common.lib.authorization.service.RoleService;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.UserRegistrationDTO;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerProfile;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerUserProfile;
import com.digicore.lucid.customer.data.modules.profile.repository.CustomerProfileRepository;
import com.digicore.lucid.customer.data.modules.profile.repository.CustomerUserProfileRepository;
import com.digicore.lucid.customer.data.modules.util.CustomerProfileUtil;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.services.RegistrationService;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-11(Tue)-2025
 */

@Service
@RequiredArgsConstructor
@Transactional
public class CustomerUserRegistrationService
    implements RegistrationService<UserProfileDTO, UserRegistrationDTO> {
  private final CustomerUserProfileRepository customerUserProfileRepository;
  private final RoleService<RoleDTO, RoleCreationDTO> customerUserRoleService;
  private final AuthProfileService<UserAuthProfileDTO> customerUserAuthProfileService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final CustomerProfileRepository customerProfileRepository;

  @Override
  public UserProfileDTO createProfile(UserRegistrationDTO registrationRequest) {
    if (profileExistenceCheckByEmailAndOrganizationId(
        registrationRequest.getEmail(), registrationRequest.getOrganizationId()))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getUserMessage(DUPLICATE), HttpStatus.BAD_REQUEST);
    customerUserRoleService.validateRole(
        registrationRequest.getAssignedRole(),
        registrationRequest.getOrganizationId(),
        registrationRequest.isSystemInitiated(),
        false);
    CustomerUserProfile customerUserProfile = createcustomerUserProfile(registrationRequest);
    customerUserAuthProfileService.saveNewAuthProfile(registrationRequest, customerUserProfile);
    UserProfileDTO userProfileDTO = getUserProfileDTO(registrationRequest);
    userProfileDTO.setSubDomain(customerUserProfile.getCustomerProfile().getSubDomainName());
    return userProfileDTO;
  }

  //  @Override
  public boolean profileExistenceCheckByEmailAndOrganizationId(
      String email, String organizationOd) {
    return customerUserProfileRepository
        .existsByEmailAndIsDeletedFalseAndCustomerProfileBankOrganizationId(email, organizationOd);
  }

  @Override
  public boolean profileCheck(String email, String companyName, String bankOrganizationId) {
    return customerUserProfileRepository
        .existsByEmailAndIsDeletedFalseAndCustomerProfileOrganizationNameAndCustomerProfileBankOrganizationId(
            email, companyName, bankOrganizationId);
  }

  @Override
  public boolean profileExistenceCheckByEmail(String email, String companyName) {
    return customerUserProfileRepository
        .existsByEmailAndIsDeletedFalseAndCustomerProfileOrganizationName(email, companyName);
  }

  private CustomerUserProfile createcustomerUserProfile(UserRegistrationDTO registrationRequest) {
    CustomerUserProfile customerUserProfile = new CustomerUserProfile();
    BeanUtilWrapper.copyNonNullProperties(registrationRequest, customerUserProfile);
    CustomerProfile customerProfile =
        CustomerProfileUtil.getCustomerProfile(
            registrationRequest.getOrganizationId(),
            customerProfileRepository,
            exceptionHandler,
            messagePropertyConfig);
    customerUserProfile.setCustomerProfile(customerProfile);
    customerUserProfile.setBvnValidated(true);
    customerUserProfileRepository.save(customerUserProfile);
    customerUserProfile.setReferralCode(
        "REF-".concat(customerUserProfile.getProfileId()).toUpperCase());
    return customerUserProfile;
  }

  //  private List<customerUserProfile> createcustomerUserProfile(
  //      List<UserRegistrationDTO> registrationRequest) {
  //    customerProfile customerProfile =
  // customerProfileUtil.getcustomerProfile(registrationRequest.getFirst().getOrganizationId(),customerProfileRepository,exceptionHandler,messagePropertyConfig);
  //    List<customerUserProfile> customerUserProfiles = new ArrayList<>();
  //    registrationRequest.forEach(
  //        request -> {
  //          customerUserProfile customerUserProfile = new customerUserProfile();
  //          BeanUtilWrapper.copyNonNullProperties(request, customerUserProfile);
  //          customerUserProfile.setcustomerProfile(customerProfile);
  //          customerUserProfileRepository.save(customerUserProfile);
  //          customerUserProfile.setReferralCode(
  //                  "REF-".concat(customerUserProfile.getProfileId()).toUpperCase());
  //          customerUserProfiles.add(customerUserProfile);
  //        });
  //    return customerUserProfiles;
  //  }
}
