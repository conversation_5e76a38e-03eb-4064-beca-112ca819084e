/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.branch.converter;

import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.branch.dto.BranchContactPerson;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/*
 * <AUTHOR>
 * @createdOn Apr-04(Fri)-2025
 */

@Converter
public class BranchContactPersonConverter
    implements AttributeConverter<BranchContactPerson, String> {
  @Override
  public String convertToDatabaseColumn(BranchContactPerson attribute) {
    if (attribute == null) {
      return null;
    }
    try {
      return getObjectMapper().writeValueAsString(attribute);
    } catch (JsonProcessingException e) {
      throw new ZeusRuntimeException("<<<< failed to convert BranchContactPerson to JSON >>>>", e);
    }
  }

  @Override
  public BranchContactPerson convertToEntityAttribute(String dbData) {
    if (ClientUtil.nullOrEmpty(dbData)) {
      return null;
    }
    try {
      return getObjectMapper().readValue(dbData, BranchContactPerson.class);
    } catch (JsonProcessingException e) {
      throw new ZeusRuntimeException("<<<< failed to convert JSON to BranchContactPerson >>>>", e);
    }
  }
}
