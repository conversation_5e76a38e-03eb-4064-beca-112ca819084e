/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.onboarding.repository;

import com.digicore.lucid.customer.data.modules.onboarding.model.CustomerFlow;
import java.time.LocalDateTime;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * <AUTHOR>
 * @createdOn Jun-10(Tue)-2025
 */

public interface CustomerFlowRepository extends JpaRepository<CustomerFlow, Long> {

  Optional<CustomerFlow> findBySessionIdAndIsDeletedFalse(String sessionId);

  Optional<CustomerFlow>
      findFirstByBankOrganizationIdAndAccountNumberAndEmailAndIsDeletedFalseAndCreatedDateAfterOrderByCreatedDateDesc(
          String bankOrganizationId,
          String accountNumber,
          String email,
          LocalDateTime createdAfter);
}
