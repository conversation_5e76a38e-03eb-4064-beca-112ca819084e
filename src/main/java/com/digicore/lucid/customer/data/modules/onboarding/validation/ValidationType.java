/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.onboarding.validation;

import com.digicore.lucid.customer.data.modules.onboarding.validation.impl.*;
import lombok.Getter;

/**
 * <AUTHOR>
 * @createdOn Jun-10(Tue)-2025
 */
@Getter
public enum ValidationType {
  MOBILE_OTP("MOBILE_OTP", MobileOtpValidationServiceImpl.class),
  EMAIL_OTP("EMAIL_OTP", EmailOtpValidationServiceImpl.class),
  SECURITY_QUESTION("SECURITY_QUESTION", SecurityQuestionValidationServiceImpl.class),
  PHISHING_IMAGE("PHISHING_IMAGE", PhishingImageValidationServiceImpl.class),
  CAPTCHA("CAPTCHA", CaptchaValidationServiceImpl.class),
  COMPLETE_REGISTRATION("COMPLETE_REGISTRATION", CompleteVerificationValidationServiceImpl.class);

  private final String type;
  private final Class<? extends ValidationService> serviceClass;

  ValidationType(String type, Class<? extends ValidationService> validationServiceClass) {
    this.type = type;
    this.serviceClass = validationServiceClass;
  }
}
