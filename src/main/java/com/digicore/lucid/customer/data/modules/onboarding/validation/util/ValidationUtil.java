/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.onboarding.validation.util;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.api.helper.response.ApiError;
import com.digicore.lucid.customer.data.modules.onboarding.dto.CustomerOnboardingFlowDTO;
import com.digicore.lucid.customer.data.modules.onboarding.dto.OnboardingResponseDTO;
import com.digicore.lucid.customer.data.modules.onboarding.model.CustomerFlow;
import com.digicore.lucid.customer.data.modules.onboarding.repository.CustomerFlowRepository;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationAction;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationService;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationServiceRegistry;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationType;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR> Ucheagwu
 * @createdOn Jun-10(Tue)-2025
 */
public class ValidationUtil {

  public static CustomerFlow getCustomerFlow(
      CustomerFlowRepository customerFlowRepository, String sessionId) {
    if (StringUtils.isBlank(sessionId)) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Session id is required")));
    }

    return customerFlowRepository
        .findBySessionIdAndIsDeletedFalse(sessionId)
        .orElseThrow(
            () ->
                new ZeusRuntimeException(
                    HttpStatus.BAD_REQUEST, List.of(new ApiError("Session id does not exist"))));
  }

  public static CustomerFlow setNextFlow(
      CustomerFlowRepository customerFlowRepository,
      CustomerFlow customerFlow,
      ValidationType currentValidationType,
      ValidationAction currentAction) {

    if ((currentValidationType.equals(ValidationType.EMAIL_OTP)
            || currentValidationType.equals(ValidationType.MOBILE_OTP))
        && currentAction != null
        && currentAction == ValidationAction.INITIATE) {
      customerFlow.setNextStep(
          currentValidationType
              .getType()
              .concat(".")
              .concat(ValidationAction.VALIDATE.getAction()));
      return customerFlowRepository.save(customerFlow);
    }
    String[] validations = customerFlow.getValidations().split(",");
    for (int i = 0; i < validations.length; i++) {
      String validation = validations[i];
      if (validation.equalsIgnoreCase(currentValidationType.getType())) {
        if (i == validations.length - 1) {
          customerFlow.setNextStep(ValidationAction.COMPLETE.getAction());
          return customerFlowRepository.save(customerFlow);
        } else {
          String nextValidation = validations[i + 1];
          customerFlow.setNextStep(
              nextValidation.concat(".").concat(ValidationAction.INITIATE.getAction()));
          return customerFlowRepository.save(customerFlow);
        }
      }
    }
    return customerFlow;
  }

  public static Object nextValidationProcess(
      CustomerFlow customerFlow,
      ValidationServiceRegistry validationServiceRegistry,
      CustomerOnboardingFlowDTO customerOnboardingFlowDTO,
      String subDomain,
      String validationTypes) {
    String nextStep = customerFlow.getNextStep();
    if (nextStep.equalsIgnoreCase(ValidationAction.COMPLETE.getAction())) {
      return OnboardingResponseDTO.builder()
          .sessionId(customerFlow.getSessionId())
          .currentFlow(ValidationAction.COMPLETE.getAction())
          .message("Customer onboarding completed successfully.")
          .nextFlow(null)
          .build();
    } else {
      // Return response indicating next step without immediately processing it
      // This prevents nested transaction issues and connection leaks
      return OnboardingResponseDTO.builder()
          .sessionId(customerFlow.getSessionId())
          .currentFlow(ValidationAction.COMPLETE.getAction())
          .message("Validation step completed successfully.")
          .nextFlow(customerFlow.getNextStep())
          .build();
    }
  }

  public static void ensureOnboardingNotCompleted(CustomerFlow customerFlow) {
    if (customerFlow.getNextStep().equalsIgnoreCase(ValidationAction.COMPLETE.getAction())) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Onboarding is complete")));
    }
  }

  public static Object returnPreviousFlow(
      CustomerFlowRepository customerFlowRepository, CustomerFlow customerFlow) {
    String[] currentNextStepOperations = customerFlow.getNextStep().split("\\.");
    // MOBILE_OTP.INITIATE, MOBILE_OTP.VALIDATE, EMAIL_OTP.INITIATE, EMAIL_OTP.VALIDATE

    if (currentNextStepOperations.length == 1) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Onboarding is complete")));
    }
    String currentNextStep = currentNextStepOperations[0]; // MOBILE_OTP
    String currentNextAction = currentNextStepOperations[1]; // VALIDATE
    String[] validations =
        customerFlow
            .getValidations()
            .split(","); // MOBILE_OTP, EMAIL_OTP, SECURITY_QUESTION, PHISHING_IMAGE, CAPTCHA,
    // COMPLETE_REGISTRATION

    if (currentNextAction.equalsIgnoreCase(ValidationAction.VALIDATE.getAction())) {
      customerFlow.setNextStep(
          currentNextStep.concat(".").concat(ValidationAction.INITIATE.getAction()));
      customerFlow = customerFlowRepository.save(customerFlow);
      return returnPreviousStep(customerFlow, validations, currentNextStep);
    } else {
      return returnPreviousStep(customerFlow, validations, currentNextStep);
    }
  }

  private static OnboardingResponseDTO returnPreviousStep(
      CustomerFlow customerFlow, String[] validations, String currentNextStep) {
    String firstValidation = validations[0];
    if (firstValidation.equals(currentNextStep)) {
      return null;
    }
    return OnboardingResponseDTO.builder()
        .sessionId(customerFlow.getSessionId())
        .currentFlow(null)
        .message("Proceed to next step")
        .nextFlow(customerFlow.getNextStep())
        .build();
  }
}
