/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.employee.dto;

import static com.digicore.lucid.common.lib.constant.payload.PayloadConstant.*;

import com.digicore.registhentication.validator.RequestBodyChecker;
import lombok.*;

/*
 * <AUTHOR>
 * @createdOn 11/03/2025
 */

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EmployeeRequestDTO {
  @RequestBodyChecker private String employeeId;

  @RequestBodyChecker(message = NAME_IS_REQUIRED)
  private String firstName;

  @RequestBodyChecker(message = NAME_IS_REQUIRED)
  private String lastName;

  private String email;
  private String validatedAccountName;
  private String phoneNumber;

  @RequestBodyChecker(
      message = ACCOUNT_NUMBER_IS_REQUIRED_MESSAGE,
      pattern = ACCOUNT_NUMBER_PATTERN)
  private String accountNumber;

  private String accountProviderName;
  private String accountProviderCode;

  private String pfaName;
  private String pfaCode;

  private String tin;
  private String minorSalaryAmount;
  private String minorTaxAmount;
  private String minorPensionAmount;
  private String systemId;
}
