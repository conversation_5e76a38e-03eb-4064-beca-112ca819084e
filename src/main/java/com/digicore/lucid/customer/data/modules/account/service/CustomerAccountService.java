/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.account.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;

import com.digicore.lucid.common.lib.account.dto.CustomerAccountDTO;
import com.digicore.lucid.common.lib.account.service.AccountService;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerOnboardingDTO;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.account.model.CustomerAccount;
import com.digicore.lucid.customer.data.modules.account.repository.CustomerAccountRepository;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerProfile;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerUserProfile;
import com.digicore.lucid.customer.data.modules.profile.repository.CustomerProfileRepository;
import com.digicore.lucid.customer.data.modules.profile.repository.CustomerUserProfileRepository;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-22(Sat)-2025
 */

@RequiredArgsConstructor
@Service
@Transactional
public class CustomerAccountService implements AccountService<CustomerAccountDTO> {
  private final CustomerProfileRepository customerProfileRepository;
  private final CustomerUserProfileRepository customerUserProfileRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final CustomerAccountRepository customerAccountRepository;

  @Override
  public void addAccount(String organizationId, String accountNumber) {
    CustomerProfile customerProfile =
        customerProfileRepository
            .findFirstByOrganizationIdAndIsDeletedFalseOrderByCreatedDateDesc(organizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getActivationMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    getCustomerAccount(accountNumber, customerProfile);
  }

  private void getCustomerAccount(String accountNumber, CustomerProfile customerProfile) {
    CustomerAccount customerAccount = new CustomerAccount();
    customerAccount.setAccountNumber(accountNumber);
    customerAccount.setAccountName(customerProfile.getOrganizationName());
    customerAccount.setCustomerProfile(customerProfile);
    customerAccount.setActive(true);
    customerAccount.setViewable(true);

    customerAccountRepository.save(customerAccount);

    Set<CustomerUserProfile> userProfiles =
        getCustomerUserProfiles(customerProfile, customerAccount);
    customerUserProfileRepository.saveAll(userProfiles);
  }

  private static Set<CustomerUserProfile> getCustomerUserProfiles(
      CustomerProfile customerProfile, CustomerAccount customerAccount) {
    Set<CustomerUserProfile> userProfiles = customerProfile.getCustomerUserProfiles();

    if (!ClientUtil.nullOrEmpty(userProfiles)) {
      userProfiles.forEach(
          customerUserProfile -> {
            Set<CustomerAccount> accounts = customerUserProfile.getCustomerAccounts();
            if (ClientUtil.nullOrEmpty(accounts)) {
              accounts = new HashSet<>();
            }
            accounts.add(customerAccount);
            customerUserProfile.setCustomerAccounts(accounts);
          });
    }
    return userProfiles;
  }

  @Override
  public void addAccount(String organizationId, List<CustomerOnboardingDTO.Account> accounts) {
    CustomerProfile customerProfile =
        customerProfileRepository
            .findFirstByOrganizationIdAndIsDeletedFalseOrderByCreatedDateDesc(organizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getActivationMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    List<CustomerAccount> customerAccounts =
        accounts.stream()
            .map(
                account -> {
                  CustomerAccount customerAccount = new CustomerAccount();
                  customerAccount.setAccountNumber(account.getAccountNumber());
                  customerAccount.setAccountName(account.getAccountName());
                  customerAccount.setAccountType(account.getAccountType());
                  customerAccount.setCurrency(account.getCurrency());
                  customerAccount.setCustomerProfile(customerProfile);
                  customerAccount.setActive(true);
                  customerAccount.setViewable(true);
                  return customerAccount;
                })
            .toList();
    customerAccountRepository.saveAll(customerAccounts);

    Set<CustomerUserProfile> userProfiles =
        getCustomerUserProfiles(customerProfile, customerAccounts);
    customerUserProfileRepository.saveAll(userProfiles);
  }

  private static Set<CustomerUserProfile> getCustomerUserProfiles(
      CustomerProfile customerProfile, List<CustomerAccount> customerAccounts) {
    Set<CustomerUserProfile> userProfiles = customerProfile.getCustomerUserProfiles();
    if (!ClientUtil.nullOrEmpty(userProfiles)) {
      userProfiles.forEach(
          customerUserProfile -> {
            Set<CustomerAccount> accounts = customerUserProfile.getCustomerAccounts();
            if (ClientUtil.nullOrEmpty(accounts)) {
              accounts = new HashSet<>();
            }
            accounts.addAll(customerAccounts);
            customerUserProfile.setCustomerAccounts(accounts);
          });
    }
    return userProfiles;
  }

  @Override
  public boolean verifyAccountNumber(String accountNumber) {
    return customerAccountRepository.existsByAccountNumberAndIsDeletedFalse(accountNumber);
  }

  @Override
  public List<CustomerAccountDTO> fetchActiveViewableAccounts(String organizationId) {
    return customerAccountRepository
        .findByCustomerProfileOrganizationId(RequestContextHolder.get().getOrganizationId())
        .stream()
        .map(
            accountEntity -> {
              CustomerAccountDTO checkDTO = new CustomerAccountDTO();
              BeanUtilWrapper.copyNonNullProperties(accountEntity, checkDTO);
              return checkDTO;
            })
        .toList();
  }

  @Override
  public List<CustomerAccountDTO> fetchAllAccounts(String organizationId) {
    return customerAccountRepository.findAllByOrganizationId(organizationId).stream()
        .map(
            accountEntity -> {
              CustomerAccountDTO checkDTO = new CustomerAccountDTO();
              BeanUtilWrapper.copyNonNullProperties(accountEntity, checkDTO);
              return checkDTO;
            })
        .toList();
  }

  @Override
  public List<CustomerAccountDTO> fetchAllAccounts(
      String bankOrganizationId, String organizationId) {
    return customerAccountRepository
        .findAllByBankOrganizationIdAndOrganizationId(bankOrganizationId, organizationId)
        .stream()
        .map(
            accountEntity -> {
              CustomerAccountDTO checkDTO = new CustomerAccountDTO();
              BeanUtilWrapper.copyNonNullProperties(accountEntity, checkDTO);
              return checkDTO;
            })
        .toList();
  }

  @Override
  public boolean verifyAccountNumber(String accountNumber, String organizationId) {
    return customerAccountRepository
        .existsByAccountNumberAndCustomerProfileOrganizationIdAndIsDeletedFalse(
            accountNumber, organizationId);
  }

  @Override
  public boolean verifyAccountNumberExists(String accountNumber, String bankOrganizationId) {
    return customerAccountRepository
        .existsByAccountNumberAndCustomerProfileBankOrganizationIdAndIsDeletedFalse(
            accountNumber, bankOrganizationId);
  }

  @Override
  public CustomerAccountDTO fetchAccount(
      String accountNumber, String organizationId, String bankOrganizationId) {
    CustomerAccount customerAccount =
        customerAccountRepository
            .findByAccountNumberAndIsDeletedFalseAndActiveTrueAndCustomerProfileBankOrganizationIdAndCustomerProfileOrganizationId(
                accountNumber, bankOrganizationId, organizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getAccountMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    return mapEntityToDTO(customerAccount);
  }

  private CustomerAccountDTO mapEntityToDTO(CustomerAccount customerAccount) {
    CustomerAccountDTO customerAccountDTO = new CustomerAccountDTO();
    BeanUtilWrapper.copyNonNullProperties(customerAccount, customerAccountDTO);
    return customerAccountDTO;
  }
}
