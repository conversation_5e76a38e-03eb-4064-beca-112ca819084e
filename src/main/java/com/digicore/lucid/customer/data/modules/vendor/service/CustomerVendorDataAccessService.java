/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.vendor.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.DUPLICATE;
import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;
import static com.digicore.lucid.common.lib.util.ClientUtil.getPageable;

import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerProfile;
import com.digicore.lucid.customer.data.modules.profile.repository.CustomerProfileRepository;
import com.digicore.lucid.customer.data.modules.vendor.dto.CustomerVendorDTO;
import com.digicore.lucid.customer.data.modules.vendor.dto.VendorRequestDTO;
import com.digicore.lucid.customer.data.modules.vendor.model.CustomerVendor;
import com.digicore.lucid.customer.data.modules.vendor.repository.CustomerVendorRepository;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.util.IDGeneratorUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ilori
 * @createdOn 11/03/2025
 */

@Service
@RequiredArgsConstructor
public class CustomerVendorDataAccessService implements DataAccessService<CustomerVendorDTO> {
  private final CustomerVendorRepository customerVendorRepository;
  private final CustomerProfileRepository customerProfileRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  @Override
  public <C> CustomerVendorDTO create(C data) {
    VendorRequestDTO createDTO = (VendorRequestDTO) data;
    if (customerVendorRepository
        .existsByAccountNumberAndCustomerProfileOrganizationIdAndIsDeletedFalse(
            createDTO.getAccountNumber(), RequestContextHolder.get().getOrganizationId())) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getBeneficiaryMessage(DUPLICATE), HttpStatus.BAD_REQUEST);
    }
    CustomerVendor customerVendor = new CustomerVendor();
    BeanUtilWrapper.copyNonNullProperties(data, customerVendor);
    customerVendor.setSystemId(IDGeneratorUtil.generateSystemId("VE-"));
    customerVendor.setCustomerProfile(fetchCustomerProfile());
    customerVendorRepository.save(customerVendor);
    return map(customerVendor);
  }

  @Override
  public <E> CustomerVendorDTO edit(E data) {
    VendorRequestDTO editDTO = (VendorRequestDTO) data;
    CustomerVendor customerVendor = fetchVendor(editDTO.getSystemId());
    if ((!ClientUtil.nullOrEmpty(editDTO.getAccountNumber()))
        && (customerVendorRepository
            .existsByAccountNumberAndCustomerProfileOrganizationIdAndIsDeletedFalse(
                editDTO.getAccountNumber(), RequestContextHolder.get().getOrganizationId()))) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getBeneficiaryMessage(DUPLICATE), HttpStatus.BAD_REQUEST);
    }
    BeanUtilWrapper.copyNonNullProperties(editDTO, customerVendor);
    customerVendorRepository.save(customerVendor);
    return map(customerVendor);
  }

  @Override
  public CustomerVendorDTO retrieve(String vendorId) {
    return customerVendorRepository
        .findByVendorIdAndOrganizationId(vendorId, RequestContextHolder.get().getOrganizationId())
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getBeneficiaryMessage(NOT_FOUND),
                    HttpStatus.BAD_REQUEST));
  }

  @Override
  public PaginatedResponseDTO<CustomerVendorDTO> retrieve(
      String organizationId, int pageNumber, int pageSize) {
    Page<CustomerVendorDTO> vendorPage =
        customerVendorRepository.findByAllByOrganizationId(
            organizationId, getPageable(pageNumber, pageSize));
    return PaginatedResponseDTO.<CustomerVendorDTO>builder()
        .content(vendorPage.getContent())
        .currentPage(vendorPage.getNumber() + 1)
        .isLastPage(vendorPage.isLast())
        .isFirstPage(vendorPage.isFirst())
        .build();
  }

  @Override
  public void remove(String vendorId) {
    CustomerVendor customerVendor =
        customerVendorRepository
            .findFirstByVendorIdAndCustomerProfileOrganizationIdAndIsDeletedFalse(
                vendorId, RequestContextHolder.get().getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getBeneficiaryMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    customerVendor.setDeleted(true);
    customerVendorRepository.save(customerVendor);
  }

  @Override
  public boolean exists(String vendorId, String accountNumber) {
    return customerVendorRepository.existsByVendorIdAndIsDeletedFalse(vendorId)
        || customerVendorRepository
            .existsByAccountNumberAndCustomerProfileOrganizationIdAndIsDeletedFalse(
                accountNumber, RequestContextHolder.get().getOrganizationId());
  }

  private CustomerVendor fetchVendor(String vendorId) {
    return customerVendorRepository
        .findFirstBySystemIdAndCustomerProfileOrganizationIdAndIsDeletedFalse(
            vendorId, RequestContextHolder.get().getOrganizationId())
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getBeneficiaryMessage(NOT_FOUND),
                    HttpStatus.BAD_REQUEST));
  }

  private CustomerProfile fetchCustomerProfile() {
    return customerProfileRepository
        .findFirstByOrganizationIdAndIsDeletedFalseOrderByCreatedDateDesc(
            RequestContextHolder.get().getOrganizationId())
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getUserMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }

  private CustomerVendorDTO map(CustomerVendor customerVendor) {
    CustomerVendorDTO dto = new CustomerVendorDTO();
    BeanUtilWrapper.copyNonNullProperties(customerVendor, dto);
    return dto;
  }
}
