/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.limit.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.util.ClientUtil.getPageable;
import static com.digicore.registhentication.util.PageableUtil.SORT_BY_CREATED_DATE;

import com.digicore.api.helper.response.ApiError;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.limit.dto.CustomerLimitConfigDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.limit.service.LimitConfigService;
import com.digicore.lucid.common.lib.limit.util.LimitUtil;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.account.model.CustomerAccount;
import com.digicore.lucid.customer.data.modules.account.repository.CustomerAccountRepository;
import com.digicore.lucid.customer.data.modules.limit.model.CustomerLimitConfig;
import com.digicore.lucid.customer.data.modules.limit.repository.CustomerLimitConfigRepository;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.validator.enums.Currency;
import jakarta.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-05(Wed)-2025
 */
@Service
@RequiredArgsConstructor
@Transactional
public class CustomerLimitConfigService implements LimitConfigService<CustomerLimitConfigDTO> {
  private final CustomerLimitConfigRepository customerLimitConfigRepository;
  private final CustomerAccountRepository customerAccountRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  //  @Override
  //  public List<LimitConfigDTO> retrieveLimitConfig(String accountNumber) {
  //    Optional<LimitConfigDTO> customerLimitConfig =
  //        customerLimitConfigRepository.findFirstLimitConfigByAccountNumberAndOrganizationId(
  //            accountNumber, RequestContextHolder.get().getOrganizationId());
  //    return customerLimitConfig.map(List::of).orElseGet(List::of);
  //  }

  @Override
  public PaginatedResponseDTO<CustomerLimitConfigDTO> retrieveLimitConfig(
      int pageNumber, int pageSize) {
    Pageable pageable = getPageable(pageNumber, pageSize, SORT_BY_CREATED_DATE);
    Page<CustomerLimitConfigDTO> customerLimitConfigDTOPage =
        customerLimitConfigRepository.findByOrganizationId(
            RequestContextHolder.get().getOrganizationId(), pageable);
    return getLimitPaginatedResponseDTO(customerLimitConfigDTOPage);
  }

  @Override
  public PaginatedResponseDTO<CustomerLimitConfigDTO> retrieveLimitConfig(
      String organizationId, String bankOrganizationId, int pageNumber, int pageSize) {
    Pageable pageable = getPageable(pageNumber, pageSize, SORT_BY_CREATED_DATE);
    Page<CustomerLimitConfigDTO> customerLimitConfigDTOPage =
        customerLimitConfigRepository.findByOrganizationIdAndBankOrganizationId(
            organizationId, bankOrganizationId, pageable);
    return getLimitPaginatedResponseDTO(customerLimitConfigDTOPage);
  }

  @Override
  public CustomerLimitConfigDTO retrieveLimitConfig(
      String accountNumber, LimitType limitType, Currency currency) {
    return customerLimitConfigRepository
        .findFirstLimitConfigByAccountNumberAndOrganizationIdAndLimitTypeAndCurrency(
            accountNumber,
            RequestContextHolder.get().getBankOrganizationId(),
            RequestContextHolder.get().getOrganizationId(),
            limitType,
            currency)
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getLimitMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }

  @Override
  public CustomerLimitConfigDTO retrieveLimitConfig(
      String accountNumber,
      LimitType limitType,
      Currency currency,
      String organizationId,
      String bankOrganizationId) {
    return customerLimitConfigRepository
        .findFirstLimitConfigByAccountNumberAndOrganizationIdAndLimitTypeAndCurrency(
            accountNumber, bankOrganizationId, organizationId, limitType, currency)
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getLimitMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }

  @Override
  public CustomerLimitConfigDTO retrieveLimitConfig(
      String accountNumber, String organizationId, LimitType limitType, Currency currency) {
    Optional<CustomerLimitConfigDTO> customerLimitConfig =
        customerLimitConfigRepository
            .findFirstLimitConfigByAccountNumberAndOrganizationIdAndLimitTypeAndCurrency(
                accountNumber, organizationId, limitType, currency);
    return customerLimitConfig.orElse(null);
  }

  @Override
  public CustomerLimitConfigDTO verifyLimitConfigExist(CustomerLimitConfigDTO limitConfigDTO) {
    CustomerLimitConfigDTO customerLimitConfigDTO =
        retrieveLimitConfig(
            limitConfigDTO.getAccountNumber(),
            limitConfigDTO.getLimitType(),
            limitConfigDTO.getCurrency(),
            limitConfigDTO.getOrganizationId(),
            limitConfigDTO.getBankOrganizationId());
    List<ApiError> apiErrors = new ArrayList<>();
    LimitUtil.validateLimitConfig(limitConfigDTO, apiErrors);

    if (!ClientUtil.nullOrEmpty(apiErrors))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getLimitMessage(LIMIT_VIOLATION),
          HttpStatus.BAD_REQUEST,
          apiErrors);

    return customerLimitConfigDTO;
  }

  @Override
  public void createLimitConfig(CustomerLimitConfigDTO limitConfigDTO) {
    if (customerLimitConfigRepository
        .existsByLimitTypeAndCurrencyAndCustomerAccountAccountNumberAndCustomerAccountCustomerProfileOrganizationIdAndCustomerAccountCustomerProfileBankOrganizationId(
            limitConfigDTO.getLimitType(),
            limitConfigDTO.getCurrency(),
            limitConfigDTO.getAccountNumber(),
            limitConfigDTO.getOrganizationId(),
            limitConfigDTO.getBankOrganizationId()))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getLimitMessage(DUPLICATE), HttpStatus.BAD_REQUEST);
    CustomerAccount customerAccount =
        customerAccountRepository
            .findByAccountNumberAndIsDeletedFalseAndActiveTrueAndCustomerProfileBankOrganizationIdAndCustomerProfileOrganizationId(
                limitConfigDTO.getAccountNumber(),
                limitConfigDTO.getBankOrganizationId(),
                limitConfigDTO.getOrganizationId())
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getAccountMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    CustomerLimitConfig newLimitConfig = new CustomerLimitConfig();
    BeanUtilWrapper.copyNonNullProperties(limitConfigDTO, newLimitConfig);
    newLimitConfig.setCustomerAccount(customerAccount);
    customerLimitConfigRepository.save(newLimitConfig);
  }

  @Override
  public void updateLimitConfig(CustomerLimitConfigDTO request) {
    if (!customerLimitConfigRepository
        .existsByLimitTypeAndCurrencyAndCustomerAccountAccountNumberAndCustomerAccountCustomerProfileOrganizationIdAndCustomerAccountCustomerProfileBankOrganizationId(
            request.getLimitType(),
            request.getCurrency(),
            request.getAccountNumber(),
            request.getOrganizationId(),
            request.getBankOrganizationId()))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getLimitMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    customerLimitConfigRepository.updateCustomerLimitConfig(
        request.getMinorMobileSingleCap(),
        request.getMinorWebSingleCap(),
        request.getMinorMobileCumulativeCap(),
        request.getMinorWebCumulativeCap(),
        request.getMinorMobileSingleCappedMessage(),
        request.getMinorWebSingleCappedMessage(),
        request.getMinorMobileCumulativeCappedMessage(),
        request.getMinorWebCumulativeCappedMessage(),
        request.isUnlimited(),
        request.isDefaultLimit(),
        request.getLimitType(),
        request.getCurrency(),
        request.getAccountNumber(),
        request.getOrganizationId(),
        request.getBankOrganizationId());
  }

  private PaginatedResponseDTO<CustomerLimitConfigDTO> getLimitPaginatedResponseDTO(
      Page<CustomerLimitConfigDTO> transactionLimits) {
    return PaginatedResponseDTO.<CustomerLimitConfigDTO>builder()
        .content(transactionLimits.getContent())
        .currentPage(transactionLimits.getNumber() + 1)
        .totalPages(transactionLimits.getTotalPages())
        .totalItems(transactionLimits.getTotalElements())
        .isFirstPage(transactionLimits.isFirst())
        .isLastPage(transactionLimits.isLast())
        .build();
  }
}
