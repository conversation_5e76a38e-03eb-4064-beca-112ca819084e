/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.onboarding.validation.impl;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.api.helper.response.ApiError;
import com.digicore.lucid.common.lib.activation.dto.CustomerDocumentUploadDTO;
import com.digicore.lucid.common.lib.activation.dto.FileUploadedDTO;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.util.ImageBase64Util;
import com.digicore.lucid.customer.data.config.ValidationConfigProperties;
import com.digicore.lucid.customer.data.modules.onboarding.dto.CustomerOnboardingFlowDTO;
import com.digicore.lucid.customer.data.modules.onboarding.model.CustomerFlow;
import com.digicore.lucid.customer.data.modules.onboarding.model.CustomerPhishingImage;
import com.digicore.lucid.customer.data.modules.onboarding.repository.CustomerFlowRepository;
import com.digicore.lucid.customer.data.modules.onboarding.repository.CustomerPhishingImageRepository;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationAction;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationService;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationServiceRegistry;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationType;
import com.digicore.lucid.customer.data.modules.onboarding.validation.util.ValidationUtil;
import com.digicore.registhentication.registration.services.DocumentUploadService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Base64;
import java.util.List;
import java.util.Map;

import static com.digicore.lucid.customer.data.modules.onboarding.validation.util.ValidationUtil.setNextFlow;

/**
 * <AUTHOR> Ucheagwu
 * @createdOn Jun-10(Tue)-2025
 */
@Service
@Qualifier("PHISHING_IMAGE") @RequiredArgsConstructor
public class PhishingImageValidationServiceImpl implements ValidationService {
  private final CustomerFlowRepository customerFlowRepository;
  private final ValidationConfigProperties validationConfigProperties;
  private final ValidationServiceRegistry validationServiceRegistry;
  private final DocumentUploadService<List<FileUploadedDTO>, CustomerDocumentUploadDTO>
      customerMultipleDocumentUploadService;
  private final CustomerPhishingImageRepository customerPhishingImageRepository;

  @Override
  public Object process(
      CustomerOnboardingFlowDTO customerOnboardingFlowDTO,
      String subDomain,
      String action,
      String validationTypes) {
    CustomerFlow customerFlow =
        ValidationUtil.getCustomerFlow(
            customerFlowRepository, customerOnboardingFlowDTO.getSessionId());
    ValidationUtil.ensureOnboardingNotCompleted(customerFlow);

    if (!action.equalsIgnoreCase(ValidationAction.INITIATE.getAction())) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST,
          List.of(new ApiError("Invalid action for phishing image validation")));
    }

    try {
      if (customerOnboardingFlowDTO.getPhishingImage() == null
          || customerOnboardingFlowDTO.getPhishingImage().getBytes().length == 0) {
        throw new ZeusRuntimeException(
            HttpStatus.BAD_REQUEST, List.of(new ApiError("Phishing image is required")));
      }
    } catch (Exception e) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Phishing image is required")));
    }

    try {
      List<Map<String, String>> allImagesAsBase64 =
          ImageBase64Util.getAllImagesAsBase64(getPhishingImagePath());
      comparePhishingImages(allImagesAsBase64, customerOnboardingFlowDTO.getPhishingImage());
    } catch (Exception e) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Error occurred while validating image")));
    }
    // images
    CustomerDocumentUploadDTO.CustomerDocument customerDocument =
        new CustomerDocumentUploadDTO.CustomerDocument();
    customerDocument.setDocumentType("PHISHING_IMAGE");
    customerDocument.setFile(customerOnboardingFlowDTO.getPhishingImage());
    customerDocument.setEmail(customerFlow.getEmail());

    CustomerDocumentUploadDTO customerDocumentUploadDTO = new CustomerDocumentUploadDTO();
    customerDocumentUploadDTO.setFiles(
        new CustomerDocumentUploadDTO.CustomerDocument[] {customerDocument});

    List<FileUploadedDTO> fileUploadedDTOS =
        customerMultipleDocumentUploadService.uploadMultipleDocument(customerDocumentUploadDTO);

    if (fileUploadedDTOS == null || fileUploadedDTOS.isEmpty()) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Failed to upload phishing image")));
    }

    savePhishingImage(
        customerOnboardingFlowDTO.getAccountNumber(),
        fileUploadedDTOS.getFirst(),
        customerFlow.getEmail());

    customerFlow =
        setNextFlow(customerFlowRepository, customerFlow, ValidationType.PHISHING_IMAGE, null);

    return ValidationUtil.nextValidationProcess(
        customerFlow,
        validationServiceRegistry,
        customerOnboardingFlowDTO,
        subDomain,
        validationTypes);
  }

  private void savePhishingImage(
      String accountNumber, FileUploadedDTO fileUploadedDTO, String email) {
    CustomerPhishingImage customerPhishingImage = new CustomerPhishingImage();
    customerPhishingImage.setAccountNumber(accountNumber);
    customerPhishingImage.setBankOrganizationId(RequestContextHolder.get().getBankOrganizationId());
    customerPhishingImage.setOrganizationId(RequestContextHolder.get().getOrganizationId());
    customerPhishingImage.setEmail(email);
    customerPhishingImage.setFileId(fileUploadedDTO.getFileId());
    customerPhishingImage.setFilePath(fileUploadedDTO.getFilePath());
    customerPhishingImageRepository.saveAndFlush(customerPhishingImage);
  }

  private void comparePhishingImages(
      List<Map<String, String>> sourceImages, MultipartFile targetImage) throws IOException {
    if (targetImage.isEmpty()) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Phishing image not provided")));
    }

    if (sourceImages == null || sourceImages.isEmpty()) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Phishing image not provided")));
    }

    String targetImageBase64 = Base64.getEncoder().encodeToString(targetImage.getBytes());

    String matchedValue =
        sourceImages.stream()
            .flatMap(map -> map.values().stream())
            .filter(
                value -> value.replaceAll("data:image/[^;]+;base64,", "").equals(targetImageBase64))
            .findFirst()
            .orElse(null);

    if (ClientUtil.nullOrEmpty(matchedValue)) {

      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Phishing image provided does not match")));
    }
  }

  public String getPhishingImagePath() {
    ValidationConfigProperties.ValidationConfig validationConfig =
        validationConfigProperties.getConfigBySubDomain(
            RequestContextHolder.get().getSubDomainName());

    return validationConfig.getValidationType().stream()
        .filter(t -> t.getType().equals(ValidationType.PHISHING_IMAGE.getType()))
        .map(ValidationConfigProperties.ValidationType::getSourcePath)
        .findFirst()
        .orElse(null);
  }
}
