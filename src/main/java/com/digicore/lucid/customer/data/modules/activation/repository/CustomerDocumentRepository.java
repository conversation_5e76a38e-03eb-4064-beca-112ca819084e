/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.activation.repository;

import com.digicore.lucid.customer.data.modules.activation.model.CustomerUploadedDocument;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * <AUTHOR>
 * @createdOn Feb-17(Mon)-2025
 */

public interface CustomerDocumentRepository extends JpaRepository<CustomerUploadedDocument, Long> {
  Optional<CustomerUploadedDocument> findFirstByOrganizationId(String organizationId);

  Optional<CustomerUploadedDocument> findFirstByOrganizationIdOrderByCreatedDateDesc(
      String organizationId);
}
