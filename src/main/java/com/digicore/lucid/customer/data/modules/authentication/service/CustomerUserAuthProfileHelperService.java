/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.authentication.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;
import static com.digicore.lucid.common.lib.constant.message.MessagePlaceHolderConstant.EMAIL;

import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.customer.data.modules.authentication.model.CustomerUserAuthProfile;
import com.digicore.lucid.customer.data.modules.authentication.repository.CustomerUserAuthProfileRepository;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn 30/05/2025
 */
@Service
@RequiredArgsConstructor
// to be used only as a util for other data access services which require CustomerAuthProfile Entity
// retrieve only
// NB on replication: must be generically fetched entities i.e each query must be for a row which
// has multiple associations
public class CustomerUserAuthProfileHelperService {
  private final CustomerUserAuthProfileRepository customerUserAuthProfileRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  public CustomerUserAuthProfile fetchCustomerAuthProfile(String username, String subDomainName) {
    return customerUserAuthProfileRepository
        .findFirstByUsernameAndSubDomainName(username, subDomainName)
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getUserMessage(NOT_FOUND).replace(EMAIL, username),
                    HttpStatus.BAD_REQUEST));
  }
}
