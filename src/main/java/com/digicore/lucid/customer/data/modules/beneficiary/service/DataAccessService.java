/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.beneficiary.service;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.lucid.common.lib.profile.dto.LucidSearchRequest;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import java.util.List;

/*
 * <AUTHOR>
 * @createdOn 11/03/2025
 */

public interface DataAccessService<T> {
  <C> T create(C data);

  <E> T edit(E data);

  T retrieve(String employeeId);

  List<T> search(String employeeId);

  default Object retrieve(String organizationId, int pageNumber, int pageSize, boolean paginated) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default PaginatedResponseDTO<T> retrieve(LucidSearchRequest lucidSearchRequest) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  void remove(String employeeId);

  boolean exists(String employeeId);

  default Long count(String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }
}
