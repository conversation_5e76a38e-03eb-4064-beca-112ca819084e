/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.authentication.repository;

import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.USER_PROFILE_PROJECTION;

import com.digicore.lucid.common.lib.profile.dto.UserProfileProjection;
import com.digicore.lucid.customer.data.modules.authentication.model.CustomerUserAuthProfile;
import com.digicore.registhentication.registration.enums.Status;
import jakarta.transaction.Transactional;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/*
 * <AUTHOR>
 * @createdOn Feb-11(Tue)-2025
 */

public interface CustomerUserAuthProfileRepository
    extends JpaRepository<CustomerUserAuthProfile, Long> {
  boolean existsByUsernameAndIsDeletedFalseAndCustomerUserProfileCustomerProfileOrganizationId(
      String username, String organizationId);

  boolean existsByUsernameAndIsDeletedFalseAndCustomerUserProfileCustomerProfileBankOrganizationId(
      String username, String bankOrganizationId);

  Optional<CustomerUserAuthProfile>
      findFirstByUsernameAndIsDeletedFalseAndCustomerUserProfileCustomerProfileOrganizationIdOrderByCreatedDate(
          String username, String organizationId);

  @Query(
      "SELECT a "
          + "FROM CustomerUserAuthProfile a WHERE a.username = :username AND a.customerUserProfile.customerProfile.subDomainName = :subDomainName AND a.isDeleted = false")
  Optional<CustomerUserAuthProfile> findFirstByUsernameAndSubDomainName(
      String username, String subDomainName);

  @Query(
      "SELECT new "
          + USER_PROFILE_PROJECTION
          + "(a.customerUserProfile.firstName, a.customerUserProfile.lastName) FROM CustomerUserAuthProfile a WHERE a.assignedRole = :role AND a.customerUserProfile.customerProfile.organizationId = :organizationId")
  List<UserProfileProjection> findAllByAssignedRole(String role, String organizationId);

  @Query(
      "SELECT new "
          + USER_PROFILE_PROJECTION
          + "(a.username, a.customerUserProfile.firstName, a.customerUserProfile.lastName,a.customerUserProfile.email, a.assignedRole, a.customerUserProfile.phoneNumber, a.status,a.lastLoginDate) "
          + "FROM CustomerUserAuthProfile a "
          + "WHERE a.customerUserProfile.customerProfile.organizationId = :organizationId "
          + "AND a.customerUserProfile.customerProfile.bankOrganizationId = :bankOrganizationId")
  Page<UserProfileProjection> findAllByAndOrganizationIdAndBankOrganizationId(
      @Param("organizationId") String organizationId,
      @Param("bankOrganizationId") String bankOrganizationId,
      Pageable pageable);

  @Query(
      "SELECT new "
          + USER_PROFILE_PROJECTION
          + "(a.customerUserProfile.firstName, a.customerUserProfile.customerProfile.organizationId,a.customerUserProfile.customerProfile.bankOrganizationId) FROM CustomerUserAuthProfile a WHERE a.username = :username AND a.customerUserProfile.customerProfile.subDomainName = :subDomain")
  Optional<UserProfileProjection> retrieveOrganizationIdAndBankOrganizationId(
      String username, String subDomain);

  @Query(
      "SELECT new "
          + USER_PROFILE_PROJECTION
          + "(a.status) FROM CustomerUserAuthProfile a WHERE a.username = :username AND a.customerUserProfile.customerProfile.organizationId = :organizationId")
  Optional<UserProfileProjection> findFirstByUsername(
      @Param("username") String username, @Param("organizationId") String organizationId);

  @Transactional
  @Modifying
  @Query(
      value =
          "UPDATE CustomerUserAuthProfile u SET u.status = :status, u.password = :password, u.isDefaultPassword = :isDefaultPassword WHERE u.username = :username AND u.customerUserProfile.id IN (SELECT p.id FROM CustomerUserProfile p WHERE p.customerProfile.organizationId = :organizationId)")
  void updatePassword(
      @Param("status") Status status,
      @Param("password") String password,
      @Param("isDefaultPassword") boolean isDefaultPassword,
      @Param("username") String username,
      @Param("organizationId") String organizationId);

  @Transactional
  @Modifying
  @Query(
      value =
          "UPDATE CustomerUserAuthProfile u SET u.status = :status WHERE u.username = :username AND u.customerUserProfile.id IN (SELECT p.id FROM CustomerUserProfile p WHERE p.customerProfile.organizationId = :organizationId) ")
  void updateStatus(
      @Param("status") Status status,
      @Param("username") String username,
      @Param("organizationId") String organizationId);

  @Transactional
  @Modifying
  @Query(
      value =
          "UPDATE CustomerUserAuthProfile u SET u.username = :newUsername, u.assignedRole = :assignedRole WHERE u.username = :username AND u.customerUserProfile.customerProfile.organizationId = :organizationId")
  void updateUsernameAndRole(
      @Param("username") String username,
      @Param("newUsername") String newUsername,
      @Param("assignedRole") String assignedRole,
      @Param("organizationId") String organizationId);

  @Query(
      "SELECT new "
          + USER_PROFILE_PROJECTION
          + "(a.username, a.customerUserProfile.firstName,"
          + "a.customerUserProfile.lastName,a.customerUserProfile.email, a.assignedRole,a.customerUserProfile.phoneNumber, a.status, a.lastLoginDate)"
          + "FROM CustomerUserAuthProfile a "
          + "WHERE a.customerUserProfile.customerProfile.organizationId = :organizationId "
          + "AND a.customerUserProfile.customerProfile.bankOrganizationId = :bankOrganizationId "
          + "AND a.username = :username")
  Optional<UserProfileProjection> findFirstByUserNameAndOrganizationIdAndBankOrganizationId(
      @Param("username") String username,
      @Param("organizationId") String organizationId,
      @Param("bankOrganizationId") String bankOrganizationId);
}
