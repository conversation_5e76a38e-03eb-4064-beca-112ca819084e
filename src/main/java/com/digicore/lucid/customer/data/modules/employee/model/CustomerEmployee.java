/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.employee.model;

import com.digicore.lucid.common.lib.beneficiary.model.BeneficiaryBaseModel;
import com.digicore.lucid.customer.data.modules.profile.model.CustomerProfile;
import jakarta.persistence.*;
import lombok.*;

/*
 * <AUTHOR>
 * @createdOn 11/03/2025
 */

@Entity
@Table(name = "customer_employee")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CustomerEmployee extends BeneficiaryBaseModel {
  @Column(nullable = false, unique = true)
  private String employeeId;

  private String firstName;
  private String lastName;
  private String email;

  @Column(name = "phone_number")
  private String phoneNumber;

  @Column(name = "pfa_name")
  private String pfaName;

  @Column(name = "pfa_code")
  private String pfaCode;

  private String tin;

  private String minorSalaryAmount;
  private String minorTaxAmount;
  private String minorPensionAmount;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "customer_profile_id")
  private CustomerProfile customerProfile;
}
