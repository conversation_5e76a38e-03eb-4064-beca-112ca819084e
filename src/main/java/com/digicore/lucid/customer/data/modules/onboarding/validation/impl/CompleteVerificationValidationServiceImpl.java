/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.onboarding.validation.impl;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.message.MessagePlaceHolderConstant.EMAIL;
import static com.digicore.lucid.common.lib.notification.constant.MailTemplateConstant.WELCOME;
import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;
import static com.digicore.lucid.customer.data.modules.onboarding.validation.util.ValidationUtil.setNextFlow;
import static com.digicore.lucid.integration.lib.modules.service.account.request.AccountServiceType.FETCH_ALL_ACCOUNTS;
import static com.digicore.lucid.integration.lib.modules.service.account.request.AccountServiceType.FETCH_CUSTOMER_DETAIL;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.api.helper.response.ApiError;
import com.digicore.api.helper.response.ApiResponseJson;
import com.digicore.lucid.common.lib.account.dto.CustomerAccountDTO;
import com.digicore.lucid.common.lib.account.service.AccountService;
import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.client.CbaFeignClient;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.notification.config.MailPropertyConfig;
import com.digicore.lucid.common.lib.notification.request.NotificationRequestType;
import com.digicore.lucid.common.lib.notification.request.NotificationServiceRequest;
import com.digicore.lucid.common.lib.notification.service.NotificationDispatcher;
import com.digicore.lucid.common.lib.profile.dto.BankPreferenceDTO;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.customer.*;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.onboarding.dto.CustomerOnboardingFlowDTO;
import com.digicore.lucid.customer.data.modules.onboarding.dto.OnboardingResponseDTO;
import com.digicore.lucid.customer.data.modules.onboarding.model.CustomerFlow;
import com.digicore.lucid.customer.data.modules.onboarding.repository.CustomerFlowRepository;
import com.digicore.lucid.customer.data.modules.onboarding.repository.CustomerPhishingImageRepository;
import com.digicore.lucid.customer.data.modules.onboarding.repository.CustomerSecurityQuestionRepository;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationAction;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationService;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationType;
import com.digicore.lucid.customer.data.modules.onboarding.validation.util.ValidationUtil;
import com.digicore.lucid.integration.lib.modules.config.properties.SecurityPropertyConfig;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.account.request.OrganizationFetchDetailRequest;
import com.digicore.lucid.integration.lib.modules.service.account.response.OrganizationFetchAccountsResponse;
import com.digicore.lucid.integration.lib.modules.service.account.response.OrganizationFetchCustomerDetailResponse;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.services.RegistrationService;
import com.digicore.registhentication.validator.enums.Currency;
import jakarta.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Ucheagwu
 * @createdOn Jun-10(Tue)-2025
 */
@Slf4j
@Service
@Qualifier("COMPLETE_REGISTRATION") @RequiredArgsConstructor
public class CompleteVerificationValidationServiceImpl implements ValidationService {
  private final CustomerFlowRepository customerFlowRepository;
  private final RegistrationService<UserProfileDTO, CustomerRegistrationDTO>
      customerProfileSelfRegistrationService;
  private final CbaFeignClient cbaFeignClient;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final MailPropertyConfig mailPropertyConfig;
  private final AccountService<CustomerAccountDTO> customerAccountService;
  private final NotificationDispatcher notificationDispatcher;
  private final RedissonClient redissonClient;
  private final SecurityPropertyConfig securityPropertyConfig;
  private final AuthProfileService<UserAuthProfileDTO> customerUserAuthProfileService;
  private final CustomerPhishingImageRepository customerPhishingImageRepository;
  private final CustomerSecurityQuestionRepository customerSecurityQuestionRepository;

  @Override
  public Object process(
      CustomerOnboardingFlowDTO customerOnboardingFlowDTO,
      String subDomain,
      String action,
      String validationTypes) {
    //    retrieve customer flow
    CustomerFlow customerFlow =
        ValidationUtil.getCustomerFlow(
            customerFlowRepository, customerOnboardingFlowDTO.getSessionId());
    ValidationUtil.ensureOnboardingNotCompleted(customerFlow);

    if (!action.equalsIgnoreCase(ValidationAction.INITIATE.getAction())) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST,
          List.of(new ApiError("Invalid action for complete onboarding process")));
    }

    //    check if username exists in db
    if (customerUserAuthProfileService.authProfileExist(
        customerOnboardingFlowDTO.getUsername(),
        RequestContextHolder.get().getBankOrganizationId())) {
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig
              .getUserMessage(DUPLICATE)
              .replace(EMAIL, customerOnboardingFlowDTO.getUsername()),
          HttpStatus.BAD_REQUEST);
    }

    //    get customer details from cba
    ApiResponseJson<Object> response;
    try {
      response =
          cbaFeignClient.processAccountRequest(
              RequestContextHolder.get().getCbaProvider(),
              FETCH_CUSTOMER_DETAIL,
              OrganizationFetchDetailRequest.builder()
                  .accountNumber(customerFlow.getAccountNumber())
                  .build());
    } catch (Exception e) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getProfileMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    }
    Object responseBody = response.getData();
    OrganizationFetchCustomerDetailResponse userFromCBA =
        getObjectMapper().convertValue(responseBody, OrganizationFetchCustomerDetailResponse.class);

    if (!userFromCBA.getResponseStatus().equals(CbaProvider.ResponseStatus.COMPLETED)) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getProfileMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    }

    if (ClientUtil.nullOrEmpty(userFromCBA.getFirstName())) {
      userFromCBA.setFirstName(customerFlow.getFirstName());
    }
    userFromCBA.setEmail(customerFlow.getEmail());

    //    get customer accounts from cba
    ApiResponseJson<Object> accountResponse;
    try {
      accountResponse =
          cbaFeignClient.processAccountRequest(
              RequestContextHolder.get().getCbaProvider(),
              FETCH_ALL_ACCOUNTS,
              OrganizationFetchDetailRequest.builder()
                  .customerId(customerFlow.getCbaCustomerId())
                  .build());
    } catch (Exception e) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    }
    Object accountsResponseBody = accountResponse.getData();
    OrganizationFetchAccountsResponse accountsFromCBA =
        getObjectMapper()
            .convertValue(accountsResponseBody, OrganizationFetchAccountsResponse.class);

    if (!accountsFromCBA.getResponseStatus().equals(CbaProvider.ResponseStatus.COMPLETED)) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    }

    //    call create profile service -> create user profile -> create auth_user profile -> create
    // user account
    UserProfileDTO userProfileDTO =
        customerProfileSelfRegistrationService.createProfile(
            getCustomerRegistrationDTO(
                userFromCBA, customerOnboardingFlowDTO, customerFlow.getPhoneNumber()));

    List<CustomerOnboardingDTO.Account> accountsToCreate = new ArrayList<>();
    for (OrganizationFetchAccountsResponse.Accounts account : accountsFromCBA.getAccounts()) {
      CustomerOnboardingDTO.Account customerAccount = new CustomerOnboardingDTO.Account();
      customerAccount.setAccountNumber(account.getAccountNumber());
      customerAccount.setCurrency(
          ClientUtil.nullOrEmpty(account.getCurrency())
              ? Currency.NGN.toString()
              : account.getCurrency());
      customerAccount.setAccountType(
          ClientUtil.nullOrEmpty(account.getAccountType())
              ? "SavingsOrCurrent"
              : account.getAccountType());
      customerAccount.setAccountName(
          ClientUtil.nullOrEmpty(account.getAccountName())
              ? userProfileDTO.getLastName() + " " + userProfileDTO.getFirstName()
              : account.getAccountName());
      accountsToCreate.add(customerAccount);
    }
    customerAccountService.addAccount(userProfileDTO.getOrganizationId(), accountsToCreate);

    //    notification service sends welcome email
    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient.getBucket(userProfileDTO.getSubDomain().concat("-PREFERENCE")).get();

    notificationDispatcher.dispatchNotification(
        NotificationServiceRequest.builder()
            .notificationSubject(messagePropertyConfig.getEmailMessage(ONBOARDING_SUBJECT))
            .recipients(List.of(customerFlow.getEmail()))
            .dateTime(LocalDateTime.now())
            .userCode(userProfileDTO.getPassword())
            .userRole(userProfileDTO.getAssignedRole())
            .userName(userProfileDTO.getUsername())
            .customerName(
                bankPreferenceDTO == null
                    ? userProfileDTO.getSubDomain()
                    : bankPreferenceDTO.getTheme().getBankName())
            .firstName(userProfileDTO.getFirstName())
            .userName(userProfileDTO.getUsername())
            .bankName(
                bankPreferenceDTO == null
                    ? userProfileDTO.getSubDomain()
                    : bankPreferenceDTO.getTheme().getBankName())
            .logoLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getTheme().getLogo())
            .subDomain(userProfileDTO.getSubDomain())
            .domain(securityPropertyConfig.getDomain())
            .supportMailLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportEmail())
            .helpUrl(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportUrl())
            .notificationRequestType(NotificationRequestType.SEND_WELCOME_EMAIL)
            .build(),
        mailPropertyConfig.getTemplate(WELCOME));

    //    set action to completed
    customerFlow =
        setNextFlow(
            customerFlowRepository, customerFlow, ValidationType.COMPLETE_REGISTRATION, null);

    for (String validation : customerFlow.getValidations().split(",")) {
      if (validation.equalsIgnoreCase(ValidationType.SECURITY_QUESTION.getType())) {
        customerSecurityQuestionRepository
            .findFirstByAccountNumberAndIsDeletedFalseOrderByCreatedDateDesc(
                customerFlow.getAccountNumber())
            .ifPresent(
                securityQuestion -> {
                  securityQuestion.setUsername(customerOnboardingFlowDTO.getUsername());
                  securityQuestion.setOrganizationId(userProfileDTO.getOrganizationId());
                  securityQuestion.setBankOrganizationId(userProfileDTO.getBankOrganizationId());
                  customerSecurityQuestionRepository.save(securityQuestion);
                });
      }
      if (validation.equalsIgnoreCase(ValidationType.PHISHING_IMAGE.getType())) {
        customerPhishingImageRepository
            .findFirstByAccountNumberAndIsDeletedFalseOrderByCreatedDateDesc(
                customerFlow.getAccountNumber())
            .ifPresent(
                phishingImage -> {
                  phishingImage.setUsername(customerOnboardingFlowDTO.getUsername());
                  phishingImage.setOrganizationId(userProfileDTO.getOrganizationId());
                  phishingImage.setBankOrganizationId(userProfileDTO.getBankOrganizationId());
                  customerPhishingImageRepository.save(phishingImage);
                });
      }
    }

    return OnboardingResponseDTO.builder()
        .sessionId(customerFlow.getSessionId())
        .currentFlow(
            ValidationType.COMPLETE_REGISTRATION
                .getType()
                .concat(".")
                .concat(ValidationAction.INITIATE.getAction()))
        .message("Customer onboarding completed successfully.")
        .nextFlow(null)
        .build();
  }

  private static CustomerRegistrationDTO getCustomerRegistrationDTO(
      OrganizationFetchCustomerDetailResponse cbaUser,
      CustomerOnboardingFlowDTO onboardingFlowDTO,
      String phoneNumber) {
    CustomerDocumentDTO customerDocumentDTO = new CustomerDocumentDTO();
    customerDocumentDTO.setDocumentType("NIN");
    customerDocumentDTO.setDocumentNumber(cbaUser.getNationalIdentityNo());

    CustomerPersonalDetailDTO customerPersonalDetailDTO = new CustomerPersonalDetailDTO();
    BeanUtilWrapper.copyNonNullProperties(cbaUser, customerPersonalDetailDTO);
    customerPersonalDetailDTO.setCustomerDocuments(List.of(customerDocumentDTO));
    customerPersonalDetailDTO.setPhoneNumber(phoneNumber);
    customerPersonalDetailDTO.setBvn(cbaUser.getBankVerificationNumber());
    customerPersonalDetailDTO.setDob(cbaUser.getDateOfBirth());
    customerPersonalDetailDTO.setUsername(onboardingFlowDTO.getUsername());
    customerPersonalDetailDTO.setPassword(onboardingFlowDTO.getPassword());

    CustomerRegistrationDTO customerRegistrationDTO = new CustomerRegistrationDTO();
    customerRegistrationDTO.setBankOrganizationId(
        RequestContextHolder.get().getBankOrganizationId());
    customerRegistrationDTO.setSubDomain(RequestContextHolder.get().getSubDomainName());
    customerRegistrationDTO.setCustomerPersonalDetailDTO(customerPersonalDetailDTO);

    CustomerBusinessDetailDTO customerBusinessDetailDTO = new CustomerBusinessDetailDTO();
    List<OrganizationFetchCustomerDetailResponse.Account> cbaUserAccounts = cbaUser.getAccounts();
    if (cbaUserAccounts != null && !cbaUserAccounts.isEmpty()) {
      customerBusinessDetailDTO.setCbaOrganizationId(cbaUserAccounts.getFirst().getCustomerId());
    }
    customerBusinessDetailDTO.setAddress(cbaUser.getAddress());
    customerRegistrationDTO.setCustomerBusinessDetailDTO(customerBusinessDetailDTO);
    return customerRegistrationDTO;
  }
}
