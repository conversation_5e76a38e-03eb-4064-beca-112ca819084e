/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.limit.repository;

import com.digicore.lucid.common.lib.limit.dto.CustomerLimitConfigDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.customer.data.modules.limit.model.CustomerLimitConfig;
import com.digicore.registhentication.validator.enums.Currency;
import jakarta.transaction.Transactional;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/*
 * <AUTHOR>
 * @createdOn Mar-05(Wed)-2025
 */
public interface CustomerLimitConfigRepository extends JpaRepository<CustomerLimitConfig, Long> {

  //  @Query(
  //      """
  //          SELECT new com.digicore.lucid.common.lib.limit.dto.LimitConfigDTO(
  //          c.limitType, c.currency,
  //          c.minorMobileSingleCap, c.minorWebSingleCap,
  //          c.minorMobileCumulativeCap, c.minorWebCumulativeCap,
  //          c.minorMobileSingleCappedMessage, c.minorWebSingleCappedMessage,
  //          c.minorMobileCumulativeCappedMessage, c.minorWebCumulativeCappedMessage,
  //          c.unlimited, c.defaultLimit )
  //          FROM CustomerLimitConfig c
  //          WHERE c.customerAccount.accountNumber = :accountNumber
  //          AND c.customerAccount.customerProfile.organizationId = :organizationId
  //          AND c.customerAccount.customerProfile.bankOrganizationId = :bankOrganizationId
  //          """)
  //  Optional<LimitConfigDTO> findFirstLimitConfigByAccountNumberAndOrganizationId(
  //      @Param("accountNumber") String accountNumber,
  //      @Param("organizationId") String organizationId,
  //      @Param("bankOrganizationId") String bankOrganizationId);

  @Query(
      """
          SELECT new com.digicore.lucid.common.lib.limit.dto.CustomerLimitConfigDTO(
          c.limitType, c.currency,
          c.minorMobileSingleCap, c.minorWebSingleCap,
          c.minorMobileCumulativeCap, c.minorWebCumulativeCap,
          c.minorMobileSingleCappedMessage,
          c.minorWebSingleCappedMessage,
          c.minorMobileCumulativeCappedMessage,
          c.minorWebCumulativeCappedMessage,
          c.unlimited, c.customerAccount.accountNumber, c.customerAccount.customerProfile.organizationId, c.customerAccount.customerProfile.bankOrganizationId )
          FROM CustomerLimitConfig c
          WHERE c.customerAccount.accountNumber = :accountNumber
          AND c.customerAccount.customerProfile.organizationId = :organizationId
          AND c.limitType = :limitType
          AND c.currency = :currency
          """)
  Optional<CustomerLimitConfigDTO>
      findFirstLimitConfigByAccountNumberAndOrganizationIdAndLimitTypeAndCurrency(
          @Param("accountNumber") String accountNumber,
          @Param("organizationId") String organizationId,
          @Param("limitType") LimitType limitType,
          @Param("currency") Currency currency);

  @Query(
      """
              SELECT new com.digicore.lucid.common.lib.limit.dto.CustomerLimitConfigDTO(
              c.limitType, c.currency,
              c.minorMobileSingleCap, c.minorWebSingleCap,
              c.minorMobileCumulativeCap, c.minorWebCumulativeCap,
              c.minorMobileSingleCappedMessage,
              c.minorWebSingleCappedMessage,
              c.minorMobileCumulativeCappedMessage,
              c.minorWebCumulativeCappedMessage,
              c.unlimited, c.customerAccount.accountNumber, c.customerAccount.customerProfile.organizationId, c.customerAccount.customerProfile.bankOrganizationId )
              FROM CustomerLimitConfig c
              WHERE c.customerAccount.accountNumber = :accountNumber
              AND c.customerAccount.customerProfile.organizationId = :organizationId
              AND c.customerAccount.customerProfile.bankOrganizationId = :bankOrganizationId
              AND c.limitType = :limitType
              AND c.currency = :currency
              """)
  Optional<CustomerLimitConfigDTO>
      findFirstLimitConfigByAccountNumberAndOrganizationIdAndLimitTypeAndCurrency(
          @Param("accountNumber") String accountNumber,
          @Param("bankOrganizationId") String bankOrganizationId,
          @Param("organizationId") String organizationId,
          @Param("limitType") LimitType limitType,
          @Param("currency") Currency currency);

  @Query(
      """
          SELECT new com.digicore.lucid.common.lib.limit.dto.CustomerLimitConfigDTO(
          c.limitType, c.currency,
          c.minorMobileSingleCap, c.minorWebSingleCap,
          c.minorMobileCumulativeCap, c.minorWebCumulativeCap,
          c.unlimited, c.customerAccount.accountNumber, c.customerAccount.customerProfile.organizationId )
          FROM CustomerLimitConfig c
          WHERE c.customerAccount.accountNumber IN :accountNumbers
          AND c.customerAccount.customerProfile.organizationId = :organizationId
          """)
  List<CustomerLimitConfigDTO> findByAccountNumbersAndOrganizationId(
      @Param("accountNumbers") List<String> accountNumbers,
      @Param("organizationId") String organizationId);

  @Query(
      """
              SELECT new com.digicore.lucid.common.lib.limit.dto.CustomerLimitConfigDTO(
              c.limitType, c.currency,
              c.minorMobileSingleCap, c.minorWebSingleCap,
              c.minorMobileCumulativeCap, c.minorWebCumulativeCap,
              c.minorMobileSingleCappedMessage,
              c.minorWebSingleCappedMessage,
              c.minorMobileCumulativeCappedMessage,
              c.minorWebCumulativeCappedMessage,
              c.unlimited, c.customerAccount.accountNumber, c.customerAccount.customerProfile.organizationId, c.customerAccount.customerProfile.bankOrganizationId )
              FROM CustomerLimitConfig c
              WHERE c.customerAccount.customerProfile.organizationId = :organizationId
              """)
  Page<CustomerLimitConfigDTO> findByOrganizationId(
      @Param("organizationId") String organizationId, Pageable pageable);

  @Query(
      """
                  SELECT new com.digicore.lucid.common.lib.limit.dto.CustomerLimitConfigDTO(
                  c.limitType, c.currency,
                  c.minorMobileSingleCap, c.minorWebSingleCap,
                  c.minorMobileCumulativeCap, c.minorWebCumulativeCap,
                  c.minorMobileSingleCappedMessage,
                  c.minorWebSingleCappedMessage,
                  c.minorMobileCumulativeCappedMessage,
                  c.minorWebCumulativeCappedMessage,
                  c.unlimited, c.customerAccount.accountNumber, c.customerAccount.customerProfile.organizationId, c.customerAccount.customerProfile.bankOrganizationId )
                  FROM CustomerLimitConfig c
                  WHERE c.customerAccount.customerProfile.organizationId = :organizationId
                  AND c.customerAccount.customerProfile.bankOrganizationId = :bankOrganizationId
                  """)
  Page<CustomerLimitConfigDTO> findByOrganizationIdAndBankOrganizationId(
      @Param("organizationId") String organizationId,
      @Param("bankOrganizationId") String bankOrganizationId,
      Pageable pageable);

  @Query(
      """
                  SELECT new com.digicore.lucid.common.lib.limit.dto.CustomerLimitConfigDTO(
                  c.limitType, c.currency,
                  c.minorMobileSingleCap, c.minorWebSingleCap,
                  c.minorMobileCumulativeCap, c.minorWebCumulativeCap,
                  c.unlimited, c.customerAccount.accountNumber, c.customerAccount.customerProfile.organizationId )
                  FROM CustomerLimitConfig c
                  WHERE c.customerAccount.accountNumber = :accountNumber
                  AND c.customerAccount.customerProfile.organizationId = :organizationId
                  """)
  List<CustomerLimitConfigDTO> findByOrganizationIdAndBankOrganizationId(
      @Param("accountNumber") String accountNumber, @Param("organizationId") String organizationId);

  @Transactional
  @Modifying
  @Query(
      """
    UPDATE CustomerLimitConfig clc
     SET
        clc.minorMobileSingleCap = :minorMobileSingleCap,
        clc.minorWebSingleCap = :minorWebSingleCap,
        clc.minorMobileCumulativeCap = :minorMobileCumulativeCap,
        clc.minorWebCumulativeCap = :minorWebCumulativeCap,
        clc.minorMobileSingleCappedMessage = :minorMobileSingleCappedMessage,
        clc.minorWebSingleCappedMessage = :minorWebSingleCappedMessage,
        clc.minorMobileCumulativeCappedMessage = :minorMobileCumulativeCappedMessage,
        clc.minorWebCumulativeCappedMessage = :minorWebCumulativeCappedMessage,
        clc.unlimited = :unlimited,
        clc.defaultLimit = :defaultLimit
    WHERE
        clc.limitType = :limitType
        AND clc.currency = :currency
        AND clc.customerAccount.accountNumber = :accountNumber
        AND clc.customerAccount.customerProfile.organizationId = :organizationId
        AND clc.customerAccount.customerProfile.bankOrganizationId = :bankOrganizationId
""")
  void updateCustomerLimitConfig(
      @Param("minorMobileSingleCap") String minorMobileSingleCap,
      @Param("minorWebSingleCap") String minorWebSingleCap,
      @Param("minorMobileCumulativeCap") String minorMobileCumulativeCap,
      @Param("minorWebCumulativeCap") String minorWebCumulativeCap,
      @Param("minorMobileSingleCappedMessage") String minorMobileSingleCappedMessage,
      @Param("minorWebSingleCappedMessage") String minorWebSingleCappedMessage,
      @Param("minorMobileCumulativeCappedMessage") String minorMobileCumulativeCappedMessage,
      @Param("minorWebCumulativeCappedMessage") String minorWebCumulativeCappedMessage,
      @Param("unlimited") boolean unlimited,
      @Param("defaultLimit") boolean defaultLimit,
      @Param("limitType") LimitType limitType,
      @Param("currency") Currency currency,
      @Param("accountNumber") String accountNumber,
      @Param("organizationId") String organizationId,
      @Param("bankOrganizationId") String bankOrganizationId);

  boolean
      existsByLimitTypeAndCurrencyAndCustomerAccountAccountNumberAndCustomerAccountCustomerProfileOrganizationIdAndCustomerAccountCustomerProfileBankOrganizationId(
          LimitType limitType,
          Currency currency,
          String accountNumber,
          String organizationId,
          String bankOrganizationId);
}
