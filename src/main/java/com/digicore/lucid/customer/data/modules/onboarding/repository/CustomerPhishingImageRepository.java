/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.onboarding.repository;

import com.digicore.lucid.customer.data.modules.onboarding.model.CustomerPhishingImage;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * <AUTHOR> <PERSON>
 * @createdOn Jun-11(Wed)-2025
 */

public interface CustomerPhishingImageRepository
    extends JpaRepository<CustomerPhishingImage, Long> {

  Optional<CustomerPhishingImage> findFirstByAccountNumberAndIsDeletedFalseOrderByCreatedDateDesc(
      String accountNumber);

  Optional<CustomerPhishingImage>
      findFirstByUsernameAndBankOrganizationIdAndOrganizationIdAndIsDeletedFalseOrderByCreatedDateDesc(
          String username, String bankOrganizationId, String organizationId);
}
