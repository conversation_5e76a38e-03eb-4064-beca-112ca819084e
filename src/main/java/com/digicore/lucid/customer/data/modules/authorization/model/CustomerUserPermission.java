/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.authorization.model;

import com.digicore.registhentication.security.authorization.models.PermissionBaseModel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/*
 * <AUTHOR>
 * @createdOn Feb-11(Tue)-2025
 */

@Entity
@Table(name = "customer_user_permission")
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@NoArgsConstructor
@ToString
public class CustomerUserPermission extends PermissionBaseModel implements Serializable {}
