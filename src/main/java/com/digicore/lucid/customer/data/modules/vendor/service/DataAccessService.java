/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.data.modules.vendor.service;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.lucid.common.lib.profile.dto.LucidSearchRequest;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;

/*
 * <AUTHOR>
 * @createdOn 11/03/2025
 */

public interface DataAccessService<T> {
  <C> T create(C data);

  <E> T edit(E data);

  T retrieve(String employeeId);

  default PaginatedResponseDTO<T> retrieve(String organizationId, int pageNumber, int pageSize) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default PaginatedResponseDTO<T> retrieve(LucidSearchRequest lucidSearchRequest) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  void remove(String vendorId);

  boolean exists(String vendorId, String accountNumber);
}
