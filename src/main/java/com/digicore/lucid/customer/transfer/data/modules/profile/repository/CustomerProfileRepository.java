/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.data.modules.profile.repository;

import com.digicore.lucid.customer.transfer.data.modules.profile.model.CustomerProfile;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

/*
 * <AUTHOR>
 * @createdOn Mar-08(Sat)-2025
 */

public interface CustomerProfileRepository extends JpaRepository<CustomerProfile, Long> {
  Optional<CustomerProfile> findFirstByOrganizationIdAndIsDeletedFalseOrderByCreatedDateDesc(
      String organizationId);
}
