/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.notification.constant.MailTemplateConstant.OTP_NOTIFICATION;
import static com.digicore.lucid.common.lib.notification.constant.MailTemplateConstant.TRANSACTION_PURPOSE;
import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;
import static com.digicore.lucid.integration.lib.modules.service.transfer.request.TransferServiceType.FETCH_INSTITUTIONS;

import com.digicore.api.helper.response.ApiResponseJson;
import com.digicore.lucid.common.lib.client.CbaFeignClient;
import com.digicore.lucid.common.lib.notification.config.MailPropertyConfig;
import com.digicore.lucid.common.lib.notification.request.NotificationRequestType;
import com.digicore.lucid.common.lib.notification.request.NotificationServiceRequest;
import com.digicore.lucid.common.lib.otp.enums.OtpType;
import com.digicore.lucid.common.lib.otp.service.OtpService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.account.request.OrganizationFetchAccountsRequest;
import com.digicore.lucid.integration.lib.modules.service.transfer.response.InstitutionResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-11(Tue)-2025
 */
@Service
@RequiredArgsConstructor
public class TransferHelperService {
  private final OtpService otpService;
  private final MessagePropertyConfig messagePropertyConfig;
  private final MailPropertyConfig mailPropertyConfig;
  private final CbaFeignClient cbaFeignClient;

  public void sendCode() {
    // todo this should send to phone number as well, need to handle this on notification service
    otpService.send(
        NotificationServiceRequest.builder()
            .firstName(ClientUtil.getValueFromAccessToken("name"))
            .recipients(List.of(ClientUtil.getValueFromAccessToken("email")))
            .purpose(TRANSACTION_PURPOSE)
            .channel("EMAIL")
            .notificationSubject(messagePropertyConfig.getTransferMessage(TRANSACTION_CODE_SUBJECT))
            .notificationRequestType(NotificationRequestType.SEND_ON_BOARDING_OTP_EMAIL)
            .build(),
        OtpType.TRANSACTION_AUTHORIZATION,
        mailPropertyConfig.getTemplate(OTP_NOTIFICATION));
  }

  public List<InstitutionResponse.InstitutionResponseData> fetchInstitutions() {
    ApiResponseJson<Object> response =
        cbaFeignClient.processTransferRequest(
            FETCH_INSTITUTIONS, new OrganizationFetchAccountsRequest());
    Object responseBody = response.getData();
    InstitutionResponse cbaResponse =
        getObjectMapper().convertValue(responseBody, InstitutionResponse.class);
    if (CbaProvider.ResponseStatus.COMPLETED.equals(cbaResponse.getResponseStatus()))
      return cbaResponse.getInstitutionResponseData();

    return List.of();
  }
}
