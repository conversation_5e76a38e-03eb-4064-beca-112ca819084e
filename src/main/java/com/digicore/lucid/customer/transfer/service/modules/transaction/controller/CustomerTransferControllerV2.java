/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V2;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.UPLOAD_API;
import static com.digicore.lucid.common.lib.swagger.constant.transfer.TransferSwaggerDocConstant.*;
import static com.digicore.lucid.integration.lib.modules.api.AccountApiControllerConstant.REQUEST;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferBulkDTO;
import com.digicore.lucid.common.lib.transaction.dto.transfer.UploadNameEnquiryDTO;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.transfer.service.modules.transaction.router.NameEnquiryServiceRouter;
import com.digicore.lucid.customer.transfer.service.modules.transaction.router.TransferServiceRouter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-03(Mon)-2025
 */
@RestController
@RequestMapping(API_V2 + TRANSFER_API)
@Tag(name = TRANSFER_CONTROLLER_TITLE, description = TRANSFER_CONTROLLER_DESCRIPTION)
@RequiredArgsConstructor
public class CustomerTransferControllerV2 {
  private final TransferServiceRouter transferServiceRouter;
  private final NameEnquiryServiceRouter nameEnquiryServiceRouter;

  @PostMapping(REQUEST)
  public ResponseEntity<Object> process(@RequestBody TransferBulkDTO transferBulkDTO) {
    transferBulkDTO.setInitiator(ClientUtil.getValueFromAccessToken("name"));
    return ControllerResponse.buildSuccessResponse(transferServiceRouter.process(transferBulkDTO));
  }

  @PostMapping(NAME_ENQUIRY_API)
  public ResponseEntity<Object> retrieveInstitutions(
      @RequestBody List<UploadNameEnquiryDTO> nameEnquiryDTO) {
    return ControllerResponse.buildSuccessResponse(
        nameEnquiryServiceRouter.process(nameEnquiryDTO));
  }

  @PostMapping(UPLOAD_API + "-" + NAME_ENQUIRY_API)
  public ResponseEntity<Object> retrieveInstitutions(@RequestParam("file") MultipartFile file) {
    return ControllerResponse.buildSuccessResponse(nameEnquiryServiceRouter.process(file));
  }
}
