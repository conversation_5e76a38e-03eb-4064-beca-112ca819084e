/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.history.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.RECEIPT;

import com.digicore.lucid.common.lib.client.CustomerFeignClient;
import com.digicore.lucid.common.lib.export.util.PdfUtil;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.notification.config.TemplatePropertyConfig;
import com.digicore.lucid.common.lib.profile.dto.BankPreferenceDTO;
import com.digicore.lucid.common.lib.profile.dto.LucidSearchRequest;
import com.digicore.lucid.common.lib.transaction.dto.transfer.FXTransferDTO;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferBulkDTO;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferDTO;
import com.digicore.lucid.common.lib.transaction.enums.TransferCategory;
import com.digicore.lucid.common.lib.util.MoneyUtil;
import com.digicore.lucid.customer.transfer.data.modules.transfer.service.DataAccessService;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ilori
 * @createdOn 04/03/2025
 */

@Service
@RequiredArgsConstructor
public class CustomerTransferHistoryOperations {
  private final CustomerFeignClient customerFeignClient;
  private final DataAccessService<TransferDTO> customerSingleTransferDataAccessService;
  private final DataAccessService<FXTransferDTO> customerFxTransferDataAccessService;
  private final DataAccessService<TransferBulkDTO> customerBulkTransferDataAccessService;
  private final PdfUtil pdfUtil;
  private final TemplatePropertyConfig templatePropertyConfig;
  private final RedissonClient redissonClient;

  public PaginatedResponseDTO<TransferDTO> fetchTransferHistory(
      String accountNumber, int pageNo, int pageSize) {
    validateAccountNumber(accountNumber);
    return customerSingleTransferDataAccessService.retrieve(accountNumber, pageNo, pageSize);
  }

  public PaginatedResponseDTO<TransferBulkDTO> fetchBulkTransferHistory(
      String accountNumber, TransferCategory transferCategory, int pageNo, int pageSize) {
    validateAccountNumber(accountNumber);
    return customerBulkTransferDataAccessService.retrieveByCategory(
        accountNumber, transferCategory, pageNo, pageSize);
  }

  public Object fetchBulkTransferEntriesHistory(
      String bulkTransactionReference, int pageNo, int pageSize) {
    return customerBulkTransferDataAccessService.retrieveAll(
        bulkTransactionReference, pageNo, pageSize);
  }

  public Object fetchBulkTransferEntriesHistoryByIdentifier(
      String identifier, int pageNo, int pageSize) {
    return customerBulkTransferDataAccessService.retrieveByIdentifier(identifier, pageNo, pageSize);
  }

  public PaginatedResponseDTO<TransferDTO> filterTransferHistory(
      String accountNumber, LocalDate startDate, LocalDate endDate, int pageNumber, int pageSize) {
    validateAccountNumber(accountNumber);
    LucidSearchRequest lucidSearchRequest = new LucidSearchRequest();
    lucidSearchRequest.setPage(pageNumber);
    lucidSearchRequest.setSize(pageSize);
    lucidSearchRequest.setForFilter(true);
    lucidSearchRequest.setStartDate(startDate == null ? null : startDate.toString());
    lucidSearchRequest.setEndDate(endDate == null ? null : endDate.toString());
    lucidSearchRequest.setParam(accountNumber);
    return customerSingleTransferDataAccessService.retrieve(lucidSearchRequest);
  }

  private void validateAccountNumber(String accountNumber) {
    String customerOrganizationId = RequestContextHolder.get().getOrganizationId();
    customerFeignClient.verifyAccountNumber(customerOrganizationId, accountNumber);
  }

  public byte[] exportReceipt(String transactionId) {
    TransferDTO transferDTO = customerSingleTransferDataAccessService.retrieve(transactionId);
    Map<String, Object> context = prepareContextVariables(transferDTO);
    return pdfUtil.generatePdf(context, templatePropertyConfig.getTransferTemplate(RECEIPT));
  }

  public byte[] exportFxReceipt(String transactionId) {
    FXTransferDTO transferDTO = customerFxTransferDataAccessService.retrieve(transactionId);
    Map<String, Object> context = prepareContextVariables(transferDTO);
    return pdfUtil.generatePdf(context, templatePropertyConfig.getTransferTemplate(RECEIPT));
  }

  private Map<String, Object> prepareContextVariables(TransferDTO transferDTO) {
    HashMap<String, Object> context = new HashMap<>();
    context.put(
        "date",
        transferDTO
            .getTransactionDate()
            .format(DateTimeFormatter.ofPattern("MMM d, yyyy, HH:mm:ss")));
    context.put("transactionRef", transferDTO.getTransactionReference());
    context.put(
        "amount",
        transferDTO
            .getCurrency()
            .name()
            .concat(" ")
            .concat(MoneyUtil.convertToFormattedMajor(transferDTO.getAmountInMinor())));
    context.put("senderName", transferDTO.getSenderAccountName());
    context.put("senderAccountNumber", transferDTO.getSenderAccountNumber());
    context.put("beneficiaryBankName", transferDTO.getBeneficiaryBankName());
    context.put("beneficiaryName", transferDTO.getBeneficiaryAccountName());
    context.put("beneficiaryAccountNumber", transferDTO.getBeneficiaryAccountNumber());
    context.put("status", transferDTO.getTransactionStatus());
    context.put("narration", transferDTO.getNarration());
    context.put(
        "channel",
        transferDTO
            .getTransferType()
            .name()
            .toLowerCase()
            .replace("_", " ")
            .concat(" ")
            .concat(transferDTO.getChannel().name()));
    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();
    context.put("logoUrl", bankPreferenceDTO != null ? bankPreferenceDTO.getTheme().getLogo() : "");

    return context;
  }

  private Map<String, Object> prepareContextVariables(FXTransferDTO transferDTO) {
    HashMap<String, Object> context = new HashMap<>();
    context.put(
        "date",
        transferDTO
            .getTransactionDate()
            .format(DateTimeFormatter.ofPattern("MMM d, yyyy, HH:mm:ss")));
    context.put("transactionRef", transferDTO.getTransactionReference());
    context.put(
        "amount",
        transferDTO
            .getBeneficiaryAccountCurrency()
            .name()
            .concat(" ")
            .concat(MoneyUtil.convertToFormattedMajor(transferDTO.getAmountInMinor())));
    context.put("senderName", transferDTO.getSenderAccountName());
    context.put("senderAccountNumber", transferDTO.getSenderAccountNumber());
    context.put("beneficiaryBankName", transferDTO.getBeneficiaryBankName());
    context.put("beneficiaryName", transferDTO.getBeneficiaryAccountName());
    context.put("beneficiaryAccountNumber", transferDTO.getBeneficiaryAccountNumber());
    context.put("status", transferDTO.getTransactionStatus());
    context.put("narration", transferDTO.getNarration());
    context.put(
        "channel",
        transferDTO
            .getTransferType()
            .name()
            .toLowerCase()
            .replace("_", " ")
            .concat(" ")
            .concat(transferDTO.getChannel().name()));
    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();
    context.put("logoUrl", bankPreferenceDTO != null ? bankPreferenceDTO.getTheme().getLogo() : "");

    return context;
  }
}
