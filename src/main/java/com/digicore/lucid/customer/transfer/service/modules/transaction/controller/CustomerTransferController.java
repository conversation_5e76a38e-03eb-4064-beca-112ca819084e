/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.SEND_CODE_API;
import static com.digicore.lucid.common.lib.swagger.constant.transfer.TransferSwaggerDocConstant.*;
import static com.digicore.lucid.integration.lib.modules.api.AccountApiControllerConstant.REQUEST;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.transaction.dto.transfer.NameEnquiryDTO;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferDTO;
import com.digicore.lucid.customer.transfer.service.modules.transaction.router.NameEnquiryServiceRouter;
import com.digicore.lucid.customer.transfer.service.modules.transaction.router.TransferServiceRouter;
import com.digicore.lucid.customer.transfer.service.modules.transaction.service.TransferHelperService;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-03(Mon)-2025
 */
@RestController
@RequestMapping(API_V1 + TRANSFER_API)
@Tag(name = TRANSFER_CONTROLLER_TITLE, description = TRANSFER_CONTROLLER_DESCRIPTION)
@RequiredArgsConstructor
public class CustomerTransferController {
  private final TransferServiceRouter transferServiceRouter;
  private final NameEnquiryServiceRouter nameEnquiryServiceRouter;
  private final TransferHelperService transferHelperService;

  @PostMapping(REQUEST)
  public ResponseEntity<Object> process(@RequestBody TransferDTO transferDTO) {
    if (transferDTO.getTransactionDate() != null
        && LocalDateTime.now().isAfter(transferDTO.getTransactionDate())) {
      return ResponseEntity.badRequest().body("invalid date");
    }
    return ControllerResponse.buildSuccessResponse(transferServiceRouter.process(transferDTO));
  }

  @GetMapping(SEND_CODE_API)
  public ResponseEntity<Object> sendAuthenticationCode() {
    transferHelperService.sendCode();
    return ControllerResponse.buildSuccessResponse();
  }

  @GetMapping(RETRIEVE_INSTITUTIONS_API)
  public ResponseEntity<Object> retrieveInstitutions() {
    return ControllerResponse.buildSuccessResponse(transferHelperService.fetchInstitutions());
  }

  @PostMapping(NAME_ENQUIRY_API)
  public ResponseEntity<Object> retrieveInstitutions(@RequestBody NameEnquiryDTO nameEnquiryDTO) {
    return ControllerResponse.buildSuccessResponse(
        nameEnquiryServiceRouter.process(nameEnquiryDTO));
  }
}
