/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.service.bulk.validators.authentication;

import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.otp.enums.OtpType;
import com.digicore.lucid.common.lib.otp.service.OtpService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferBulkDTO;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.validator.service.ValidationHandler;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Mar-04(Tue)-2025
 */

@Service("bulkTransferAuthenticationValidator")
@Slf4j
@RequiredArgsConstructor
public class BulkTransferAuthenticationValidatorHandler implements ValidationHandler<Object> {
  private ValidationHandler<Object> next;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final OtpService otpService;

  @Override
  public void setNext(ValidationHandler<Object> next) {
    this.next = next;
  }

  @Override
  public void validate(Object request, List<String> limit) {
    log.info("<<<< validating authentication >>>>");
    TransferBulkDTO transferDTO =
        ClientUtil.getObjectMapper().convertValue(request, TransferBulkDTO.class);
    otpService.effect(
        RequestContextHolder.get().getOrganizationId().concat(ClientUtil.getLoggedInUsername()),
        OtpType.TRANSACTION_AUTHORIZATION,
        transferDTO.getAuthenticationKey());
    log.info("<<<< done validating authentication >>>>");
    if (next != null) next.validate(request, limit);
  }
}
