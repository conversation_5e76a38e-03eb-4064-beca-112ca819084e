/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.service.single.validators.account;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.SAME_ACCOUNT;

import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferDTO;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.validator.service.ValidationHandler;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Mar-04(Tue)-2025
 */

@Service("singleTransferAccountValidator")
@Slf4j
@RequiredArgsConstructor
public class TransferAccountValidator implements ValidationHandler<Object> {
  private ValidationHandler<Object> next;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  //  private final AccountService<CustomerAccountDTO> customerAccountService;

  @Override
  public void setNext(ValidationHandler<Object> next) {
    this.next = next;
  }

  @Override
  public void validate(Object request, List<String> limit) {
    log.info("<<<< validating account >>>>");
    TransferDTO transferDTO = ClientUtil.getObjectMapper().convertValue(request, TransferDTO.class);

    if (transferDTO
        .getSenderAccountNumber()
        .equalsIgnoreCase(transferDTO.getBeneficiaryAccountNumber()))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getTransferMessage(SAME_ACCOUNT), HttpStatus.BAD_REQUEST);

    //    if (!customerAccountService.verifyAccountNumber(
    //        transferDTO.getSenderAccountNumber(), RequestContextHolder.get().getOrganizationId()))
    //      exceptionHandler.processCustomExceptions(
    //          messagePropertyConfig.getTransferMessage(NO_ACCESS), HttpStatus.BAD_REQUEST);

    log.info("<<<< done validating account >>>>");
    if (next != null) next.validate(request, limit);
  }
}
