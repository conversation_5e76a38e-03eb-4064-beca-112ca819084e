/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.history.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.FETCH_API;
import static com.digicore.lucid.common.lib.swagger.constant.dashboard.DashboardSwaggerDocConstant.DASHBOARD_API;
import static com.digicore.lucid.common.lib.swagger.constant.dashboard.DashboardSwaggerDocConstant.FREQUENT_BENEFICIARIES_API;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.customer.transfer.service.modules.history.service.CustomerTransferDashboardOperations;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/*
 * <AUTHOR> Ilori
 * @createdOn 07/05/2025
 */

@RestController
@RequestMapping(API_V1 + DASHBOARD_API)
@RequiredArgsConstructor
public class CustomerTransferDashboardController {
  private final CustomerTransferDashboardOperations transferDashboardOperations;

  @GetMapping(FREQUENT_BENEFICIARIES_API)
  ResponseEntity<Object> findFrequentBeneficiaries(
      @RequestParam int size, @RequestParam String organizationId) {
    return ControllerResponse.buildSuccessResponse(
        transferDashboardOperations.findFrequentBeneficiaries(size, organizationId));
  }

  @GetMapping(FETCH_API)
  ResponseEntity<Object> fetchCustomerTransfers(
      @RequestParam String organizationId,
      @RequestParam int pageNumber,
      @RequestParam int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        transferDashboardOperations.fetchAllCustomerTransferHistory(
            organizationId, pageNumber, pageSize));
  }
}
