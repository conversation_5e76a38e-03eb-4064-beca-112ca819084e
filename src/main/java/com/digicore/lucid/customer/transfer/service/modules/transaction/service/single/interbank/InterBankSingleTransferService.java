/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.service.single.interbank;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.SINGLE;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.TRANSFER;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.TRANFER_DTO;
import static com.digicore.lucid.common.lib.transaction.enums.TransferType.INTER_BANK;
import static com.digicore.lucid.customer.transfer.service.modules.transaction.service.single.SingleTransferHelperService.*;

import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferDTO;
import com.digicore.lucid.common.lib.transaction.enums.TransferCategory;
import com.digicore.lucid.common.lib.transaction.service.TransactionService;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.customer.transfer.data.modules.transfer.service.DataAccessService;
import com.digicore.lucid.customer.transfer.service.modules.transaction.dto.TransferResponseDTO;
import com.digicore.lucid.customer.transfer.service.modules.transaction.service.single.SingleTransferHelperService;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.transfer.request.InterBankTransferRequest;
import com.digicore.lucid.integration.lib.modules.service.transfer.request.TransferServiceType;
import com.digicore.lucid.integration.lib.modules.service.transfer.response.TransferResponse;
import com.digicore.registhentication.registration.enums.Status;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-03(Mon)-2025
 */

@Service
@RequiredArgsConstructor
public class InterBankSingleTransferService
    implements TransactionService<TransferResponseDTO, TransferDTO> {
  private final DataAccessService<TransferDTO> customerSingleTransferDataAccessService;
  private final DataAccessService<TransferDTO> postDatedTransferDataAccessService;
  private final SingleTransferHelperService singleTransferHelperService;

  @Override
  @MakerChecker(
      checkerPermission = "approve-single-transfer",
      makerPermission = "single-transfer",
      requestClassName = TRANFER_DTO,
      activity = SINGLE,
      module = TRANSFER)
  public Object singleTransfer(Object initialData, Object updateData, Object... files) {
    TransferDTO request = (TransferDTO) updateData;

    TransferResponseDTO transferResponseDTO;
    TransferDTO savedRequest = null;
    String failedMessage = "";
    String failedCode = "";
    boolean successful = false;
    try {
      if (Status.FUTURE.equals(request.getTransactionStatus())) {
        prepareScheduledTransferRequest(request, false);
        savedRequest = postDatedTransferDataAccessService.create(request);
        return Optional.empty();
      }

      prepareTransferRequest(request, false);
      savedRequest = customerSingleTransferDataAccessService.create(request);

      InterBankTransferRequest cbaRequest = getCbaRequest(request);
      TransferResponse cbaResponse =
          singleTransferHelperService.getCbaResponse(
              TransferServiceType.INTER_BANK_TRANSFER, cbaRequest);

      transferResponseDTO = new TransferResponseDTO();
      BeanUtilWrapper.copyNonNullProperties(cbaResponse, transferResponseDTO);

      successful = cbaResponse.getResponseStatus().equals(CbaProvider.ResponseStatus.COMPLETED);
      savedRequest.setTransactionStatus(successful ? Status.SUCCESS : Status.FAILED);
      transferResponseDTO.setStatus(successful ? Status.SUCCESS : Status.FAILED);

      evaluateTransactionStatus(successful, transferResponseDTO, cbaResponse);

      if (successful) {
        transferResponseDTO.setTransactionId(savedRequest.getTransactionId());
      }
      setCbaResponse(savedRequest, cbaResponse);

      failedMessage = successful ? "" : cbaResponse.getNarration();
      failedCode = cbaResponse.getResponseCode();
    } catch (Exception e) {
      failedMessage = e.getMessage();
      transferResponseDTO = new TransferResponseDTO();
      transferResponseDTO.setResponseCode(failedCode);
      transferResponseDTO.setStatus(Status.FAILED);
      transferResponseDTO.setResponseMessage(failedMessage);
    } finally {
      if (!Status.FUTURE.equals(request.getTransactionStatus())) {
        singleTransferHelperService.cleanUp(
            request,
            savedRequest,
            successful,
            failedMessage,
            failedCode,
            LimitType.INTERBANK_TRANSFER_LIMIT);
      }
    }
    return transferResponseDTO;
  }

  private static InterBankTransferRequest getCbaRequest(TransferDTO request) {
    return InterBankTransferRequest.builder()
        .amountInMinor(request.getAmountInMinor())
        .beneficiaryAccountNumber(request.getBeneficiaryAccountNumber())
        .beneficiaryAccountName(request.getBeneficiaryAccountName())
        .beneficiaryBankCode(request.getBeneficiaryBankCode())
        .senderAccountNumber(request.getSenderAccountNumber())
        .senderAccountName(request.getSenderAccountName())
        .narration(request.getNarration())
        .sessionId(request.getSessionId())
        .transactionRef(request.getTransactionReference())
        .build();
  }

  @Override
  public String getServiceKey() {
    return INTER_BANK.toString().concat(TransferCategory.SINGLE.toString());
  }
}
