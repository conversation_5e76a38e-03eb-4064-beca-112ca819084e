/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.service.single.fx.sale;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.FX_SALE;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.TRANSFER;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.*;
import static com.digicore.lucid.customer.transfer.service.modules.transaction.service.single.SingleTransferHelperService.*;

import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.lucid.common.lib.transaction.dto.transfer.FXTransferDTO;
import com.digicore.lucid.common.lib.transaction.enums.TransferCategory;
import com.digicore.lucid.common.lib.transaction.enums.TransferType;
import com.digicore.lucid.common.lib.transaction.service.TransactionService;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.customer.transfer.data.modules.transfer.service.DataAccessService;
import com.digicore.lucid.customer.transfer.service.modules.transaction.dto.TransferResponseDTO;
import com.digicore.lucid.customer.transfer.service.modules.transaction.service.single.SingleTransferHelperService;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.transfer.request.FxSaleRequest;
import com.digicore.lucid.integration.lib.modules.service.transfer.request.TransferServiceType;
import com.digicore.lucid.integration.lib.modules.service.transfer.response.TransferResponse;
import com.digicore.registhentication.registration.enums.Status;
import com.digicore.registhentication.util.IDGeneratorUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-03(Mon)-2025
 */

@Service
@RequiredArgsConstructor
public class FxSaleService implements TransactionService<TransferResponseDTO, FXTransferDTO> {
  private final DataAccessService<FXTransferDTO> customerFxTransferDataAccessService;
  private final SingleTransferHelperService singleTransferHelperService;

  @Override
  @MakerChecker(
      checkerPermission = "approve-fx-sale",
      makerPermission = "fx-sale",
      requestClassName = FX_TRANSFER_DTO,
      activity = FX_SALE,
      module = TRANSFER)
  public Object fxSale(Object initialData, Object updateData, Object... files) {
    FXTransferDTO request = (FXTransferDTO) updateData;

    TransferResponseDTO transferResponseDTO;
    FXTransferDTO savedRequest = null;
    String failedMessage = "";
    String failedCode = "";
    boolean successful = false;
    try {

      request.setTransactionStatus(Status.PENDING);
      request.setTransactionReference(IDGeneratorUtil.generateTransactionRef("F", 12));
      request.setChannel(RequestContextHolder.get().getChannel());
      savedRequest = customerFxTransferDataAccessService.create(request);

      FxSaleRequest cbaRequest = getCbaRequest(request);
      TransferResponse cbaResponse =
          singleTransferHelperService.getCbaResponse(TransferServiceType.FX_SALE, cbaRequest);

      transferResponseDTO = new TransferResponseDTO();
      BeanUtilWrapper.copyNonNullProperties(cbaResponse, transferResponseDTO);

      successful = cbaResponse.getResponseStatus().equals(CbaProvider.ResponseStatus.COMPLETED);
      savedRequest.setTransactionStatus(successful ? Status.SUCCESS : Status.FAILED);
      transferResponseDTO.setStatus(successful ? Status.SUCCESS : Status.FAILED);

      evaluateTransactionStatus(successful, transferResponseDTO, cbaResponse);

      if (successful) {
        transferResponseDTO.setTransactionId(savedRequest.getTransactionId());
      }

      failedMessage = successful ? "" : cbaResponse.getNarration();
      failedCode = cbaResponse.getResponseCode();
    } catch (Exception e) {
      failedMessage = e.getMessage();
      transferResponseDTO = new TransferResponseDTO();
      transferResponseDTO.setResponseCode(failedCode);
      transferResponseDTO.setStatus(Status.FAILED);
      transferResponseDTO.setResponseMessage(failedMessage);
    } finally {
      singleTransferHelperService.cleanUp(
          request,
          savedRequest,
          successful,
          failedMessage,
          failedCode,
          LimitType.INTRABANK_TRANSFER_LIMIT);
    }
    return transferResponseDTO;
  }

  private static FxSaleRequest getCbaRequest(FXTransferDTO request) {
    return FxSaleRequest.builder()
        .fromAccount(request.getSenderAccountNumber())
        .fromCurrency(request.getSenderAccountCurrency().toString())
        .toAccount(request.getBeneficiaryAccountNumber())
        .toCurrency(request.getBeneficiaryAccountCurrency().toString())
        .amountToConvert(request.getAmountInMinor())
        .build();
  }

  @Override
  public String getServiceKey() {
    return TransferType.FX_SALE.toString().concat(TransferCategory.FX.toString());
  }
}
