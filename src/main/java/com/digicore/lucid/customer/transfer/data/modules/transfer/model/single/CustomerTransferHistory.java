/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.data.modules.transfer.model.single;

import com.digicore.lucid.common.lib.transaction.converter.TransferSpendCategoryConverter;
import com.digicore.lucid.common.lib.transaction.enums.TransferCategory;
import com.digicore.lucid.common.lib.transaction.enums.TransferSpendCategory;
import com.digicore.lucid.common.lib.transaction.model.Transaction;
import com.digicore.lucid.customer.transfer.data.modules.profile.model.CustomerProfile;
import com.digicore.lucid.customer.transfer.data.modules.transfer.converter.TransferCategoryConverter;
import com.digicore.lucid.customer.transfer.data.modules.transfer.model.CustomerSenderDetail;
import com.digicore.registhentication.common.enums.Channel;
import com.digicore.registhentication.converter.ChannelConverter;
import com.digicore.registhentication.converter.CurrencyConverter;
import com.digicore.registhentication.validator.enums.Currency;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-03(Mon)-2025
 */
@Entity
@Table(name = "customer_transfer_history")
@Getter
@Setter
public class CustomerTransferHistory extends Transaction implements Serializable {
  @NotNull @Convert(converter = TransferCategoryConverter.class)
  private TransferCategory transferCategory;

  @NotNull @Convert(converter = ChannelConverter.class)
  private Channel channel;

  @NotNull @Convert(converter = CurrencyConverter.class)
  private Currency currency;

  @Convert(converter = TransferSpendCategoryConverter.class)
  private TransferSpendCategory spendCategory = TransferSpendCategory.OTHERS;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "customer_sender_detail_id")
  private CustomerSenderDetail customerSenderDetail;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "customer_profile_id")
  private CustomerProfile customerProfile;
}
