/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.service.bulk.validators.limit;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.LIMIT_VIOLATION;
import static com.digicore.lucid.common.lib.limit.util.LimitUtil.validateLimits;

import com.digicore.api.helper.response.ApiError;
import com.digicore.lucid.common.lib.client.BackOfficeFeignClient;
import com.digicore.lucid.common.lib.client.CustomerFeignClient;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.limit.service.CumulativeAmountTrackerService;
import com.digicore.lucid.common.lib.limit.util.LimitFetcher;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferBulkDTO;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.validator.service.ValidationHandler;
import com.digicore.registhentication.common.enums.Channel;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.validator.enums.Currency;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Apr-23(Wed)-2025
 */

@Service("bulkTransferLimitValidator")
@Slf4j
@RequiredArgsConstructor
public class BulkTransferLimitValidator implements ValidationHandler<Object> {
  private ValidationHandler<Object> next;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final CustomerFeignClient customerFeignClient;
  private final BackOfficeFeignClient backOfficeFeignClient;
  private final CumulativeAmountTrackerService cumulativeAmountTrackerService;

  @Override
  public void setNext(ValidationHandler<Object> next) {
    this.next = next;
  }

  @Override
  public void validate(Object request, List<String> limit) {

    TransferBulkDTO transferDTO =
        ClientUtil.getObjectMapper().convertValue(request, TransferBulkDTO.class);

    if (!ClientUtil.nullOrEmpty(transferDTO.getEntries())) {
      log.info("<<<< validating bulk transfer limit >>>>");
      Channel channel = RequestContextHolder.get().getChannel();

      // Sum all amounts in minor units
      BigDecimal totalAmountInMinor =
          transferDTO.getEntries().stream()
              .map(t -> new BigDecimal(t.getAmountInMinor()))
              .reduce(BigDecimal.ZERO, BigDecimal::add);

      LimitFetcher limitFetcher =
          new LimitFetcher()
              .withTransactionType(LimitType.BULK_TRANSFER_LIMIT)
              .withCurrency(Currency.NGN)
              .withSenderAccount(transferDTO.getSenderAccountNumber())
              .withAmount(totalAmountInMinor.toString())
              .fetchCustomerLimit(customerFeignClient, cumulativeAmountTrackerService)
              .fetchProfileLimit(customerFeignClient)
              .fetchDefaultAndGlobalLimits(backOfficeFeignClient, cumulativeAmountTrackerService);

      List<ApiError> apiErrorList = new ArrayList<>();

      String limitUsed = limitFetcher.getLimitUsed();
      validateLimits(
          totalAmountInMinor.toString(),
          limitFetcher.getCumulativeSpent(),
          limitFetcher.getCustomerLimit(),
          limitFetcher.getProfileLimit(),
          limitFetcher.getDefaultLimit(),
          limitFetcher.getGlobalLimit(),
          channel,
          apiErrorList);

      if (!ClientUtil.nullOrEmpty(apiErrorList)) {
        exceptionHandler.processCustomExceptions(
            messagePropertyConfig.getLimitMessage(LIMIT_VIOLATION),
            HttpStatus.BAD_REQUEST,
            apiErrorList);
      }

      limit.add(limitUsed);
      log.info("<<<< done validating bulk transfer limit >>>>");
    }

    if (next != null) next.validate(transferDTO, limit);
  }
}
