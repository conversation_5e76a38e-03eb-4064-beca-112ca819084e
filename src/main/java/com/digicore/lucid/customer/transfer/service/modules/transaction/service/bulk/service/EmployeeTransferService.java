/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.service.bulk.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.EMPLOYEE_PAYMENT;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.TRANSFER;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.TRANSFER_BULK_DTO;
import static com.digicore.registhentication.registration.enums.Status.PENDING;

import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferBulkDTO;
import com.digicore.lucid.common.lib.transaction.enums.TransferCategory;
import com.digicore.lucid.common.lib.transaction.service.TransactionService;
import com.digicore.lucid.customer.transfer.data.modules.transfer.service.DataAccessService;
import com.digicore.lucid.customer.transfer.service.modules.transaction.dto.TransferResponseDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Apr-23(Wed)-2025
 */

@Service
@RequiredArgsConstructor
public class EmployeeTransferService
    implements TransactionService<TransferResponseDTO, TransferBulkDTO> {
  private final DataAccessService<TransferBulkDTO> customerBulkTransferDataAccessService;

  @Override
  @MakerChecker(
      checkerPermission = "approve-bulk-transfer",
      makerPermission = "bulk-transfer",
      requestClassName = TRANSFER_BULK_DTO,
      activity = EMPLOYEE_PAYMENT,
      module = TRANSFER)
  public Object employeePayment(Object initialData, Object updateData, Object... files) {
    TransferBulkDTO transferBulkDTO = (TransferBulkDTO) updateData;
    customerBulkTransferDataAccessService.create(transferBulkDTO);
    TransferResponseDTO transferResponseDTO = new TransferResponseDTO();
    transferResponseDTO.setResponseCode("P00");
    transferResponseDTO.setStatus(PENDING);
    transferResponseDTO.setResponseMessage("Pending");
    return transferResponseDTO;
  }

  @Override
  public String getServiceKey() {
    return TransferCategory.EMPLOYEE.toString();
  }
}
