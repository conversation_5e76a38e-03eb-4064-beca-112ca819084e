/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.history.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V2;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.transfer.TransferHistorySwaggerDocConstant.*;
import static com.digicore.registhentication.util.PageableUtil.PAGE_NUMBER;
import static com.digicore.registhentication.util.PageableUtil.PAGE_SIZE;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.transaction.enums.TransferCategory;
import com.digicore.lucid.customer.transfer.service.modules.history.service.CustomerTransferHistoryOperations;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Apr-29(Tue)-2025
 */

@RestController
@RequestMapping(API_V2 + TRANSFER_HISTORY_API)
@Tag(
    name = TRANSFER_HISTORY_CONTROLLER_TITLE,
    description = TRANSFER_HISTORY_CONTROLLER_DESCRIPTION)
@RequiredArgsConstructor
public class CustomerTransferHistoryControllerV2 {
  private final CustomerTransferHistoryOperations customerTransferHistoryService;

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-transactions')")
  ResponseEntity<Object> fetchTransfers(
      @RequestParam String accountNumber,
      @RequestParam TransferCategory transferCategory,
      @RequestParam(value = PAGE_NUMBER) int pageNumber,
      @RequestParam(value = PAGE_SIZE) int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        customerTransferHistoryService.fetchBulkTransferHistory(
            accountNumber, transferCategory, pageNumber, pageSize));
  }

  @GetMapping(FETCH_ALL_API)
  @PreAuthorize("hasAuthority('view-transactions')")
  ResponseEntity<Object> fetchTransfersEntires(
      @RequestParam String bulkTransactionReference,
      @RequestParam(value = PAGE_NUMBER) int pageNumber,
      @RequestParam(value = PAGE_SIZE) int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        customerTransferHistoryService.fetchBulkTransferEntriesHistory(
            bulkTransactionReference, pageNumber, pageSize));
  }

  @GetMapping(RETRIEVE_API)
  @PreAuthorize("hasAuthority('view-transactions')")
  ResponseEntity<Object> fetchTransfersEntiresByIdentifier(
      @RequestParam String identifier,
      @RequestParam(value = PAGE_NUMBER) int pageNumber,
      @RequestParam(value = PAGE_SIZE) int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        customerTransferHistoryService.fetchBulkTransferEntriesHistoryByIdentifier(
            identifier, pageNumber, pageSize));
  }
}
