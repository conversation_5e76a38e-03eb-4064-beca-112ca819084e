/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.router;

import com.digicore.lucid.common.lib.transaction.dto.transfer.NameEnquiryDTO;
import com.digicore.lucid.common.lib.transaction.dto.transfer.UploadNameEnquiryDTO;
import com.digicore.lucid.customer.transfer.service.modules.transaction.locator.NameEnquiryServiceLocator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/*
 * <AUTHOR>
 * @createdOn Feb-26(Wed)-2025
 */

@Component
@Slf4j
public class NameEnquiryServiceRouter {
  private final NameEnquiryServiceLocator serviceLocator;

  @Autowired
  public NameEnquiryServiceRouter(NameEnquiryServiceLocator serviceLocator) {
    this.serviceLocator = serviceLocator;
  }

  public Object process(NameEnquiryDTO request) {
    log.info("<<< single name enquiry request : {} >>>", request);
    log.info("<<< type : {} >>>", request.getTransferType());
    log.info("<<< category : {} >>>", request.getTransferCategory());
    return serviceLocator
        .getService(request.getTransferType().toString(), request.getTransferCategory().toString())
        .process(request);
  }

  public Object process(List<UploadNameEnquiryDTO> request) {
    log.info("<<< bulk name enquiry request size : {} >>>", request.size());
    return serviceLocator.getService().process(request);
  }

  public Object process(MultipartFile multipartFile) {
    log.info("<<< bulk upload name enquiry request size : {} >>>", multipartFile.getSize());
    return serviceLocator.getUploadService().process(multipartFile);
  }
}
