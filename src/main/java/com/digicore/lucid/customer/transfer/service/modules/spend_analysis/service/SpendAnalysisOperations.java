/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.spend_analysis.service;

import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.spend_analysis.dto.CategoryYearSpendAnalysisDTO;
import com.digicore.lucid.common.lib.spend_analysis.dto.DateRangeSpendAnalysisDTO;
import com.digicore.lucid.common.lib.spend_analysis.dto.YearSpendAnalysisDTO;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferDTO;
import com.digicore.lucid.common.lib.transaction.enums.TransferSpendCategory;
import com.digicore.lucid.customer.transfer.data.modules.transfer.service.SpendAnalysisDataAccessService;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Year;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ilori
 * @createdOn 24/04/2025
 */

@Service
@RequiredArgsConstructor
public class SpendAnalysisOperations {
  private final SpendAnalysisDataAccessService spendAnalysisDataAccessService;

  public BigInteger fetchSpendingTotal() {
    return spendAnalysisDataAccessService.spendingTotal(
        RequestContextHolder.get().getOrganizationId());
  }

  public List<TransferSpendCategory> fetchSpendingCategories() {
    return spendAnalysisDataAccessService.getCategories();
  }

  public PaginatedResponseDTO<TransferDTO> fetchSpendingHistory(
      LocalDate startDate,
      LocalDate endDate,
      TransferSpendCategory transferSpendCategory,
      int pageNo,
      int pageSize) {
    LocalDateTime startDateTime = startDate.atStartOfDay();
    LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);
    return spendAnalysisDataAccessService.spendingHistory(
        startDateTime,
        endDateTime,
        RequestContextHolder.get().getOrganizationId(),
        List.of(transferSpendCategory),
        pageNo,
        pageSize);
  }

  public DateRangeSpendAnalysisDTO dateRangeSpendAnalysis(
      LocalDate startDate, LocalDate endDate, String organizationId) {
    return spendAnalysisDataAccessService.dateRangeSpentSum(startDate, endDate, organizationId);
  }

  public YearSpendAnalysisDTO yearSpendAnalysis(Year year, String organizationId) {
    return spendAnalysisDataAccessService.yearSpentSumAnalysis(year, organizationId);
  }

  public CategoryYearSpendAnalysisDTO categoryYearSpendAnalysis(Year year, String organizationId) {
    return spendAnalysisDataAccessService.categoryYearSpentSumAnalysis(year, organizationId);
  }
}
