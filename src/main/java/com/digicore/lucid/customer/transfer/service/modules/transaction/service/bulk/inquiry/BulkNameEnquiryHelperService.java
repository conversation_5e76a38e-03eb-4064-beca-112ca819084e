/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.service.bulk.inquiry;

import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;

import com.digicore.api.helper.response.ApiResponseJson;
import com.digicore.lucid.common.lib.client.CbaFeignClient;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.transaction.dto.transfer.UploadNameEnquiryDTO;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.customer.transfer.service.modules.transaction.dto.NameEnquiryResponseDTO;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.transfer.request.NameEnquiryRequest;
import com.digicore.lucid.integration.lib.modules.service.transfer.request.TransferServiceType;
import com.digicore.lucid.integration.lib.modules.service.transfer.response.NameEnquiryResponse;
import com.digicore.registhentication.registration.enums.Status;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn May-07(Wed)-2025
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class BulkNameEnquiryHelperService {
  private final CbaFeignClient cbaFeignClient;

  public List<NameEnquiryResponseDTO> process(List<UploadNameEnquiryDTO> requests) {
    RequestContextHolder.RequestContext requestContext = RequestContextHolder.get();
    try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
      return requests.stream()
          .map(req -> executor.submit(() -> processRequest(req, requestContext)))
          .map(this::safeGetFuture)
          .toList();
    }
  }

  private NameEnquiryResponseDTO safeGetFuture(Future<NameEnquiryResponseDTO> future) {
    try {
      return future.get();
    } catch (Exception e) {
      return createFailedResponse();
    }
  }

  private NameEnquiryResponseDTO processRequest(
      UploadNameEnquiryDTO request, RequestContextHolder.RequestContext requestContext) {
    RequestContextHolder.set(requestContext);
    String serviceType =
        request.getBankCode().equalsIgnoreCase(RequestContextHolder.get().getBankCode())
            ? TransferServiceType.INTRA_NAME_ENQUIRY
            : TransferServiceType.INTER_NAME_ENQUIRY;
    NameEnquiryRequest cbaRequest =
        serviceType.equalsIgnoreCase(TransferServiceType.INTRA_NAME_ENQUIRY)
            ? getCbaRequest(request.getAccountNumber())
            : getCbaRequest(request.getAccountNumber(), request.getBankCode());

    NameEnquiryResponse cbaResponse = getCbaResponse(cbaRequest, serviceType);
    NameEnquiryResponseDTO responseDTO = new NameEnquiryResponseDTO();

    if (CbaProvider.ResponseStatus.COMPLETED.equals(cbaResponse.getResponseStatus())) {
      BeanUtilWrapper.copyNonNullProperties(cbaResponse, responseDTO);
      responseDTO.setValidatedAccountName(cbaResponse.getAccountName());
      responseDTO.setStatus(Status.SUCCESS);
      responseDTO.setBankCode(request.getBankCode());
      responseDTO.setAmount(request.getAmount());
      responseDTO.setNarration(request.getNarration());
      responseDTO.setIdentifier(request.getIdentifier());
    } else {
      responseDTO.setAccountNumber(request.getAccountNumber());
      responseDTO.setBankCode(request.getBankCode());
      responseDTO.setAmount(request.getAmount());
      responseDTO.setValidatedAccountName("");
      responseDTO.setStatus(Status.FAILED);
    }

    return responseDTO;
  }

  private NameEnquiryResponseDTO createFailedResponse() {
    NameEnquiryResponseDTO response = new NameEnquiryResponseDTO();
    response.setAccountNumber(null);
    response.setStatus(Status.FAILED);
    return response;
  }

  private static NameEnquiryRequest getCbaRequest(String accountNumber, String bankCode) {
    return NameEnquiryRequest.builder().accountNumber(accountNumber).bankCode(bankCode).build();
  }

  private static NameEnquiryRequest getCbaRequest(String accountNumber) {
    return NameEnquiryRequest.builder().accountNumber(accountNumber).build();
  }

  private NameEnquiryResponse getCbaResponse(
      NameEnquiryRequest cbaRequest, String transferServiceType) {
    ApiResponseJson<Object> response =
        cbaFeignClient.processTransferRequest(transferServiceType, cbaRequest);
    Object responseBody = response.getData();
    return getObjectMapper().convertValue(responseBody, NameEnquiryResponse.class);
  }
}
