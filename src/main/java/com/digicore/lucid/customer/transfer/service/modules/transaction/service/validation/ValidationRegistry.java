/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.service.validation;

import com.digicore.lucid.common.lib.validator.service.ValidationHandler;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/*
 * <AUTHOR>
 * @createdOn Mar-11(Tue)-2025
 */

@Component
public class ValidationRegistry {
  private final Map<String, ValidationHandler> validators = new LinkedHashMap<>();

  public ValidationRegistry(List<ValidationHandler> handlers, ApplicationContext context) {
    handlers.forEach(
        handler -> {
          // Get the actual bean name from the context
          String[] beanNames = context.getBeanNamesForType(handler.getClass());
          String beanName =
              (beanNames.length > 0) ? beanNames[0] : handler.getClass().getSimpleName();

          validators.put(beanName, handler);
        });
  }

  public ValidationHandler getValidator(String name) {
    return validators.get(name);
  }

  public List<String> getAvailableValidators() {
    return new ArrayList<>(validators.keySet());
  }
}
