/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.history.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.transfer.TransferHistorySwaggerDocConstant.*;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.customer.transfer.service.modules.history.service.CustomerTransferHistoryFeignOperations;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR>
 * @createdOn 08/03/2025
 */

@Hidden
@RestController
@RequestMapping(API_V1 + CUSTOMER_API + TRANSFER_HISTORY_API)
@RequiredArgsConstructor
public class CustomerTransferHistoryFeignController {
  private final CustomerTransferHistoryFeignOperations customerTransferHistoryFeignOperations;

  @GetMapping(RETRIEVE_API)
  ResponseEntity<Object> fetchTransfer(
      @RequestHeader String bankOrganizationId, @RequestParam String transactionId) {
    return ControllerResponse.buildSuccessResponse(
        customerTransferHistoryFeignOperations.fetchTransferByTransactionId(
            bankOrganizationId, transactionId));
  }

  @GetMapping(RETRIEVE_ALL_API)
  ResponseEntity<Object> fetchTransfers(
      @RequestHeader String bankOrganizationId,
      @RequestParam String accountNumber,
      @RequestParam int pageNumber,
      @RequestParam int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        customerTransferHistoryFeignOperations.fetchTransferHistory(
            bankOrganizationId, accountNumber, pageNumber, pageSize));
  }

  @GetMapping(FETCH_ALL_API)
  ResponseEntity<Object> fetchTransfers(
      @RequestHeader String bankOrganizationId,
      @RequestParam int pageNumber,
      @RequestParam int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        customerTransferHistoryFeignOperations.fetchTransferHistory(
            bankOrganizationId, pageNumber, pageSize));
  }
}
