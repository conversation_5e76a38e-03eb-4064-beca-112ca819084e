/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.service.single.intrabank.inquiry;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NAME_ENQUIRY_FAILED;
import static com.digicore.lucid.common.lib.transaction.enums.TransferType.INTRA_BANK;
import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;

import com.digicore.api.helper.response.ApiResponseJson;
import com.digicore.lucid.common.lib.client.CbaFeignClient;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.transaction.dto.transfer.NameEnquiryDTO;
import com.digicore.lucid.common.lib.transaction.enums.TransferCategory;
import com.digicore.lucid.common.lib.transaction.service.TransactionService;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.customer.transfer.service.modules.transaction.dto.NameEnquiryResponseDTO;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.transfer.request.NameEnquiryRequest;
import com.digicore.lucid.integration.lib.modules.service.transfer.request.TransferServiceType;
import com.digicore.lucid.integration.lib.modules.service.transfer.response.NameEnquiryResponse;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.enums.Status;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-04(Tue)-2025
 */
@Service
@RequiredArgsConstructor
public class IntraBankNameEnquiryService
    implements TransactionService<NameEnquiryResponseDTO, NameEnquiryDTO> {
  private final CbaFeignClient cbaFeignClient;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  @Override
  public NameEnquiryResponseDTO process(NameEnquiryDTO request) {
    NameEnquiryRequest cbaRequest = getCbaRequest(request.getAccountNumber());
    NameEnquiryResponse cbaResponse = getCbaResponse(cbaRequest);
    if (CbaProvider.ResponseStatus.COMPLETED.equals(cbaResponse.getResponseStatus())) {
      NameEnquiryResponseDTO nameEnquiryResponseDTO = new NameEnquiryResponseDTO();
      BeanUtilWrapper.copyNonNullProperties(cbaResponse, nameEnquiryResponseDTO);
      nameEnquiryResponseDTO.setStatus(Status.SUCCESS);
      return nameEnquiryResponseDTO;
    }
    throw exceptionHandler.processCustomException(
        messagePropertyConfig.getTransferMessage(NAME_ENQUIRY_FAILED), HttpStatus.BAD_REQUEST);
  }

  private static NameEnquiryRequest getCbaRequest(String accountNumber) {
    return NameEnquiryRequest.builder().accountNumber(accountNumber).build();
  }

  private NameEnquiryResponse getCbaResponse(NameEnquiryRequest cbaRequest) {
    ApiResponseJson<Object> response =
        cbaFeignClient.processTransferRequest(TransferServiceType.INTRA_NAME_ENQUIRY, cbaRequest);
    Object responseBody = response.getData();
    return getObjectMapper().convertValue(responseBody, NameEnquiryResponse.class);
  }

  @Override
  public String getServiceKey() {
    return INTRA_BANK.toString().concat(TransferCategory.SINGLE.toString()).concat("NAME_LOOK_UP");
  }
}
