/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.data.modules.transfer.service;

import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.limit.service.CumulativeAmountTrackerService;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.util.MoneyUtil;
import com.digicore.lucid.customer.transfer.data.modules.transfer.model.CustomerGeneralCumulativeAmountTracker;
import com.digicore.lucid.customer.transfer.data.modules.transfer.model.CustomerPersonalCumulativeAmountTracker;
import com.digicore.lucid.customer.transfer.data.modules.transfer.repository.CustomerGeneralCumulativeAmountTrackerRepository;
import com.digicore.lucid.customer.transfer.data.modules.transfer.repository.CustomerPersonalCumulativeAmountTrackerRepository;
import com.digicore.registhentication.validator.enums.Currency;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-11(Tue)-2025
 */
@Service
@RequiredArgsConstructor
@Transactional
public class CumulativeSpentService implements CumulativeAmountTrackerService {
  private final CustomerPersonalCumulativeAmountTrackerRepository customerCumulativeSpentRepository;
  private final CustomerGeneralCumulativeAmountTrackerRepository generalCumulativeSpentRepository;

  @Override
  public void updateCumulativeAmount(
      String organizationId,
      String accountNumber,
      String amount,
      LimitType limitType,
      Currency currency) {
    LocalDate localDate = LocalDate.now();
    CustomerPersonalCumulativeAmountTracker spent =
        customerCumulativeSpentRepository
            .findFirstByOrganizationIdAndAccountNumberAndLimitTypeAndCurrencyAndTransactionDate(
                organizationId, accountNumber, limitType, currency, localDate)
            .orElse(new CustomerPersonalCumulativeAmountTracker());

    spent.setTotalSpentMinor(
        MoneyUtil.convertToMinor(
            MoneyUtil.getAmountFromAmountInMinor(spent.getTotalSpentMinor())
                .add(MoneyUtil.getAmountFromAmountInMinor(amount))
                .toString()));
    spent.setCurrency(currency);
    spent.setLimitType(limitType);
    spent.setOrganizationId(organizationId);
    spent.setTransactionDate(localDate);
    spent.setAccountNumber(accountNumber);
    customerCumulativeSpentRepository.save(spent);
  }

  @Override
  public void updateCumulativeAmount(
      String organizationId, String amount, LimitType limitType, Currency currency) {
    LocalDate localDate = LocalDate.now();
    CustomerGeneralCumulativeAmountTracker spent =
        generalCumulativeSpentRepository
            .findFirstByOrganizationIdAndLimitTypeAndCurrencyAndTransactionDate(
                organizationId, limitType, currency, localDate)
            .orElse(new CustomerGeneralCumulativeAmountTracker());

    spent.setTotalSpentMinor(
        MoneyUtil.convertToMinor(
            MoneyUtil.getAmountFromAmountInMinor(spent.getTotalSpentMinor())
                .add(MoneyUtil.getAmountFromAmountInMinor(amount))
                .toString()));
    spent.setCurrency(currency);
    spent.setLimitType(limitType);
    spent.setOrganizationId(organizationId);
    spent.setTransactionDate(localDate);
    generalCumulativeSpentRepository.save(spent);
  }

  @Override
  public String retrieveCumulativeAmount(
      String organizationId, LimitType limitType, Currency currency) {
    CustomerGeneralCumulativeAmountTracker spent =
        generalCumulativeSpentRepository
            .findFirstByOrganizationIdAndLimitTypeAndCurrencyAndTransactionDate(
                organizationId, limitType, currency, LocalDate.now())
            .orElse(new CustomerGeneralCumulativeAmountTracker());
    return ClientUtil.nullOrEmpty(spent.getTotalSpentMinor()) ? "0" : spent.getTotalSpentMinor();
  }

  @Override
  public String retrieveCumulativeAmount(
      String organizationId, String accountNumber, LimitType limitType, Currency currency) {
    CustomerPersonalCumulativeAmountTracker spent =
        customerCumulativeSpentRepository
            .findFirstByOrganizationIdAndAccountNumberAndLimitTypeAndCurrencyAndTransactionDate(
                organizationId, accountNumber, limitType, currency, LocalDate.now())
            .orElse(new CustomerPersonalCumulativeAmountTracker());

    return ClientUtil.nullOrEmpty(spent.getTotalSpentMinor()) ? "0" : spent.getTotalSpentMinor();
  }
}
