/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.data.modules.transfer.model.bulk;

import com.digicore.lucid.common.lib.transaction.enums.RecurringFrequency;
import com.digicore.lucid.common.lib.transaction.enums.TransferCategory;
import com.digicore.lucid.customer.transfer.data.modules.profile.model.CustomerProfile;
import com.digicore.lucid.customer.transfer.data.modules.transfer.converter.RecurringFrequencyConverter;
import com.digicore.lucid.customer.transfer.data.modules.transfer.converter.TransferCategoryConverter;
import com.digicore.lucid.customer.transfer.data.modules.transfer.model.CustomerSenderDetail;
import com.digicore.registhentication.common.enums.Channel;
import com.digicore.registhentication.converter.ChannelConverter;
import com.digicore.registhentication.converter.CurrencyConverter;
import com.digicore.registhentication.converter.StatusConverter;
import com.digicore.registhentication.registration.enums.Status;
import com.digicore.registhentication.registration.models.Auditable;
import com.digicore.registhentication.validator.enums.Currency;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-03(Mon)-2025
 */
@Entity
@Table(name = "customer_bulk_transfer_history")
@Getter
@Setter
public class CustomerBulkTransferHistory extends Auditable<String> implements Serializable {
  @NotNull @Convert(converter = StatusConverter.class)
  private Status transactionStatus;

  private String totalAmountInMinor;

  private int transferEntriesCount;

  private LocalDateTime transactionDate;

  private String bulkTransactionReference;

  @NotNull @Convert(converter = ChannelConverter.class)
  private Channel channel;

  @NotNull @Convert(converter = CurrencyConverter.class)
  private Currency currency;

  @NotNull @Convert(converter = TransferCategoryConverter.class)
  private TransferCategory transferCategory;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "customer_sender_detail_id")
  private CustomerSenderDetail customerSenderDetail;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "customer_profile_id")
  private CustomerProfile customerProfile;

  @OneToMany(mappedBy = "customerBulkTransferHistory", cascade = CascadeType.ALL)
  @ToString.Exclude
  private Set<CustomerBulkTransferEntry> customerBulkTransferEntries;

  private boolean recurring = false;

  @Convert(converter = RecurringFrequencyConverter.class)
  private RecurringFrequency recurringFrequency;

  // @NotNull @Convert(converter = LimitTypeConverter.class)
  private String limitType;

  private String initiator;
}
