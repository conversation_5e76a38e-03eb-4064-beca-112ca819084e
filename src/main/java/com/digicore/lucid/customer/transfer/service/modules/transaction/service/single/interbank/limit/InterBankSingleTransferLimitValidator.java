/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.service.single.interbank.limit;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.LIMIT_VIOLATION;
import static com.digicore.lucid.common.lib.limit.util.LimitUtil.validateLimits;
import static com.digicore.lucid.common.lib.transaction.enums.TransferType.INTER_BANK;

import com.digicore.api.helper.response.ApiError;
import com.digicore.lucid.common.lib.client.BackOfficeFeignClient;
import com.digicore.lucid.common.lib.client.CustomerFeignClient;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.limit.service.CumulativeAmountTrackerService;
import com.digicore.lucid.common.lib.limit.util.LimitFetcher;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferDTO;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.validator.service.ValidationHandler;
import com.digicore.registhentication.common.enums.Channel;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.validator.enums.Currency;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-04(Tue)-2025
 */

@Service("singleTransferInterBankLimitValidator")
@Slf4j
@RequiredArgsConstructor
public class InterBankSingleTransferLimitValidator implements ValidationHandler<Object> {
  private ValidationHandler<Object> next;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final CustomerFeignClient customerFeignClient;
  private final BackOfficeFeignClient backOfficeFeignClient;
  private final CumulativeAmountTrackerService cumulativeAmountTrackerService;

  @Override
  public void setNext(ValidationHandler<Object> next) {
    this.next = next;
  }

  @Override
  public void validate(Object request, List<String> limit) {

    TransferDTO transferDTO = ClientUtil.getObjectMapper().convertValue(request, TransferDTO.class);
    if (INTER_BANK.equals(transferDTO.getTransferType())) {
      log.info("<<<< validating limit >>>>");
      Channel channel = RequestContextHolder.get().getChannel();
      LimitFetcher limitFetcher =
          new LimitFetcher()
              .withTransactionType(LimitType.INTERBANK_TRANSFER_LIMIT)
              .withCurrency(Currency.NGN)
              .withSenderAccount(transferDTO.getSenderAccountNumber())
              .withAmount(transferDTO.getAmountInMinor())
              .fetchCustomerLimit(customerFeignClient, cumulativeAmountTrackerService)
              .fetchProfileLimit(customerFeignClient)
              .fetchDefaultAndGlobalLimits(backOfficeFeignClient, cumulativeAmountTrackerService);
      List<ApiError> apiErrorList = new ArrayList<>();

      // do not delete
      //    List<ApiError> errors = LimitGuard.newBuilder()
      //            .transactionAmount(transferDTO.getAmountInMinor())
      //            .cumulativeSpent(limitFetcher.getCumulativeSpent())
      //            .customerLimit(limitFetcher.getCustomerLimit())
      //            .profileLimit(limitFetcher.getProfileLimit())
      //            .defaultLimit(limitFetcher.getDefaultLimit())
      //            .globalLimit(limitFetcher.getGlobalLimit())
      //            .channel(channel)
      //            .validate();

      String limitUsed = limitFetcher.getLimitUsed();
      validateLimits(
          transferDTO.getAmountInMinor(),
          limitFetcher.getCumulativeSpent(),
          limitFetcher.getCustomerLimit(),
          limitFetcher.getProfileLimit(),
          limitFetcher.getDefaultLimit(),
          limitFetcher.getGlobalLimit(),
          channel,
          apiErrorList);

      if (!ClientUtil.nullOrEmpty(apiErrorList))
        exceptionHandler.processCustomExceptions(
            messagePropertyConfig.getLimitMessage(LIMIT_VIOLATION),
            HttpStatus.BAD_REQUEST,
            apiErrorList);

      limit.add(limitUsed);

      log.info("<<<< done validating limit >>>>");
    }
    if (next != null) next.validate(transferDTO, limit);
  }
}
