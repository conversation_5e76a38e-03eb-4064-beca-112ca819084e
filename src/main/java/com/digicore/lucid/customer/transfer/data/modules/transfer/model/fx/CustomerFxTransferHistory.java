/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.data.modules.transfer.model.fx;

import com.digicore.lucid.common.lib.transaction.converter.TransferTypeConverter;
import com.digicore.lucid.common.lib.transaction.enums.TransferType;
import com.digicore.lucid.customer.transfer.data.modules.profile.model.CustomerProfile;
import com.digicore.lucid.customer.transfer.data.modules.transfer.model.CustomerSenderDetail;
import com.digicore.registhentication.converter.CurrencyConverter;
import com.digicore.registhentication.converter.StatusConverter;
import com.digicore.registhentication.registration.enums.Status;
import com.digicore.registhentication.registration.models.Auditable;
import com.digicore.registhentication.validator.enums.Currency;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> Ogunwuyi
 * @createdOn Jun-27(Fri)-2025
 */
@Entity
@Table(name = "customer_fx_transfer_history")
@Getter
@Setter
public class CustomerFxTransferHistory extends Auditable<String> implements Serializable {
  @NotNull @Convert(converter = StatusConverter.class)
  private Status transactionStatus;

  @NotBlank private String amountInMinor;
  @NotBlank private String rate;
  private String vatAmountInMinor;
  private String feeAmountInMinor;

  @NotNull @Convert(converter = TransferTypeConverter.class)
  private TransferType transferType;

  @NotBlank
  @Column(unique = true)
  private String transactionId;

  @NotBlank private String transactionReference;

  private String beneficiaryAccountName;
  @NotBlank private String beneficiaryAccountNumber;
  private String beneficiaryBankCode;
  private String beneficiaryBankName;

  @NotNull @Convert(converter = CurrencyConverter.class)
  private Currency beneficiaryAccountCurrency;

  @NotNull @Convert(converter = CurrencyConverter.class)
  private Currency senderAccountCurrency;

  private String nameEnquiryReference = "N/A";
  private String narration = "N/A";
  private String cbaTransactionReference = "N/A";
  private String cbaTransactionResponseCode = "N/A";
  private String cbaTransactionResponseMessage = "N/A";
  private LocalDateTime transactionDate;
  private String sessionId = "N/A";

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "customer_sender_detail_id")
  private CustomerSenderDetail customerSenderDetail;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "customer_profile_id")
  private CustomerProfile customerProfile;
}
