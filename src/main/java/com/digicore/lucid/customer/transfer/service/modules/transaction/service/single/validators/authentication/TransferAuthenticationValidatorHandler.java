/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.service.modules.transaction.service.single.validators.authentication;

import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.otp.enums.OtpType;
import com.digicore.lucid.common.lib.otp.service.OtpService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferDTO;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.validator.service.ValidationHandler;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Mar-04(Tue)-2025
 */

@Service("singleTransferAuthenticationValidator")
@Slf4j
@RequiredArgsConstructor
public class TransferAuthenticationValidatorHandler implements ValidationHandler<Object> {
  private ValidationHandler<Object> next;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final OtpService otpService;

  @Override
  public void setNext(ValidationHandler<Object> next) {
    this.next = next;
  }

  @Override
  public void validate(Object request, List<String> limit) {
    log.info("<<<< validating authentication >>>>");
    TransferDTO transferDTO = ClientUtil.getObjectMapper().convertValue(request, TransferDTO.class);
    otpService.effect(
        RequestContextHolder.get()
            .getOrganizationId()
            .concat(ClientUtil.getValueFromAccessToken("email")),
        OtpType.TRANSACTION_AUTHORIZATION,
        transferDTO.getAuthenticationKey());
    log.info("<<<< done validating authentication >>>>");
    if (next != null) next.validate(request, limit);
  }
}
