/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.transfer.data.modules.transfer.service.single;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;
import static com.digicore.lucid.common.lib.util.ClientUtil.getPageable;

import com.digicore.lucid.common.lib.profile.dto.LucidSearchRequest;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.transaction.dto.transfer.TransferDTO;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.customer.transfer.data.modules.profile.repository.CustomerProfileRepository;
import com.digicore.lucid.customer.transfer.data.modules.transfer.model.single.CustomerTransferHistory;
import com.digicore.lucid.customer.transfer.data.modules.transfer.repository.single.CustomerTransferHistoryRepository;
import com.digicore.lucid.customer.transfer.data.modules.transfer.service.DataAccessService;
import com.digicore.lucid.customer.transfer.data.modules.transfer.specification.CustomerTransferHistorySpecification;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-03(Mon)-2025
 */

@Service
@RequiredArgsConstructor
public class BackOfficeTransferDataAccessService implements DataAccessService<TransferDTO> {
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final CustomerTransferHistoryRepository customerTransferHistoryRepository;
  private final CustomerProfileRepository customerProfileRepository;
  private final CustomerTransferHistorySpecification customerTransferHistorySpecification;

  @Override
  public PaginatedResponseDTO<TransferDTO> retrieve(
      String accountNumber, int pageNo, int pageSize) {
    Page<CustomerTransferHistory> transferHistoryPage =
        customerTransferHistoryRepository.findAllByCustomerProfileOrganizationId(
            accountNumber, getPageable(pageNo, pageSize));
    return map(transferHistoryPage);
  }

  @Override
  public PaginatedResponseDTO<TransferDTO> retrieveAll(
      String bankOrganizationId, int pageNo, int pageSize) {
    Page<CustomerTransferHistory> transferHistoryPage =
        customerTransferHistoryRepository.findAllByCustomerProfileBankOrganizationId(
            bankOrganizationId, getPageable(pageNo, pageSize));
    return map(transferHistoryPage);
  }

  @Override
  public TransferDTO retrieve(String transactionId, String organizationId) {
    CustomerTransferHistory transfer =
        customerTransferHistoryRepository
            .findFirstByTransactionIdAndCustomerProfileBankOrganizationId(
                transactionId, organizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getTransferMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));
    return map(transfer);
  }

  @Override
  public PaginatedResponseDTO<TransferDTO> retrieve(LucidSearchRequest lucidSearchRequest) {

    Specification<CustomerTransferHistory> specification =
        customerTransferHistorySpecification.buildSpecification(lucidSearchRequest);

    Page<CustomerTransferHistory> transferHistoryPage =
        customerTransferHistoryRepository.findAll(
            specification, getPageable(lucidSearchRequest.getPage(), lucidSearchRequest.getSize()));

    return map(transferHistoryPage);
  }

  private PaginatedResponseDTO<TransferDTO> map(Page<CustomerTransferHistory> transferHistoryPage) {
    return PaginatedResponseDTO.<TransferDTO>builder()
        .content(transferHistoryPage.stream().map(this::map).toList())
        .isFirstPage(transferHistoryPage.isFirst())
        .isLastPage(transferHistoryPage.isLast())
        .currentPage(transferHistoryPage.getNumber() + 1)
        .totalItems(transferHistoryPage.getTotalElements())
        .totalPages(transferHistoryPage.getTotalPages())
        .build();
  }

  private TransferDTO map(CustomerTransferHistory transferHistoryEntity) {
    TransferDTO transferDTO = new TransferDTO();
    BeanUtilWrapper.copyNonNullProperties(transferHistoryEntity, transferDTO);
    return transferDTO;
  }
}
