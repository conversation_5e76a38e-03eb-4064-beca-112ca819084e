/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.config;

import jakarta.annotation.Nonnull;
import jakarta.annotation.PostConstruct;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.*;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createdOn Jul-07(Mon)-2025
 */
@Component
public class CustomLoginSanityCheck implements EnvironmentAware {

  private Environment environment;

  @Override
  public void setEnvironment(@Nonnull Environment environment) {
    this.environment = environment;
  }

  @PostConstruct
  public void validate() {
    boolean defaultEnabled =
        Boolean.parseBoolean(environment.getProperty("custom-login.default.enabled", "true"));

    int customEnabledCount = 0;

    if (environment instanceof ConfigurableEnvironment configurableEnv) {
      MutablePropertySources propertySources = configurableEnv.getPropertySources();

      for (PropertySource<?> source : propertySources) {
        if (source instanceof EnumerablePropertySource<?> enumerableSource) {
          for (String name : enumerableSource.getPropertyNames()) {
            if (name.startsWith("custom-login.")
                && !name.equals("custom-login.default.enabled")
                && name.endsWith(".enabled")) {

              String value = environment.getProperty(name);
              if ("true".equalsIgnoreCase(value)) {
                customEnabledCount++;
              }
            }
          }
        }
      }
    }

    if (defaultEnabled && customEnabledCount > 0) {
      throw new IllegalStateException("Cannot enable default login when a custom login is active.");
    }

    if (customEnabledCount > 1) {
      throw new IllegalStateException("Only one custom login can be active at a time.");
    }
  }
}
