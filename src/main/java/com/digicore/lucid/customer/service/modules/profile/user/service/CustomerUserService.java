/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.profile.user.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.CUSTOMER;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.USER_EDIT_DTO;
import static com.digicore.lucid.common.lib.constant.system.SystemConstant.*;
import static com.digicore.lucid.common.lib.notification.constant.MailTemplateConstant.PASSWORD_RESET_OTP;
import static com.digicore.lucid.common.lib.notification.constant.MailTemplateConstant.PASSWORD_UPDATE;

import com.digicore.lucid.common.lib.authentication.dto.ChangePasswordRequestDTO;
import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.model.LoginAttempt;
import com.digicore.lucid.common.lib.authentication.repository.LoginAttemptRepository;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.authentication.service.LucidLoginAttemptService;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.notification.config.MailPropertyConfig;
import com.digicore.lucid.common.lib.notification.request.NotificationRequestType;
import com.digicore.lucid.common.lib.notification.request.NotificationServiceRequest;
import com.digicore.lucid.common.lib.notification.service.NotificationDispatcher;
import com.digicore.lucid.common.lib.otp.enums.OtpType;
import com.digicore.lucid.common.lib.otp.service.OtpService;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.lucid.common.lib.profile.dto.BankPreferenceDTO;
import com.digicore.lucid.common.lib.profile.dto.CustomerUserProfileApiResponse;
import com.digicore.lucid.common.lib.profile.dto.UserEditDTO;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.profile.service.UserProfileService;
import com.digicore.lucid.common.lib.properties.FeaturePropertyConfig;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.authentication.repository.CustomerUserAuthProfileRepository;
import com.digicore.lucid.customer.data.modules.authentication.repository.CustomerUserPasswordHistoryRepository;
import com.digicore.lucid.customer.data.modules.authentication.service.CustomerUserPasswordResetService;
import com.digicore.lucid.customer.service.modules.profile.user.proxy.CustomerUserProxyService;
import com.digicore.registhentication.authentication.dtos.request.LoginAttemptDTO;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Feb-12(Wed)-2025
 */

@Service
@RequiredArgsConstructor
public class CustomerUserService implements CustomerUserProxyService {
  private final UserProfileService<UserProfileDTO> customerUserProfileService;
  private final AuthProfileService<UserAuthProfileDTO> customerUserAuthProfileService;
  private final OtpService otpService;
  private final PasswordEncoder passwordEncoder;
  private final RedissonClient redissonClient;
  private final FeaturePropertyConfig featurePropertyConfig;
  private final MessagePropertyConfig messagePropertyConfig;
  private final CustomerUserPasswordHistoryRepository customerUserPasswordHistoryRepository;
  private final CustomerUserAuthProfileRepository customerUserAuthProfileRepository;
  private final LoginAttemptRepository loginAttemptRepository;
  private final LucidLoginAttemptService lucidLoginAttemptService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MailPropertyConfig mailPropertyConfig;
  private final NotificationDispatcher notificationDispatcher;
  private final CustomerUserPasswordResetService customerUserPasswordResetService;

  public PaginatedResponseDTO<CustomerUserProfileApiResponse> retrieveCustomerProfiles(
      String bankOrganizationId, String organizationId, int pageNumber, int pageSize) {
    PaginatedResponseDTO<UserAuthProfileDTO> paginatedResponseDTO =
        customerUserAuthProfileService.retrieveUserProfile(
            bankOrganizationId, organizationId, pageNumber, pageSize);

    return PaginatedResponseDTO.<CustomerUserProfileApiResponse>builder()
        .content(
            paginatedResponseDTO.getContent().stream()
                .map(CustomerUserService::getCustomerUserProfileApiResponse)
                .toList())
        .currentPage(paginatedResponseDTO.getCurrentPage())
        .totalPages(paginatedResponseDTO.getTotalPages())
        .totalItems(paginatedResponseDTO.getTotalItems())
        .isFirstPage(paginatedResponseDTO.getIsFirstPage())
        .isLastPage(paginatedResponseDTO.getIsLastPage())
        .build();
  }

  private static CustomerUserProfileApiResponse getCustomerUserProfileApiResponse(
      UserAuthProfileDTO userAuthProfileDTO) {
    CustomerUserProfileApiResponse customerUserProfileApiResponse =
        new CustomerUserProfileApiResponse();
    customerUserProfileApiResponse.setUsername(userAuthProfileDTO.getUsername());
    customerUserProfileApiResponse.setStatus(userAuthProfileDTO.getStatus());
    customerUserProfileApiResponse.setEmail(userAuthProfileDTO.getUserProfile().getEmail());
    customerUserProfileApiResponse.setFirstName(userAuthProfileDTO.getUserProfile().getFirstName());
    customerUserProfileApiResponse.setLastName(userAuthProfileDTO.getUserProfile().getLastName());
    customerUserProfileApiResponse.setAssignedRole(userAuthProfileDTO.getAssignedRole());
    customerUserProfileApiResponse.setPhoneNumber(
        userAuthProfileDTO.getUserProfile().getPhoneNumber());
    return customerUserProfileApiResponse;
  }

  public CustomerUserProfileApiResponse retrieveCustomerProfileDetails(
      String username, String bankOrganizationId, String organizationId) {
    UserAuthProfileDTO userAuthProfileDTO =
        customerUserAuthProfileService.retrieveUserProfile(
            username, bankOrganizationId, organizationId);

    return getCustomerUserProfileApiResponse(userAuthProfileDTO);
  }

  public void editCustomerUserDetails(UserEditDTO userEditDTO) {
    customerUserProfileService.editUserProfile(userEditDTO);
  }

  public void enableCustomerUser(String username, String organizationId) {
    customerUserAuthProfileService.enableUserProfile(username, organizationId);
  }

  public void disableCustomerUser(String username, String organizationId) {
    customerUserAuthProfileService.disableUserProfile(username, organizationId);
  }

  @Override
  @MakerChecker(
      checkerPermission = "approve-edit-customer-user-detail",
      makerPermission = "edit-customer-user-detail",
      requestClassName = USER_EDIT_DTO,
      activity = EDIT,
      module = CUSTOMER)
  public Object editCustomerUserDetails(Object initialData, Object updateData, Object... files) {
    UserEditDTO userEditDTO = (UserEditDTO) updateData;
    customerUserProfileService.editUserProfile(userEditDTO);
    return Optional.empty();
  }

  @Override
  @MakerChecker(
      checkerPermission = "approve-enable-customer-user",
      makerPermission = "enable-customer-user",
      requestClassName = USER_EDIT_DTO,
      activity = ENABLE,
      module = CUSTOMER)
  public Object enableCustomerUser(Object initialData, Object updateData, Object... files) {
    UserEditDTO userEditDTO = (UserEditDTO) updateData;
    customerUserAuthProfileService.enableUserProfile(
        userEditDTO.getUsername(), userEditDTO.getOrganizationId());
    return Optional.empty();
  }

  @Override
  @MakerChecker(
      checkerPermission = "approve-disable-customer-user",
      makerPermission = "disable-customer-user",
      requestClassName = USER_EDIT_DTO,
      activity = DISABLE,
      module = CUSTOMER)
  public Object disableCustomerUser(Object initialData, Object updateData, Object... files) {
    UserEditDTO userEditDTO = (UserEditDTO) updateData;
    customerUserAuthProfileService.disableUserProfile(
        userEditDTO.getUsername(), userEditDTO.getOrganizationId());
    return Optional.empty();
  }

  public UserProfileDTO fetchUserprofile() {
    return customerUserProfileService.retrieveLoggedInUserProfile();
  }

  public void initResetPassword(String userName) {
    UserAuthProfileDTO authProfileDTO =
        customerUserAuthProfileService.retrieveAuthProfile(userName, false);
    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();
    otpService.send(
        NotificationServiceRequest.builder()
            .recipients(List.of(authProfileDTO.getUserProfile().getEmail()))
            .firstName(authProfileDTO.getUserProfile().getFirstName())
            .notificationSubject(
                messagePropertyConfig.getEmailMessage(PASSWORD_CHANGE_REQUEST_SUBJECT))
            .dateTime(LocalDateTime.now())
            .notificationRequestType(NotificationRequestType.SEND_PASSWORD_UPDATE_EMAIL)
            .primaryColor(
                bankPreferenceDTO == null
                    ? featurePropertyConfig.getPrimaryColor()
                    : bankPreferenceDTO.getTheme().getPrimaryColor())
            .platformName(CORPORATE)
            .bankName(
                bankPreferenceDTO == null
                    ? RequestContextHolder.get().getSubDomainName()
                    : bankPreferenceDTO.getTheme().getBankName())
            .logoLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getTheme().getLogo())
            .subDomain(RequestContextHolder.get().getSubDomainName())
            .supportMailLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportEmail())
            .helpUrl(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportUrl())
            .isHtml(true)
            .build(),
        authProfileDTO
            .getUserProfile()
            .getEmail()
            .concat(RequestContextHolder.get().getOrganizationId()),
        OtpType.CHANGE_PASSWORD,
        mailPropertyConfig.getTemplate(PASSWORD_RESET_OTP));
  }

  public void changeUserPassword(ChangePasswordRequestDTO updatePasswordRequestDTO) {
    UserAuthProfileDTO authProfileDTO =
        customerUserAuthProfileService.retrieveAuthProfile(ClientUtil.getLoggedInUsername(), false);
    otpService.effect(
        authProfileDTO
            .getUserProfile()
            .getEmail()
            .concat(RequestContextHolder.get().getOrganizationId()),
        OtpType.CHANGE_PASSWORD,
        updatePasswordRequestDTO.getResetCode());
    customerUserPasswordResetService.updateAccountPassword(updatePasswordRequestDTO);
    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();
    notificationDispatcher.dispatchNotification(
        NotificationServiceRequest.builder()
            .notificationSubject(
                messagePropertyConfig.getEmailMessage(PASSWORD_RESET_SUCCESSFUL_SUBJECT))
            .recipients(List.of(authProfileDTO.getUserProfile().getEmail()))
            .dateTime(LocalDateTime.now())
            .primaryColor(
                bankPreferenceDTO == null
                    ? featurePropertyConfig.getPrimaryColor()
                    : bankPreferenceDTO.getTheme().getPrimaryColor())
            .platformName(CORPORATE)
            .bankName(
                bankPreferenceDTO == null
                    ? RequestContextHolder.get().getSubDomainName()
                    : bankPreferenceDTO.getTheme().getBankName())
            .logoLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getTheme().getLogo())
            .subDomain(RequestContextHolder.get().getSubDomainName())
            .supportMailLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportEmail())
            .helpUrl(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportUrl())
            .notificationRequestType(NotificationRequestType.SEND_PASSWORD_UPDATE_EMAIL)
            .build(),
        mailPropertyConfig.getTemplate(PASSWORD_UPDATE));
  }

  public static LoginAttemptDTO convertToDto(LoginAttempt attempt) {
    LoginAttemptDTO dto = new LoginAttemptDTO();
    BeanUtils.copyProperties(attempt, dto);
    return dto;
  }

  public List<LoginAttemptDTO> getAllLoginAttempts(String bankOrganizationId) {
    return loginAttemptRepository.findByBankOrganizationId(bankOrganizationId).stream()
        .map(CustomerUserService::convertToDto)
        .collect(Collectors.toList());
  }

  public List<LoginAttemptDTO> filterLoginAttemptsByUsername(
      String bankOrganizationId, String username) {
    return loginAttemptRepository
        .searchByUsernameAndBankOrganizationId(bankOrganizationId, username)
        .stream()
        .map(CustomerUserService::convertToDto)
        .collect(Collectors.toList());
  }

  public Optional<LoginAttemptDTO> getLoginAttemptByUsername(String username) {
    return loginAttemptRepository
        .findFirstByUsernameOrderByCreatedDate(username)
        .map(CustomerUserService::convertToDto);
  }

  public void lockCustomerUserAccount(
      String username, String bankOrganizationId, String organizationId) {
    LoginAttempt userLoginAttempt =
        loginAttemptRepository
            .findFirstByUsernameAndOrganizationIdAndBankOrganizationIdOrderByCreatedDate(
                username, organizationId, bankOrganizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getAccountMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));

    lucidLoginAttemptService.manualLock(userLoginAttempt);
  }

  public void unlockCustomerUserAccount(
      String username, String bankOrganizationId, String organizationId) {

    LoginAttempt userLoginAttempt =
        loginAttemptRepository
            .findFirstByUsernameAndOrganizationIdAndBankOrganizationIdOrderByCreatedDate(
                username, organizationId, bankOrganizationId)
            .orElseThrow(
                () ->
                    exceptionHandler.processCustomException(
                        messagePropertyConfig.getAccountMessage(NOT_FOUND),
                        HttpStatus.BAD_REQUEST));

    lucidLoginAttemptService.unlockUser(convertToDto(userLoginAttempt));
  }
}
