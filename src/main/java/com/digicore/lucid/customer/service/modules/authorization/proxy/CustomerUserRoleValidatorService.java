/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.authorization.proxy;

import com.digicore.lucid.common.lib.authorization.dto.PermissionDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleCreationDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleDTO;
import com.digicore.lucid.common.lib.authorization.service.PermissionService;
import com.digicore.lucid.common.lib.authorization.service.RoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Mar-12(Wed)-2025
 */

@Service
@RequiredArgsConstructor
public class CustomerUserRoleValidatorService {
  private final CustomerUserRoleProxyService customerUserProxyService;
  private final RoleService<RoleDTO, RoleCreationDTO> customerUserRoleService;
  private final PermissionService<PermissionDTO> customerUserPermissionService;

  public void createRole(RoleCreationDTO roleDTO) {
    customerUserRoleService.validateRole(roleDTO.getName(), false, true);
    customerUserPermissionService.validatePermissions(roleDTO.getPermissions().stream().toList());
    roleDTO.setSystemInitiated(false);
    customerUserProxyService.createRole(null, roleDTO);
  }

  public void deleteRole(String name) {
    customerUserRoleService.validateRole(name, false, false);
    customerUserProxyService.deleteRole(null, name);
  }

  public void editRole(RoleCreationDTO roleDTO) {
    customerUserRoleService.validateRole(roleDTO.getName(), false, false);
    RoleDTO roleToEdit = customerUserRoleService.retrieveRole(roleDTO.getName());
    customerUserPermissionService.validatePermissions(roleDTO.getPermissions().stream().toList());
    customerUserProxyService.editRole(roleToEdit, roleDTO);
  }

  public void disableRole(String roleName) {
    customerUserRoleService.validateRole(roleName, false, false);
    RoleDTO roleToDisable = customerUserRoleService.retrieveRole(roleName);
    RoleDTO roleDTO = new RoleDTO();
    roleDTO.setName(roleName);
    customerUserProxyService.disableRole(roleToDisable, roleDTO);
  }

  public void enableRole(String roleName) {
    customerUserRoleService.validateRole(roleName, false, false);
    RoleDTO roleToEnable = customerUserRoleService.retrieveRole(roleName);
    RoleDTO roleDTO = new RoleDTO();
    roleDTO.setName(roleName);
    customerUserProxyService.enableRole(roleToEnable, roleDTO);
  }
}
