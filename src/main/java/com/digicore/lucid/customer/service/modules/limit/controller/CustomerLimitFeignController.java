/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.limit.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.limit.LimitSwaggerDocConstant.*;
import static com.digicore.registhentication.util.PageableUtil.*;
import static com.digicore.registhentication.util.PageableUtil.PAGE_SIZE_DEFAULT_VALUE;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.limit.dto.CustomerLimitConfigDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.customer.service.modules.limit.service.CustomerLimitFeignService;
import com.digicore.registhentication.validator.enums.Currency;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-10(Mon)-2025
 */

@Hidden
@RestController
@RequestMapping(API_V1 + CUSTOMER_API + LIMIT_API)
@RequiredArgsConstructor
public class CustomerLimitFeignController {
  private final CustomerLimitFeignService customerLimitFeignService;

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-customer-limit')")
  public ResponseEntity<Object> retrieveLimitConfig(
      @RequestParam(value = PAGE_NUMBER, defaultValue = PAGE_NUMBER_DEFAULT_VALUE, required = false)
          int pageNumber,
      @RequestParam(value = PAGE_SIZE, defaultValue = PAGE_SIZE_DEFAULT_VALUE, required = false)
          int pageSize,
      @RequestParam String organizationId,
      @RequestHeader String bankOrganizationId) {
    return ControllerResponse.buildSuccessResponse(
        customerLimitFeignService.retrieveLimitConfig(
            bankOrganizationId, organizationId, pageNumber, pageSize),
        "Limit types fetched successfully.");
  }

  @GetMapping(RETRIEVE_API)
  @PreAuthorize("hasAuthority('view-customer-limit')")
  public ResponseEntity<Object> retrieveLimitConfig(
      @RequestParam String accountNumber,
      @RequestParam String organizationId,
      @RequestHeader String bankOrganizationId,
      @RequestParam LimitType limitType,
      @RequestParam Currency currency) {
    return ControllerResponse.buildSuccessResponse(
        customerLimitFeignService.retrieveLimitConfig(
            accountNumber, bankOrganizationId, organizationId, limitType, currency),
        "Limit types fetched successfully.");
  }

  @GetMapping(FETCH_API)
  @PreAuthorize("hasAuthority('view-customer-limit')")
  public ResponseEntity<Object> retrieveLimitConfig(
      @RequestParam String accountNumber,
      @RequestParam String organizationId,
      @RequestParam LimitType limitType,
      @RequestParam Currency currency) {
    return ControllerResponse.buildSuccessResponse(
        customerLimitFeignService.retrieveLimitConfig(
            accountNumber, organizationId, limitType, currency),
        "Limit types fetched successfully.");
  }

  @PostMapping(VALIDATE_API)
  @PreAuthorize("hasAuthority('edit-customer-limit')")
  public ResponseEntity<Object> validateLimit(@RequestBody CustomerLimitConfigDTO request) {
    return ControllerResponse.buildSuccessResponse(customerLimitFeignService.verifyLimit(request));
  }

  @PostMapping(CREATE_API)
  @PreAuthorize("hasAuthority('approve-create-customer-limit')")
  public ResponseEntity<Object> createLimit(@RequestBody CustomerLimitConfigDTO request) {
    customerLimitFeignService.createLimitConfig(request);
    return ControllerResponse.buildSuccessResponse();
  }

  @PostMapping(EDIT_API)
  @PreAuthorize("hasAuthority('approve-edit-customer-limit')")
  public ResponseEntity<Object> updateLimit(@RequestBody CustomerLimitConfigDTO request) {
    customerLimitFeignService.editLimitConfig(request);
    return ControllerResponse.buildSuccessResponse();
  }
}
