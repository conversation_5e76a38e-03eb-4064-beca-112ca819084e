/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.branch.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.branch.BranchSwaggerDocConstant.*;
import static com.digicore.registhentication.util.PageableUtil.*;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.export.enums.ExportFileType;
import com.digicore.lucid.customer.data.modules.branch.dto.CustomerBranchDTO;
import com.digicore.lucid.customer.service.modules.branch.dto.CustomerBranchCreateRequest;
import com.digicore.lucid.customer.service.modules.branch.proxy.CustomerBranchValidatorService;
import com.digicore.lucid.customer.service.modules.branch.service.CustomerBranchOperations;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> John
 * @createdOn Apr-04(Fri)-2025
 */

@RestController
@RequestMapping(API_V1 + BRANCH_API)
@RequiredArgsConstructor
@Tag(name = BRANCH_CONTROLLER_TITLE, description = BRANCH_CONTROLLER_DESCRIPTION)
@Validated
public class CustomerBranchController {
  private final CustomerBranchValidatorService customerBranchValidatorService;
  private final CustomerBranchOperations customerBranchOperations;

  @PostMapping(CREATE_API)
  @PreAuthorize("hasAuthority('create-customer-branch')")
  @Operation(
      summary = BRANCH_CONTROLLER_CREATE_TITLE,
      description = BRANCH_CONTROLLER_CREATE_DESCRIPTION)
  public ResponseEntity<Object> createCustomerBranch(
      @RequestBody CustomerBranchCreateRequest request) {
    customerBranchValidatorService.createCustomerBranch(request);
    return ControllerResponse.buildSuccessResponse("Request logged for approval");
  }

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-customer-branches')")
  @Operation(
      summary = BRANCH_CONTROLLER_VIEW_ALL_TITLE,
      description = BRANCH_CONTROLLER_VIEW_ALL_DESCRIPTION)
  public ResponseEntity<Object> retrieveAllCustomerBranches(
      @RequestParam(value = PAGE_NUMBER, defaultValue = PAGE_NUMBER_DEFAULT_VALUE, required = false)
          int pageNumber,
      @RequestParam(value = PAGE_SIZE, defaultValue = PAGE_SIZE_DEFAULT_VALUE, required = false)
          int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        customerBranchOperations.retrieveAllBranches(pageNumber, pageSize),
        "Customer branches retrieved successfully.");
  }

  @GetMapping(RETRIEVE_API)
  @PreAuthorize("hasAuthority('view-customer-branch-details')")
  @Operation(
      summary = BRANCH_CONTROLLER_VIEW_TITLE,
      description = BRANCH_CONTROLLER_VIEW_DESCRIPTION)
  public ResponseEntity<Object> retrieveCustomerBranch(@RequestParam String branchId) {
    return ControllerResponse.buildSuccessResponse(
        customerBranchOperations.retrieveBranchDetails(branchId),
        "Customer branch details retrieved successfully.");
  }

  @PostMapping(EDIT_API)
  @PreAuthorize("hasAuthority('edit-customer-branch')")
  @Operation(
      summary = BRANCH_CONTROLLER_UPDATE_TITLE,
      description = BRANCH_CONTROLLER_UPDATE_DESCRIPTION)
  public ResponseEntity<Object> editCustomerBranch(@RequestBody CustomerBranchDTO request) {
    customerBranchValidatorService.editCustomerBranch(request);
    return ControllerResponse.buildSuccessResponse("Request logged for approval");
  }

  @DeleteMapping(DELETE_API)
  @PreAuthorize("hasAuthority('delete-customer-branch')")
  @Operation(
      summary = BRANCH_CONTROLLER_DELETE_TITLE,
      description = BRANCH_CONTROLLER_DELETE_DESCRIPTION)
  public ResponseEntity<Object> deleteCustomerBranch(@PathVariable("name") String branchId) {
    customerBranchValidatorService.deleteCustomerBranch(branchId);
    return ControllerResponse.buildSuccessResponse("Request logged for approval");
  }

  @GetMapping(EXPORT_API)
  @PreAuthorize("hasAuthority('export-customer-branches')")
  @Operation(
      summary = BRANCH_CONTROLLER_EXPORT_TITLE,
      description = BRANCH_CONTROLLER_EXPORT_DESCRIPTION)
  public ResponseEntity<byte[]> exportCustomerBranches() {
    ExportFileType exportFileType = ExportFileType.CSV;
    return ResponseEntity.ok()
        .header(
            HttpHeaders.CONTENT_DISPOSITION,
            "attachment; filename=customer_branches.".concat(exportFileType.name().toLowerCase()))
        .body(customerBranchOperations.exportCustomerBranches(exportFileType));
  }
}
