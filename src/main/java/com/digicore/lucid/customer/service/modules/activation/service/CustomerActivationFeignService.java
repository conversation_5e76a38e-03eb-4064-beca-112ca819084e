/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.activation.service;

import com.digicore.lucid.common.lib.activation.dto.*;
import com.digicore.lucid.common.lib.activation.enums.CustomerActivationStatus;
import com.digicore.lucid.common.lib.activation.service.ActivationService;
import com.digicore.lucid.common.lib.client.BackOfficeFeignClient;
import com.digicore.lucid.common.lib.profile.service.DocumentService;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.registration.services.DocumentUploadService;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Feb-22(Sat)-2025
 */

@RequiredArgsConstructor
@Service
public class CustomerActivationFeignService {
  private final ActivationService<CustomerActivationDTO> activationService;
  private final ActivationService<CustomerActivationComment> customerActivationCommentService;
  private final DocumentUploadService<List<FileUploadedDTO>, CustomerDocumentUploadDTO>
      customerMultipleDocumentUploadService;
  private final BackOfficeFeignClient backOfficeFeignClient;
  private final DocumentService<FileDownloadDTO> customerDocumentService;

  public List<CustomerActivationComment> retrieveComments(
      String bankOrganizationId, String organizationId) {
    return customerActivationCommentService.retrieveComment(bankOrganizationId, organizationId);
  }

  public PaginatedResponseDTO<CustomerActivationDTO> retrieveProgress(
      String bankOrganizationId,
      CustomerActivationStatus customerActivationStatus,
      int page,
      int size) {
    return activationService.retrieveProgress(
        bankOrganizationId, customerActivationStatus, page, size);
  }

  public CustomerActivationDTO saveProgress(BackOfficeCustomerActivationDTO approvalDecisionDTO) {
    return activationService.saveProgress(approvalDecisionDTO);
  }

  public void saveProgress(String organizationId, String officerCode, String productCode) {
    activationService.saveProgress(organizationId, officerCode, productCode);
  }

  public void processActivation(CustomerActivationDTO customerActivationDTO) {
    activationService.processActivation(customerActivationDTO);
  }

  public void saveActivation(CustomerActivationDTO customerActivationDTO) {
    activationService.saveActivation(customerActivationDTO);
  }

  public FileDownloadDTO retrieveDocument(
      String fileId, String organizationId, HttpServletResponse httpServletResponse) {
    return customerDocumentService.retrieveDocument(fileId, organizationId);
  }
}
