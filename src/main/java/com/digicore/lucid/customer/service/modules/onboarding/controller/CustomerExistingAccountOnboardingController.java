/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.onboarding.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.onboarding.OnboardingSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;

import com.digicore.api.helper.response.ApiResponseJson;
import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.client.BackOfficeFeignClient;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.onboarding.dto.CustomerInitiateOnboardingFlowDTO;
import com.digicore.lucid.customer.data.modules.onboarding.dto.CustomerOnboardingFlowDTO;
import com.digicore.lucid.customer.service.modules.onboarding.service.CustomerExistingAccountOnboardingService;
import com.digicore.registhentication.common.enums.Channel;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> Ucheagwu
 * @createdOn Jun-10(Tue)-2025
 */
@RestController
@RequestMapping(API_V1 + EXISTING_ACCOUNT_ONBOARDING_API)
@RequiredArgsConstructor
@Tag(name = ONBOARDING_CONTROLLER_TITLE, description = ONBOARDING_CONTROLLER_DESCRIPTION)
public class CustomerExistingAccountOnboardingController {
  private final BackOfficeFeignClient backOfficeFeignClient;
  private final CustomerExistingAccountOnboardingService customerExistingAccountOnboardingService;

  @GetMapping("phishing-image/" + FETCH_ALL_API)
  public ResponseEntity<Object> retrievePhishingImages(HttpServletRequest httpServletRequest)
      throws IOException {
    setRequestContext(httpServletRequest);
    return ControllerResponse.buildSuccessResponse(
        customerExistingAccountOnboardingService.retrievePhishingImages());
  }

  @GetMapping("security-question/" + RETRIEVE_ALL_API)
  public ResponseEntity<Object> retrieveSecurityQuestions(HttpServletRequest httpServletRequest) {
    setRequestContext(httpServletRequest);
    return ControllerResponse.buildSuccessResponse(
        customerExistingAccountOnboardingService.retrieveSecurityQuestions());
  }

  @PostMapping(INITIATE_API)
  public ResponseEntity<Object> initiateExistingOnboarding(
      HttpServletRequest httpServletRequest,
      @RequestBody CustomerInitiateOnboardingFlowDTO request) {
    setRequestContext(httpServletRequest);
    return ControllerResponse.buildSuccessResponse(
        customerExistingAccountOnboardingService.initiateOnboardingFlow(request));
  }

  @PostMapping(value = CREATE_API, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResponseEntity<Object> completeExistingOnboarding(
      HttpServletRequest httpServletRequest, @ModelAttribute CustomerOnboardingFlowDTO request) {
    setRequestContext(httpServletRequest);
    return ControllerResponse.buildSuccessResponse(
        customerExistingAccountOnboardingService.completeOnboardingFlow(request));
  }

  private void setRequestContext(HttpServletRequest httpServletRequest) {
    ClientUtil.setRequestContext(httpServletRequest);
    RequestContextHolder.RequestContext requestContext = RequestContextHolder.get();
    requestContext.setChannel(Channel.WEB);
    ApiResponseJson<Object> backOfficeResponse =
        backOfficeFeignClient.getCbaProviderAndCbaToken(requestContext.getSubDomainName());

    assert backOfficeResponse != null;
    RequestContextHolder.RequestContext backOfficeRequestContext =
        getObjectMapper()
            .convertValue(backOfficeResponse.getData(), RequestContextHolder.RequestContext.class);
    requestContext.setCbaProvider(backOfficeRequestContext.getCbaProvider());
    requestContext.setCbaToken(backOfficeRequestContext.getCbaToken());
    requestContext.setBankOrganizationId(backOfficeRequestContext.getOrganizationId());
  }
}
