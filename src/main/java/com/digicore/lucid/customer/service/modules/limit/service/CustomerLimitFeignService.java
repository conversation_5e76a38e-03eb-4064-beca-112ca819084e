/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.limit.service;

import com.digicore.lucid.common.lib.limit.dto.CustomerLimitConfigDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.limit.service.LimitConfigService;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.validator.enums.Currency;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Mar-10(Mon)-2025
 */

@Service
@RequiredArgsConstructor
public class CustomerLimitFeignService {
  private final LimitConfigService<CustomerLimitConfigDTO> customerLimitConfigService;

  public PaginatedResponseDTO<CustomerLimitConfigDTO> retrieveLimitConfig(
      String bankOrganizationId, String organizationId, int pageNumber, int pageSize) {
    return customerLimitConfigService.retrieveLimitConfig(
        organizationId, bankOrganizationId, pageNumber, pageSize);
  }

  public CustomerLimitConfigDTO retrieveLimitConfig(
      String accountNumber,
      String bankOrganizationId,
      String organizationId,
      LimitType limitType,
      Currency currency) {
    return customerLimitConfigService.retrieveLimitConfig(
        accountNumber, limitType, currency, organizationId, bankOrganizationId);
  }

  public CustomerLimitConfigDTO retrieveLimitConfig(
      String accountNumber, String organizationId, LimitType limitType, Currency currency) {
    return customerLimitConfigService.retrieveLimitConfig(
        accountNumber, organizationId, limitType, currency);
  }

  public CustomerLimitConfigDTO verifyLimit(CustomerLimitConfigDTO limitConfigDTO) {
    return customerLimitConfigService.verifyLimitConfigExist(limitConfigDTO);
  }

  public void createLimitConfig(CustomerLimitConfigDTO limitConfigDTO) {
    customerLimitConfigService.createLimitConfig(limitConfigDTO);
  }

  public void editLimitConfig(CustomerLimitConfigDTO limitConfigDTO) {
    customerLimitConfigService.updateLimitConfig(limitConfigDTO);
  }
}
