/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.vendor.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.vendor.VendorSwaggerDocConstant.*;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.customer.data.modules.vendor.dto.VendorRequestDTO;
import com.digicore.lucid.customer.service.modules.vendor.proxy.CustomerVendorValidatorService;
import com.digicore.lucid.customer.service.modules.vendor.service.CustomerVendorOperations;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> Ilori
 * @createdOn 12/03/2025
 */
@RestController
@RequestMapping(API_V1 + VENDOR_API)
@RequiredArgsConstructor
@Tag(name = VENDOR_API_CONTROLLER_TITLE, description = VENDOR_API_CONTROLLER_DESCRIPTION)
public class CustomerVendorController {
  private final CustomerVendorValidatorService customerVendorValidatorService;
  private final CustomerVendorOperations customerVendorOperations;

  @GetMapping(RETRIEVE_API)
  @PreAuthorize("hasAuthority('view-customer-vendor')")
  ResponseEntity<Object> fetchVendor(@RequestParam String employeeId) {
    return ControllerResponse.buildSuccessResponse(
        customerVendorOperations.fetchVendor(employeeId));
  }

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-customer-vendor')")
  ResponseEntity<Object> fetchVendors(@RequestParam int pageNumber, @RequestParam int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        customerVendorOperations.fetchVendors(pageNumber, pageSize));
  }

  @PostMapping(CREATE_API)
  @PreAuthorize("hasAuthority('create-customer-vendor')")
  ResponseEntity<Object> createVendor(@RequestBody VendorRequestDTO createDTO) {
    customerVendorValidatorService.createVendor(createDTO);
    return ControllerResponse.buildSuccessResponse();
  }

  @PostMapping(EDIT_API)
  @PreAuthorize("hasAuthority('edit-customer-vendor')")
  ResponseEntity<Object> editVendor(@RequestBody VendorRequestDTO editDTO) {
    customerVendorValidatorService.editVendor(editDTO);
    return ControllerResponse.buildSuccessResponse();
  }

  @PostMapping(DELETE_API)
  @PreAuthorize("hasAuthority('delete-customer-vendor')")
  ResponseEntity<Object> removeVendor(@PathVariable String name) {
    customerVendorValidatorService.removeVendor(name);
    return ControllerResponse.buildSuccessResponse();
  }
}
