/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.employee.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.EMPLOYEE;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.EMPLOYEE_REQUEST_DTO;
import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;

import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.lucid.customer.data.modules.employee.dto.CustomerEmployeeDTO;
import com.digicore.lucid.customer.data.modules.employee.dto.EmployeeRequestDTO;
import com.digicore.lucid.customer.data.modules.employee.service.DataAccessService;
import com.digicore.lucid.customer.service.modules.employee.proxy.CustomerEmployeeProxyService;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ilori
 * @createdOn 12/03/2025
 */

@Service
@RequiredArgsConstructor
public class CustomerEmployeeOperations implements CustomerEmployeeProxyService {
  private final DataAccessService<CustomerEmployeeDTO> customerEmployeeDataAccessService;

  @MakerChecker(
      checkerPermission = "approve-create-customer-employee",
      makerPermission = "create-customer-employee",
      requestClassName = EMPLOYEE_REQUEST_DTO,
      activity = CREATE,
      module = EMPLOYEE)
  public Object createEmployee(Object initialData, Object dataToUpdate, Object... files) {
    EmployeeRequestDTO createDTO =
        getObjectMapper().convertValue(dataToUpdate, EmployeeRequestDTO.class);
    customerEmployeeDataAccessService.create(createDTO);
    return Optional.empty();
  }

  @MakerChecker(
      checkerPermission = "approve-edit-customer-employee",
      makerPermission = "edit-customer-employee",
      requestClassName = EMPLOYEE_REQUEST_DTO,
      activity = EDIT,
      module = EMPLOYEE)
  public Object editEmployee(Object initialData, Object dataToUpdate, Object... files) {
    EmployeeRequestDTO editDTO =
        getObjectMapper().convertValue(dataToUpdate, EmployeeRequestDTO.class);
    customerEmployeeDataAccessService.edit(editDTO);
    return Optional.empty();
  }

  @MakerChecker(
      checkerPermission = "approve-delete-customer-employee",
      makerPermission = "delete-customer-employee",
      requestClassName = "java.lang.String",
      activity = DELETE,
      module = EMPLOYEE)
  public Object removeEmployee(Object initialData, Object dataToUpdate, Object... files) {
    String employeeId = getObjectMapper().convertValue(dataToUpdate, String.class);
    customerEmployeeDataAccessService.remove(employeeId);
    return Optional.empty();
  }

  public CustomerEmployeeDTO fetchEmployee(String employeeId) {
    return customerEmployeeDataAccessService.retrieve(employeeId);
  }

  public PaginatedResponseDTO<CustomerEmployeeDTO> fetchEmployees(int pageNumber, int pageSize) {
    return customerEmployeeDataAccessService.retrieve(
        RequestContextHolder.get().getOrganizationId(), pageNumber, pageSize);
  }
}
