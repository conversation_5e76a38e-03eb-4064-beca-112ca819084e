/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.content.service;

import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;

import com.digicore.api.helper.response.ApiResponseJson;
import com.digicore.lucid.common.lib.client.BackOfficeFeignClient;
import com.digicore.lucid.common.lib.content.dto.BankContentDTO;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Mar-15(Sat)-2025
 */

@Service
@RequiredArgsConstructor
public class CustomerContentService {
  private final BackOfficeFeignClient backOfficeFeignClient;

  public PaginatedResponseDTO<BankContentDTO> viewAllContent(int pageNumber, int pageSize) {
    ApiResponseJson<Object> response =
        backOfficeFeignClient.retrieveAllContent(pageNumber, pageSize);
    Object responseBody = response.getData();
    return getObjectMapper().convertValue(responseBody, new TypeReference<>() {});
  }

  public BankContentDTO viewContent(String contentId) {
    ApiResponseJson<Object> response = backOfficeFeignClient.retrieveContent(contentId);
    Object responseBody = response.getData();

    return getObjectMapper().convertValue(responseBody, BankContentDTO.class);
  }
}
