/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.activation.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.activation.ActivationSwaggerDocConstant.*;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.activation.dto.BackOfficeCustomerActivationDTO;
import com.digicore.lucid.common.lib.activation.dto.CustomerActivationDTO;
import com.digicore.lucid.common.lib.activation.enums.CustomerActivationStatus;
import com.digicore.lucid.customer.service.modules.activation.service.CustomerActivationFeignService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-18(Tue)-2025
 */

@Hidden
@RestController
@RequestMapping(API_V1 + CUSTOMER_API + ACTIVATION_API)
@Tag(name = ACTIVATION_CONTROLLER_TITLE, description = ACTIVATION_CONTROLLER_DESCRIPTION)
@RequiredArgsConstructor
public class CustomerActivationFeignController {
  private final CustomerActivationFeignService customerActivationFeignService;

  @GetMapping(RETRIEVE_ACTIVATION_PROGRESS_API)
  public ResponseEntity<Object> retrieveProgress(
      @RequestHeader("bankOrganizationId") String bankOrganizationId,
      @RequestParam() CustomerActivationStatus customerActivationStatus,
      @RequestParam() int page,
      @RequestParam() int size) {
    return ControllerResponse.buildSuccessResponse(
        customerActivationFeignService.retrieveProgress(
            bankOrganizationId, customerActivationStatus, page, size));
  }

  @PostMapping(SAVE_PROGRESS_API)
  public ResponseEntity<Object> saveProgress(
      @RequestBody BackOfficeCustomerActivationDTO approvalDecisionDTO) {
    return ControllerResponse.buildSuccessResponse(
        customerActivationFeignService.saveProgress(approvalDecisionDTO));
  }

  @GetMapping(RETRIEVE_ACTIVIATION_COMMENT_API)
  public ResponseEntity<Object> retrieveComment(
      @RequestHeader("organizationId") String bankOrganizationId,
      @RequestParam("organizationId") String organizationId) {
    return ControllerResponse.buildSuccessResponse(
        customerActivationFeignService.retrieveComments(bankOrganizationId, organizationId));
  }

  @PostMapping(COMPLETE_API)
  public ResponseEntity<Object> processActivation(
      @RequestBody CustomerActivationDTO customerActivationDTO) {
    customerActivationFeignService.processActivation(customerActivationDTO);
    return ControllerResponse.buildSuccessResponse();
  }

  @PostMapping(UPDATE_API)
  public ResponseEntity<Object> saveActivation(
      @RequestBody CustomerActivationDTO customerActivationDTO) {
    customerActivationFeignService.saveActivation(customerActivationDTO);
    return ControllerResponse.buildSuccessResponse();
  }

  @GetMapping(value = RETRIEVE_ACTIVATION_DOCUMENT_API)
  public ResponseEntity<Object> retrieveDocument(
      @RequestParam String fileId,
      @RequestParam String organizationId,
      HttpServletResponse httpServletResponse) {
    return ControllerResponse.buildSuccessResponse(
        customerActivationFeignService.retrieveDocument(
            fileId, organizationId, httpServletResponse),
        "Activation document retrieved successfully");
  }
}
