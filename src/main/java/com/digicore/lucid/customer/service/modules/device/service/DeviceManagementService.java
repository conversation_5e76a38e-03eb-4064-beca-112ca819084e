/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.device.service;

import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.device.dto.DeviceDTO;
import com.digicore.lucid.customer.data.modules.device.dto.DeviceRequestDTO;
import com.digicore.lucid.customer.data.modules.device.service.DeviceDAS;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn 02/06/2025
 */

@Service
@RequiredArgsConstructor
public class DeviceManagementService {
  private final DeviceDAS deviceDataAccessService;

  public void block(String deviceId) {
    deviceDataAccessService.blockDevice(
        deviceId, ClientUtil.getLoggedInUsername(), RequestContextHolder.get().getSubDomainName());
  }

  public void unblock(String deviceId) {
    deviceDataAccessService.unblockDevice(
        deviceId, ClientUtil.getLoggedInUsername(), RequestContextHolder.get().getSubDomainName());
  }

  public void edit(DeviceRequestDTO editDTO) {
    deviceDataAccessService.edit(
        editDTO, ClientUtil.getLoggedInUsername(), RequestContextHolder.get().getSubDomainName());
  }

  public DeviceDTO retrieve(String deviceId) {
    return deviceDataAccessService.retrieve(
        deviceId, ClientUtil.getLoggedInUsername(), RequestContextHolder.get().getSubDomainName());
  }

  public List<DeviceDTO> retrieve() {
    return deviceDataAccessService.retrieve(
        ClientUtil.getLoggedInUsername(), RequestContextHolder.get().getSubDomainName());
  }
}
