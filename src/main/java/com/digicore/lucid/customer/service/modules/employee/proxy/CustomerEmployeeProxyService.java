/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.employee.proxy;

/*
 * <AUTHOR>
 * @createdOn 12/03/2025
 */
public interface CustomerEmployeeProxyService {
  Object createEmployee(Object initialData, Object updateData, Object... files);

  Object editEmployee(Object initialData, Object updateData, Object... files);

  Object removeEmployee(Object initialData, Object updateData, Object... files);
}
