/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.dashboard.service;

import static java.time.temporal.ChronoUnit.DAYS;

import com.digicore.lucid.common.lib.beneficiary.dto.CustomerBeneficiaryDTO;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.customer.data.modules.beneficiary.service.DataAccessService;
import com.digicore.lucid.customer.service.modules.account.service.CustomerAccountOperations;
import com.digicore.lucid.customer.service.modules.dashboard.dto.AccountFlowDTO;
import com.digicore.lucid.integration.lib.modules.service.account.response.OrganizationTransactionDataResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ilori
 * @createdOn 29/04/2025
 */

@Service
@RequiredArgsConstructor
public class DashboardService {
  private final CustomerAccountOperations customerAccountOperations;
  private final DataAccessService<CustomerBeneficiaryDTO> customerBeneficiaryDataAccessService;

  public Long fetchBeneficiaryCount() {
    return customerBeneficiaryDataAccessService.count(
        RequestContextHolder.get().getOrganizationId());
  }

  public Map<LocalDate, AccountFlowDTO> accountFlows(
      String accountNumber, LocalDate startDate, LocalDate endDate) {
    List<OrganizationTransactionDataResponse> transactionResponse =
        customerAccountOperations.fetchTransactions(accountNumber, startDate, endDate);
    Long daysBetween = DAYS.between(startDate, endDate);
    LocalDate currentDay = startDate;
    Map<LocalDate, AccountFlowDTO> flows = new HashMap<>();
    Map<LocalDate, List<OrganizationTransactionDataResponse>> responseMap = new HashMap<>();

    for (OrganizationTransactionDataResponse transactionData : transactionResponse) {
      LocalDate temp = transactionData.getTransactionDate().toLocalDate();
      if (responseMap.get(temp) != null) {
        responseMap.get(temp).add(transactionData);
        continue;
      }
      responseMap.put(
          transactionData.getTransactionDate().toLocalDate(),
          new ArrayList<>(List.of(transactionData)));
    }

    for (int i = 0; i <= daysBetween; i++) {
      if (responseMap.get(currentDay) == null) {
        flows.put(currentDay, null);
        currentDay = currentDay.plusDays(1);
        continue;
      }
      BigDecimal inFlow = new BigDecimal(0L);
      BigDecimal outFlow = new BigDecimal(0L);
      BigDecimal closingBalance = null;
      BigDecimal openingBalance = null;
      LocalDateTime firstTransactionDateTime = null;
      LocalDateTime lastTransactionDateTime = null;

      for (OrganizationTransactionDataResponse transactionData : responseMap.get(currentDay)) {
        if (firstTransactionDateTime == null
            || transactionData.getTransactionDate().isBefore(firstTransactionDateTime)) {
          firstTransactionDateTime = transactionData.getTransactionDate();
          openingBalance = new BigDecimal(transactionData.getOpeningBalance());
        }
        if (lastTransactionDateTime == null
            || transactionData.getTransactionDate().isAfter(lastTransactionDateTime)) {
          lastTransactionDateTime = transactionData.getTransactionDate();
          closingBalance = new BigDecimal(transactionData.getBalance());
        }

        if ("CREDIT".equalsIgnoreCase(transactionData.getRecordType())) {
          inFlow = inFlow.add(new BigDecimal(transactionData.getAmount()));
        } else {
          outFlow = outFlow.add(new BigDecimal(transactionData.getAmount()));
        }
      }

      flows.put(currentDay, new AccountFlowDTO(openingBalance, closingBalance, inFlow, outFlow));
      currentDay = currentDay.plusDays(1);
    }

    return flows;
  }
}
