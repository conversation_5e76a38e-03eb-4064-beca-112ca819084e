/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.activation.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.ONBOARDING_SUBJECT;
import static com.digicore.lucid.common.lib.notification.constant.MailTemplateConstant.SIGNATORY_INVITE;

import com.digicore.lucid.common.lib.activation.dto.CustomerActivationComment;
import com.digicore.lucid.common.lib.activation.dto.CustomerActivationDTO;
import com.digicore.lucid.common.lib.activation.dto.CustomerDocumentUploadDTO;
import com.digicore.lucid.common.lib.activation.dto.CustomerUserDetailDTO;
import com.digicore.lucid.common.lib.activation.dto.FileUploadedDTO;
import com.digicore.lucid.common.lib.activation.service.ActivationService;
import com.digicore.lucid.common.lib.client.BackOfficeFeignClient;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.notification.config.MailPropertyConfig;
import com.digicore.lucid.common.lib.notification.request.NotificationRequestType;
import com.digicore.lucid.common.lib.notification.request.NotificationServiceRequest;
import com.digicore.lucid.common.lib.notification.service.NotificationDispatcher;
import com.digicore.lucid.common.lib.otp.enums.OtpType;
import com.digicore.lucid.common.lib.otp.service.OtpService;
import com.digicore.lucid.common.lib.profile.dto.BankPreferenceDTO;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.properties.MiscPropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerBvnValidationDTO;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerSignatoryInviteRequestDTO;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.integration.lib.modules.config.properties.SecurityPropertyConfig;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.account.response.BvnValidationResponse;
import com.digicore.registhentication.registration.services.DocumentUploadService;
import com.digicore.registhentication.registration.services.RegistrationService;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-17(Mon)-2025
 */

@Service
@RequiredArgsConstructor
public class CustomerActivationProcessor {
  private final ActivationService<CustomerActivationDTO> activationService;
  private final ActivationService<CustomerActivationComment> customerActivationCommentService;
  private final DocumentUploadService<List<FileUploadedDTO>, CustomerDocumentUploadDTO>
      customerMultipleDocumentUploadService;
  private final BackOfficeFeignClient backOfficeFeignClient;
  private final NotificationDispatcher notificationDispatcher;
  private final MessagePropertyConfig messagePropertyConfig;
  private final RedissonClient redissonClient;
  private final MiscPropertyConfig miscPropertyConfig;
  private final MailPropertyConfig mailPropertyConfig;
  private final OtpService otpService;
  private final SecurityPropertyConfig securityPropertyConfig;
  private final RegistrationService<UserProfileDTO, CustomerSignatoryInviteRequestDTO>
      customerSignatoryInvitationRegistrationService;

  /**
   * Saves the progress of customer activation data. If the bank organization ID is not provided, it
   * fetches the ID from the back-office service and updates the customer activation data before
   * saving.
   *
   * @param customerActivationDTO The customer activation data to save.
   */
  public void saveProgress(CustomerActivationDTO customerActivationDTO) {
    if (ClientUtil.nullOrEmpty(customerActivationDTO.getBankOrganizationId())) {
      customerActivationDTO.setBankOrganizationId(
          backOfficeFeignClient
              .getOrganizationId(RequestContextHolder.get().getSubDomainName())
              .getData());
    }
    activationService.saveProgress(customerActivationDTO);
  }

  /**
   * Retrieves the saved progress of customer activation for the specified organization ID.
   *
   * @param organizationId The ID of the organization to retrieve activation progress for.
   * @return The customer activation data associated with the organization ID.
   */
  public CustomerActivationDTO retrieveProgress(String organizationId) {
    return activationService.retrieveProgress(organizationId);
  }

  /**
   * Retrieves customer activation comments for the specified organization ID.
   *
   * @param organizationId The ID of the organization for which comments are retrieved.
   * @return A list of customer activation comments associated with the organization ID.
   */
  public List<CustomerActivationComment> retrieveComments(String organizationId) {
    return customerActivationCommentService.retrieveComment(organizationId);
  }

  /**
   * Uploads multiple documents associated with a customer activation process.
   *
   * @param customerDocumentUploadDTO Object containing document details to upload.
   * @return A list of uploaded document details as DTOs.
   */
  public List<FileUploadedDTO> uploadDocument(CustomerDocumentUploadDTO customerDocumentUploadDTO) {
    return customerMultipleDocumentUploadService.uploadMultipleDocument(customerDocumentUploadDTO);
  }

  /**
   * Sends invitation emails to all unvalidated email addresses associated with the provided
   * customer activation data. If a user detail corresponding to an email is found, it is further
   * processed.
   *
   * @param customerActivationDTO The customer activation data containing email information.
   */
  private void sendInviteMails(CustomerActivationDTO customerActivationDTO) {
    List<String> unValidatedEmails =
        activationService.retrieveUnValidatedEmail(customerActivationDTO);
    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();
    unValidatedEmails.forEach(
        email -> {
          CustomerUserDetailDTO userDetail =
              getCustomerUserDetailByEmail(customerActivationDTO, email);
          if (userDetail != null) {
            notificationDispatcher.dispatchNotification(
                NotificationServiceRequest.builder()
                    .notificationSubject(messagePropertyConfig.getEmailMessage(ONBOARDING_SUBJECT))
                    .recipients(List.of(email))
                    .dateTime(LocalDateTime.now())
                    .userRole(userDetail.getSignUpType().toString())
                    .firstName(userDetail.getFirstName())
                    .customerName(
                        customerActivationDTO.getCustomerBusinessDetailDTO().getOrganizationName())
                    .bankName(
                        bankPreferenceDTO == null
                            ? RequestContextHolder.get().getSubDomainName()
                            : bankPreferenceDTO.getTheme().getBankName())
                    .platformName(RequestContextHolder.get().getPlatform())
                    .logoLink(
                        bankPreferenceDTO == null ? "" : bankPreferenceDTO.getTheme().getLogo())
                    .subDomain(RequestContextHolder.get().getSubDomainName())
                    .onboardingPath(miscPropertyConfig.getOnboardingPath())
                    .supportMailLink(
                        bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportEmail())
                    .helpUrl(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportUrl())
                    .notificationRequestType(
                        NotificationRequestType.SEND_INVITE_FOR_BACKOFFICE_EMAIL)
                    .build(),
                mailPropertyConfig.getTemplate(SIGNATORY_INVITE));
          }
        });
  }

  public BankPreferenceDTO retrieveBankPreferences() {
    return (BankPreferenceDTO)
        redissonClient
            .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
            .get();
  }

  public void sendInviteMail(CustomerUserDetailDTO customerUserDetailDTO) {
    activationService.addSignatory(
        customerUserDetailDTO, RequestContextHolder.get().getOrganizationId());
    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();
    notificationDispatcher.dispatchNotification(
        NotificationServiceRequest.builder()
            .notificationSubject(messagePropertyConfig.getEmailMessage(ONBOARDING_SUBJECT))
            .recipients(List.of(customerUserDetailDTO.getEmail()))
            .dateTime(LocalDateTime.now())
            .userRole(customerUserDetailDTO.getSignUpType().toString())
            .firstName(customerUserDetailDTO.getFirstName())
            .customerName(RequestContextHolder.get().getSubDomainName())
            .bankName(
                bankPreferenceDTO == null
                    ? RequestContextHolder.get().getSubDomainName()
                    : bankPreferenceDTO.getTheme().getBankName())
            .platformName(RequestContextHolder.get().getPlatform())
            .logoLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getTheme().getLogo())
            .subDomain(RequestContextHolder.get().getSubDomainName())
            .onboardingPath(
                String.format(
                    miscPropertyConfig.getOnboardingPath(),
                    RequestContextHolder.get().getOrganizationId(),
                    customerUserDetailDTO.getEmail()))
            .supportMailLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportEmail())
            .helpUrl(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportUrl())
            .notificationRequestType(NotificationRequestType.SEND_INVITE_FOR_BACKOFFICE_EMAIL)
            .build(),
        mailPropertyConfig.getTemplate(SIGNATORY_INVITE));
  }

  public BvnValidationResponse validateBvn(CustomerBvnValidationDTO customerBvnValidationDTO) {
    // todo cache the cbaProvider
    String cbaProvider = RequestContextHolder.get().getCbaProvider();
    if (!securityPropertyConfig.isSkipBvnOtpValidation())
      otpService.effect(
          customerBvnValidationDTO.getBvn().concat(cbaProvider),
          OtpType.BVN_VERIFICATION,
          customerBvnValidationDTO.getOtp());

    BvnValidationResponse bvnValidationResponse =
        (BvnValidationResponse)
            redissonClient
                .getBucket(customerBvnValidationDTO.getBvn().concat(cbaProvider))
                .getAndDelete();
    if (bvnValidationResponse != null
        && bvnValidationResponse.getResponseStatus().equals(CbaProvider.ResponseStatus.COMPLETED)) {
      bvnValidationResponse.setValidationRef(
          otpService.store(
              customerBvnValidationDTO
                  .getBvn()
                  .concat(customerBvnValidationDTO.getOrganizationId()),
              OtpType.CUSTOMER_REGISTRATION));
    }
    return bvnValidationResponse;
  }

  public void updateInvite(CustomerSignatoryInviteRequestDTO customerSignatoryInviteRequestDTO) {
    otpService.effect(
        customerSignatoryInviteRequestDTO
            .getBvn()
            .concat(customerSignatoryInviteRequestDTO.getOrganizationId()),
        OtpType.CUSTOMER_REGISTRATION,
        customerSignatoryInviteRequestDTO.getValidationRef());
    customerSignatoryInvitationRegistrationService.createProfile(customerSignatoryInviteRequestDTO);
  }

  /**
   * Retrieves a CustomerUserDetailDTO from the customerActivationDTO matching the provided email.
   *
   * @param customerActivationDTO CustomerActivationDTO containing the list of customerUserDetailDto
   * @param email The email to search for
   * @return The matching CustomerUserDetailDTO or null if not found
   */
  public CustomerUserDetailDTO getCustomerUserDetailByEmail(
      CustomerActivationDTO customerActivationDTO, String email) {
    if (customerActivationDTO == null || customerActivationDTO.getCustomerUserDetailDTO() == null) {
      return null;
    }
    return customerActivationDTO.getCustomerUserDetailDTO().stream()
        .filter(userDetail -> email.equals(userDetail.getEmail()))
        .findFirst()
        .orElse(null);
  }
}
