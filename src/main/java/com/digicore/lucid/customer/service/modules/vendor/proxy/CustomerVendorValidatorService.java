/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.vendor.proxy;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.DUPLICATE;

import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.customer.data.modules.vendor.dto.CustomerVendorDTO;
import com.digicore.lucid.customer.data.modules.vendor.dto.VendorRequestDTO;
import com.digicore.lucid.customer.data.modules.vendor.service.DataAccessService;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> <PERSON>
 * @createdOn 12/03/2025
 */

@Service
@RequiredArgsConstructor
public class CustomerVendorValidatorService {
  private final DataAccessService<CustomerVendorDTO> customerVendorDataAccessService;
  private final CustomerVendorProxyService customerVendorProxyService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  public void createVendor(VendorRequestDTO createDTO) {
    if (customerVendorDataAccessService.exists(
        createDTO.getVendorId(), createDTO.getAccountNumber())) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getProfileMessage(DUPLICATE), HttpStatus.BAD_REQUEST);
    }
    customerVendorProxyService.createVendor(null, createDTO);
  }

  public void editVendor(VendorRequestDTO editDTO) {
    CustomerVendorDTO currentData = customerVendorDataAccessService.retrieve(editDTO.getVendorId());
    customerVendorProxyService.editVendor(currentData, editDTO);
  }

  public void removeVendor(String vendorId) {
    CustomerVendorDTO currentData = customerVendorDataAccessService.retrieve(vendorId);
    customerVendorProxyService.removeVendor(currentData, vendorId);
  }
}
