/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.authorization.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.ROLE;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.ROLE_CREATION_DTO;

import com.digicore.lucid.common.lib.authorization.dto.RoleCreationDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleWorkFlowDTO;
import com.digicore.lucid.common.lib.authorization.service.RoleService;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.lucid.customer.service.modules.authorization.proxy.CustomerUserRoleProxyService;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Mar-12(Wed)-2025
 */

@Service
@RequiredArgsConstructor
public class CustomerUserRoleOperations implements CustomerUserRoleProxyService {
  private final RoleService<RoleDTO, RoleCreationDTO> customerUserRoleService;

  public Object getAllRoles(int pageNumber, int pageSize, String paginated) {
    if ("false".equalsIgnoreCase(paginated)) return customerUserRoleService.retrieveAllRoles();
    return customerUserRoleService.retrieveAllRoles(pageNumber, pageSize);
  }

  public List<RoleWorkFlowDTO> getAllRoles() {
    return customerUserRoleService.retrieveRoles();
  }

  @MakerChecker(
      checkerPermission = "approve-create-customer-role",
      makerPermission = "create-customer-role",
      requestClassName = ROLE_CREATION_DTO,
      activity = CREATE,
      module = ROLE)
  public Object createRole(Object initialData, Object updateData, Object... files) {
    RoleCreationDTO roleDTO = (RoleCreationDTO) updateData;
    customerUserRoleService.createRole(roleDTO);
    return Optional.empty();
  }

  @MakerChecker(
      checkerPermission = "approve-delete-customer-role",
      makerPermission = "delete-customer-role",
      requestClassName = "java.lang.String",
      activity = DELETE,
      module = ROLE)
  public Object deleteRole(Object initialData, Object updateData, Object... files) {
    String roleName = (String) updateData;
    customerUserRoleService.deleteRole(roleName);
    return Optional.empty();
  }

  @MakerChecker(
      checkerPermission = "approve-edit-customer-role",
      makerPermission = "edit-customer-role",
      requestClassName = ROLE_CREATION_DTO,
      activity = EDIT,
      module = ROLE)
  public Object editRole(Object initialData, Object updateData, Object... files) {
    RoleCreationDTO roleDTO = (RoleCreationDTO) updateData;
    customerUserRoleService.editRole(roleDTO);
    return Optional.empty();
  }

  @MakerChecker(
      checkerPermission = "approve-disable-customer-role",
      makerPermission = "disable-customer-role",
      requestClassName = ROLE_CREATION_DTO,
      activity = DISABLE,
      module = ROLE)
  public Object disableRole(Object initialData, Object updateData, Object... files) {
    RoleCreationDTO roleDTO = (RoleCreationDTO) updateData;
    customerUserRoleService.disableRole(roleDTO.getName());
    return Optional.empty();
  }

  @MakerChecker(
      checkerPermission = "approve-enable-customer-role",
      makerPermission = "enable-customer-role",
      requestClassName = ROLE_CREATION_DTO,
      activity = ENABLE,
      module = ROLE)
  public Object enableRole(Object initialData, Object updateData, Object... files) {
    RoleCreationDTO roleDTO = (RoleCreationDTO) updateData;
    customerUserRoleService.enableRole(roleDTO.getName());
    return Optional.empty();
  }
}
