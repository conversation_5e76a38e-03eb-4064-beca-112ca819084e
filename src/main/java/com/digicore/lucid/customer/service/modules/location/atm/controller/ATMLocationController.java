/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.location.atm.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.RETRIEVE_ALL_API;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.RETRIEVE_API;
import static com.digicore.lucid.common.lib.swagger.constant.location.LocationSwaggerDocConstant.*;
import static com.digicore.registhentication.util.PageableUtil.PAGE_NUMBER;
import static com.digicore.registhentication.util.PageableUtil.PAGE_SIZE;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.customer.service.modules.location.atm.service.ATMLocationService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/*
 * <AUTHOR> Ilori
 * @createdOn 20/03/2025
 */

@RestController
@RequestMapping(API_V1 + ATM_LOCATION_API)
@RequiredArgsConstructor
@Tag(name = LOCATION_CONTROLLER_TITLE, description = LOCATION_CONTROLLER_DESCRIPTION)
public class ATMLocationController {
  private final ATMLocationService atmLocationService;

  @GetMapping(RETRIEVE_API)
  @PreAuthorize("hasAuthority('view-atm-location')")
  ResponseEntity<Object> retrieve(@RequestParam String terminalId) {
    return ControllerResponse.buildSuccessResponse(atmLocationService.retrieve(terminalId));
  }

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-atm-location')")
  ResponseEntity<Object> retrieveAll(
      @RequestParam(value = PAGE_NUMBER) int pageNumber,
      @RequestParam(value = PAGE_SIZE) int pageSize) {
    return ControllerResponse.buildSuccessResponse(
        atmLocationService.retrieve(pageNumber, pageSize));
  }
}
