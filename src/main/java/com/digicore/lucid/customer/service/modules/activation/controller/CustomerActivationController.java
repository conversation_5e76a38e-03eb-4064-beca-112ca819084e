/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.activation.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.PREFERENCE_API;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.RETRIEVE_API;
import static com.digicore.lucid.common.lib.swagger.constant.activation.ActivationSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.onboarding.OnboardingSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.onboarding.OnboardingSwaggerDocConstant.ONBOARDING_CONTROLLER_BVN_VALIDATION_DESCRIPTION;
import static com.digicore.lucid.common.lib.swagger.constant.profile.ProfileSwaggerDocConstant.PROFILE_CONTROLLER_VIEW_PREFERENCE_DESCRIPTION;
import static com.digicore.lucid.common.lib.swagger.constant.profile.ProfileSwaggerDocConstant.PROFILE_CONTROLLER_VIEW_PREFERENCE_TITLE;
import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;

import com.digicore.api.helper.response.ApiResponseJson;
import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.activation.dto.CustomerActivationDTO;
import com.digicore.lucid.common.lib.activation.dto.CustomerDocumentUploadDTO;
import com.digicore.lucid.common.lib.activation.dto.CustomerUserDetailDTO;
import com.digicore.lucid.common.lib.client.BackOfficeFeignClient;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerBvnValidationDTO;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerSignatoryInviteRequestDTO;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.service.modules.activation.service.CustomerActivationProcessor;
import com.digicore.lucid.customer.service.modules.registration.service.CustomerRegistrationService;
import com.digicore.registhentication.common.enums.Channel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-17(Mon)-2025
 */

@RestController
@RequestMapping(API_V1 + ACTIVATION_API)
@Tag(name = ACTIVATION_CONTROLLER_TITLE, description = ACTIVATION_CONTROLLER_DESCRIPTION)
@RequiredArgsConstructor
public class CustomerActivationController {
  private final CustomerActivationProcessor customerActivationProcessor;
  private final BackOfficeFeignClient backOfficeFeignClient;
  private final CustomerRegistrationService customerRegistrationService;

  @GetMapping(RETRIEVE_ACTIVATION_PROGRESS_API)
  @Operation(
      summary = ACTIVATION_CONTROLLER_RETRIEVE_PROGRESS_TITLE,
      description = ACTIVATION_CONTROLLER_RETRIEVE_PROGRESS_DESCRIPTION)
  public ResponseEntity<Object> retrieveProgress() {
    return ControllerResponse.buildSuccessResponse(
        customerActivationProcessor.retrieveProgress(
            RequestContextHolder.get().getOrganizationId()));
  }

  @GetMapping(PREFERENCE_API + RETRIEVE_API)
  @Operation(
      summary = PROFILE_CONTROLLER_VIEW_PREFERENCE_TITLE,
      description = PROFILE_CONTROLLER_VIEW_PREFERENCE_DESCRIPTION)
  public ResponseEntity<Object> retrievePreferences(HttpServletRequest httpServletRequest) {
    ClientUtil.setRequestContext(httpServletRequest);
    return ControllerResponse.buildSuccessResponse(
        customerActivationProcessor.retrieveBankPreferences(),
        "Bank preferences retrieved successfully.");
  }

  @GetMapping(RETRIEVE_ACTIVATION_CUSTOMER_API)
  @Operation(
      summary = ACTIVATION_CONTROLLER_RETRIEVE_PROGRESS_TITLE,
      description = ACTIVATION_CONTROLLER_RETRIEVE_PROGRESS_DESCRIPTION)
  public ResponseEntity<Object> retrieveSignatory(
      @RequestParam String email, @RequestParam String organizationId) {
    return ControllerResponse.buildSuccessResponse(
        customerActivationProcessor.getCustomerUserDetailByEmail(
            customerActivationProcessor.retrieveProgress(organizationId), email));
  }

  @PostMapping(SAVE_PROGRESS_API)
  @Operation(
      summary = ACTIVATION_CONTROLLER_SAVE_PROGRESS_TITLE,
      description = ACTIVATION_CONTROLLER_SAVE_PROGRESS_DESCRIPTION)
  public ResponseEntity<Object> saveProgress(
      @RequestBody CustomerActivationDTO customerActivationDTO) {
    customerActivationProcessor.saveProgress(customerActivationDTO);
    return ControllerResponse.buildSuccessResponse();
  }

  @PostMapping(INVITE_SIGNATORY_API)
  @Operation(
      summary = ACTIVATION_CONTROLLER_INVITE_SIGNATORY_TITLE,
      description = ACTIVATION_CONTROLLER_INVITE_SIGNATORY_DESCRIPTION)
  public ResponseEntity<Object> sendInvite(
      @RequestBody CustomerUserDetailDTO customerUserDetailDTO) {
    customerActivationProcessor.sendInviteMail(customerUserDetailDTO);
    return ControllerResponse.buildSuccessResponse();
  }

  @PostMapping(value = UPLOAD_DOCUMENT_API)
  @Operation(
      summary = ACTIVATION_CONTROLLER_UPLOAD_DOCUMENT_TITLE,
      description = ACTIVATION_CONTROLLER_UPLOAD_DOCUMENT_DESCRIPTION)
  public ResponseEntity<Object> uploadDocuments(@ModelAttribute CustomerDocumentUploadDTO request) {
    return ControllerResponse.buildSuccessResponse(
        customerActivationProcessor.uploadDocument(request));
  }

  @GetMapping(RETRIEVE_ACTIVIATION_COMMENT_API)
  @Operation(
      summary = ACTIVATION_CONTROLLER_RETRIEVE_COMMENT_TITLE,
      description = ACTIVATION_CONTROLLER_RETRIEVE_COMMENT_DESCRIPTION)
  public ResponseEntity<Object> retrieveComment() {
    return ControllerResponse.buildSuccessResponse(
        customerActivationProcessor.retrieveComments(
            RequestContextHolder.get().getOrganizationId()));
  }

  @PostMapping(SEND_OTP_TO_BVN_PHONE_NUMBER_API)
  @Operation(
      summary = ONBOARDING_CONTROLLER_BVN_SEND_OTP_BVN_TITLE,
      description = ONBOARDING_CONTROLLER_BVN_SEND_OTP_BVN_DESCRIPTION)
  public ResponseEntity<Object> sendOtpToBvn(
      @Valid @RequestBody CustomerBvnValidationDTO customerBvnValidationDTO,
      HttpServletRequest httpServletRequest) {
    setRequestContext(httpServletRequest);
    customerRegistrationService.sendOtpToBvn(customerBvnValidationDTO);
    return ControllerResponse.buildSuccessResponse(
        "Otp sent successfully to customer phone number");
  }

  @PostMapping(BVN_VALIDATION_API)
  @Operation(
      summary = ONBOARDING_CONTROLLER_BVN_VALIDATION_TITLE,
      description = ONBOARDING_CONTROLLER_BVN_VALIDATION_DESCRIPTION)
  public ResponseEntity<Object> validateBvn(
      @Valid @RequestBody CustomerBvnValidationDTO customerBvnValidationDTO,
      HttpServletRequest httpServletRequest) {
    setRequestContext(httpServletRequest);
    return ControllerResponse.buildSuccessResponse(
        customerActivationProcessor.validateBvn(customerBvnValidationDTO));
  }

  @PostMapping(SAVE_SIGNATORY_API)
  @Operation(
      summary = ONBOARDING_CONTROLLER_BVN_VALIDATION_TITLE,
      description = ONBOARDING_CONTROLLER_BVN_VALIDATION_DESCRIPTION)
  public ResponseEntity<Object> updateSignatory(
      @Valid @RequestBody CustomerSignatoryInviteRequestDTO customerSignatoryInviteRequestDTO,
      HttpServletRequest httpServletRequest) {
    setRequestContext(httpServletRequest);
    customerActivationProcessor.updateInvite(customerSignatoryInviteRequestDTO);
    return ControllerResponse.buildSuccessResponse();
  }

  private void setRequestContext(HttpServletRequest httpServletRequest) {
    ClientUtil.setRequestContext(httpServletRequest);
    RequestContextHolder.RequestContext requestContext = RequestContextHolder.get();
    requestContext.setChannel(Channel.WEB);
    ApiResponseJson<Object> backOfficeResponse =
        backOfficeFeignClient.getCbaProviderAndCbaToken(requestContext.getSubDomainName());

    assert backOfficeResponse != null;
    RequestContextHolder.RequestContext backOfficeRequestContext =
        getObjectMapper()
            .convertValue(backOfficeResponse.getData(), RequestContextHolder.RequestContext.class);
    requestContext.setCbaProvider(backOfficeRequestContext.getCbaProvider());
    requestContext.setCbaToken(backOfficeRequestContext.getCbaToken());
    requestContext.setBankOrganizationId(backOfficeRequestContext.getOrganizationId());
  }
}
