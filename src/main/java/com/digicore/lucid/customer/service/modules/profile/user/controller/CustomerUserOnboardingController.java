/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.profile.user.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.USER_API;
import static com.digicore.lucid.common.lib.swagger.constant.onboarding.OnboardingSwaggerDocConstant.*;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.registration.dto.UserInvitationDTO;
import com.digicore.lucid.customer.service.modules.profile.user.proxy.CustomerUserValidatorService;
import com.digicore.lucid.customer.service.modules.profile.user.service.CustomerUserOnboardingService;
import com.digicore.registhentication.authentication.dtos.request.ResetPasswordSecondBaseRequestDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.security.Principal;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/*
 * <AUTHOR> John
 * @createdOn Mar-11(Tue)-2025
 */

@RestController
@RequestMapping(API_V1 + USER_API + ONBOARDING_API)
@Tag(name = ONBOARDING_CONTROLLER_TITLE, description = ONBOARDING_CONTROLLER_DESCRIPTION)
@RequiredArgsConstructor
public class CustomerUserOnboardingController {
  private final CustomerUserOnboardingService customerUserOnboardingService;
  private final CustomerUserValidatorService customerUserValidatorService;

  @PostMapping(INVITE_API)
  @PreAuthorize("hasAuthority('invite-customer-user')")
  @Operation(
      summary = ONBOARDING_CONTROLLER_INVITE_USER_TITLE,
      description = ONBOARDING_CONTROLLER_INVITE_USER_DESCRIPTION)
  public ResponseEntity<Object> inviteUser(
      @Valid @RequestBody UserInvitationDTO userInvitationDTO) {
    return ControllerResponse.buildSuccessResponse(
        customerUserValidatorService.onboardUser(userInvitationDTO),
        "invitation would be sent to ".concat(userInvitationDTO.getEmail()));
  }

  @PostMapping("password-update")
  @Operation(
      summary = ONBOARDING_CONTROLLER_RESET_DEFAULT_PASSWORD_TITLE,
      description = ONBOARDING_CONTROLLER_RESET_DEFAULT_PASSWORD_DESCRIPTION)
  public ResponseEntity<Object> updateDefaultPassword(
      @Valid @RequestBody ResetPasswordSecondBaseRequestDTO resetPasswordFirstBaseRequestDTO,
      Principal principal) {
    customerUserOnboardingService.updateDefaultPassword(
        resetPasswordFirstBaseRequestDTO, principal);
    return ControllerResponse.buildSuccessResponse();
  }
}
