/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.onboarding.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.DUPLICATE;
import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;
import static com.digicore.lucid.common.lib.constant.message.MessagePlaceHolderConstant.EMAIL;
import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;
import static com.digicore.lucid.integration.lib.modules.service.account.request.AccountServiceType.FETCH_ACCOUNT_DETAIL;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.api.helper.response.ApiError;
import com.digicore.api.helper.response.ApiResponseJson;
import com.digicore.lucid.common.lib.account.dto.CustomerAccountDTO;
import com.digicore.lucid.common.lib.account.service.AccountService;
import com.digicore.lucid.common.lib.client.CbaFeignClient;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.profile.dto.CustomerProfileDTO;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.profile.service.OrganizationProfileService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerOnboardingDTO;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.util.ImageBase64Util;
import com.digicore.lucid.customer.data.config.SecurityQuestionsProperties;
import com.digicore.lucid.customer.data.config.ValidationConfigProperties;
import com.digicore.lucid.customer.data.modules.onboarding.dto.CustomerInitiateOnboardingFlowDTO;
import com.digicore.lucid.customer.data.modules.onboarding.dto.CustomerOnboardingFlowDTO;
import com.digicore.lucid.customer.data.modules.onboarding.model.CustomerFlow;
import com.digicore.lucid.customer.data.modules.onboarding.repository.CustomerFlowRepository;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationAction;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationService;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationServiceRegistry;
import com.digicore.lucid.customer.data.modules.onboarding.validation.ValidationType;
import com.digicore.lucid.customer.data.modules.onboarding.validation.util.ValidationUtil;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.account.request.OrganizationFetchDetailRequest;
import com.digicore.lucid.integration.lib.modules.service.account.response.OrganizationFetchDetailResponse;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.services.RegistrationService;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Ucheagwu
 * @createdOn Jun-10(Tue)-2025
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerExistingAccountOnboardingService {

  private final ValidationServiceRegistry validationServiceRegistry;
  private final ValidationConfigProperties validationConfigProperties;
  private final CustomerFlowRepository customerFlowRepository;
  private final AccountService<CustomerAccountDTO> customerAccountService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final CbaFeignClient cbaFeignClient;
  private final RegistrationService<List<UserProfileDTO>, CustomerOnboardingDTO>
      existingCustomerUserOnboardingService;
  private final OrganizationProfileService<CustomerProfileDTO> customerProfileService;
  private final SecurityQuestionsProperties securityQuestionsProperties;

  private void prerequisiteCheck(CustomerOnboardingFlowDTO customerOnboardingFlowDTO) {
    //        check that email and account number is not blank
    if (ClientUtil.nullOrEmpty(customerOnboardingFlowDTO.getEmail())
        && ClientUtil.nullOrEmpty(customerOnboardingFlowDTO.getAccountNumber())) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Account number or Email not provided")));
    }

    //        confirm the email doesn't exist in that subdomain
    if (existingCustomerUserOnboardingService.profileExistenceCheckByEmail(
        customerOnboardingFlowDTO.getEmail(), RequestContextHolder.get().getBankOrganizationId()))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig
              .getUserMessage(DUPLICATE)
              .replace(EMAIL, customerOnboardingFlowDTO.getEmail()),
          HttpStatus.BAD_REQUEST);

    // Check account number exists
    if (customerAccountService.verifyAccountNumberExists(
        customerOnboardingFlowDTO.getAccountNumber(),
        RequestContextHolder.get().getBankOrganizationId()))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getAccountMessage(DUPLICATE), HttpStatus.BAD_REQUEST);

    //    check email && bankorganizationId
    if (customerProfileService.emailProfileExists(
        customerOnboardingFlowDTO.getEmail(), RequestContextHolder.get().getBankOrganizationId())) {
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig
              .getUserMessage(DUPLICATE)
              .replace(EMAIL, customerOnboardingFlowDTO.getEmail()),
          HttpStatus.BAD_REQUEST);
    }

    // call CBA Service get email and phone number if exists
    String provider = RequestContextHolder.get().getCbaProvider();
    OrganizationFetchDetailRequest cbaRequest =
        OrganizationFetchDetailRequest.builder()
            .accountNumber(customerOnboardingFlowDTO.getAccountNumber())
            .fetchAllAccounts(false)
            .build();
    ApiResponseJson<Object> response;
    try {
      response = cbaFeignClient.processAccountRequest(provider, FETCH_ACCOUNT_DETAIL, cbaRequest);
    } catch (Exception e) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    }
    Object responseBody = response.getData();
    OrganizationFetchDetailResponse accountFromCBA =
        getObjectMapper().convertValue(responseBody, OrganizationFetchDetailResponse.class);

    if (!accountFromCBA.getResponseStatus().equals(CbaProvider.ResponseStatus.COMPLETED)) {
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    }
    if (ClientUtil.nullOrEmpty(accountFromCBA.getBusinessPhoneNo())
        && ClientUtil.nullOrEmpty(accountFromCBA.getPhoneNo())) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Phone not provided")));
    }

    if (customerProfileService.phoneProfileExists(
        customerOnboardingFlowDTO.getPhoneNumber(),
        RequestContextHolder.get().getBankOrganizationId())) {
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig
              .getUserMessage(DUPLICATE)
              .replace(EMAIL, customerOnboardingFlowDTO.getPhoneNumber()),
          HttpStatus.BAD_REQUEST);
    }
    if (!ClientUtil.nullOrEmpty(accountFromCBA.getName())) {
      customerOnboardingFlowDTO.setFirstName(accountFromCBA.getName());
    }

    // if email or phone number does not exist from CBA throw error
    if (ClientUtil.nullOrEmpty(accountFromCBA.getBusinessPhoneNo())
        && ClientUtil.nullOrEmpty(accountFromCBA.getEmail())) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("CBA email or phone not provided")));
    }

    String phoneNumber =
        ClientUtil.nullOrEmpty(accountFromCBA.getPhoneNo())
            ? accountFromCBA.getBusinessPhoneNo()
            : accountFromCBA.getPhoneNo();
    String firstName =
        ClientUtil.nullOrEmpty(accountFromCBA.getName())
            ? customerOnboardingFlowDTO.getEmail()
            : accountFromCBA.getName();

    if (StringUtils.isBlank(accountFromCBA.getCustomerId())) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST,
          List.of(new ApiError("Cannot retrieve CBA customer id from account number")));
    }

    customerOnboardingFlowDTO.setPhoneNumber(phoneNumber);
    customerOnboardingFlowDTO.setFirstName(firstName);
    customerOnboardingFlowDTO.setCbaCustomerId(accountFromCBA.getCustomerId());
  }

  public Object initiateOnboardingFlow(
      CustomerInitiateOnboardingFlowDTO customerInitiateOnboardingFlowDTO) {
    CustomerOnboardingFlowDTO customerOnboardingFlowDTO = new CustomerOnboardingFlowDTO();
    BeanUtilWrapper.copyNonNullProperties(
        customerInitiateOnboardingFlowDTO, customerOnboardingFlowDTO);
    customerOnboardingFlowDTO.setSessionId(null);
    prerequisiteCheck(customerOnboardingFlowDTO);
    Optional<CustomerFlow> optionalCustomerFlow =
        customerFlowRepository
            .findFirstByBankOrganizationIdAndAccountNumberAndEmailAndIsDeletedFalseAndCreatedDateAfterOrderByCreatedDateDesc(
                RequestContextHolder.get().getBankOrganizationId(),
                customerOnboardingFlowDTO.getAccountNumber(),
                customerOnboardingFlowDTO.getEmail(),
                LocalDateTime.now().minusMinutes(10));
    if (optionalCustomerFlow.isPresent()) {
      CustomerFlow customerFlow = optionalCustomerFlow.get();
      ValidationUtil.ensureOnboardingNotCompleted(customerFlow);
      Object previousFlow = ValidationUtil.returnPreviousFlow(customerFlowRepository, customerFlow);
      if (previousFlow == null) {
        return initiateFlow(customerOnboardingFlowDTO);
      } else {
        return previousFlow;
      }
    }
    return initiateFlow(customerOnboardingFlowDTO);
  }

  private Object initiateFlow(CustomerOnboardingFlowDTO customerOnboardingFlowDTO) {
    ValidationConfigProperties.ValidationConfig validationConfig = getValidationConfig();
    if (validationConfig.getSubDomain().equalsIgnoreCase("coronation")) {
      String cbaCustomerId = customerOnboardingFlowDTO.getCbaCustomerId();
      if (cbaCustomerId.startsWith("C")) {
        throw new ZeusRuntimeException(
            HttpStatus.BAD_REQUEST, List.of(new ApiError("Cannot onboard corporate account")));
      }
    }

    List<String> validationTypes = getValidationType(validationConfig);
    String strValidations = String.join(",", validationTypes);
    if (strValidations.endsWith(",")) {
      strValidations = strValidations.substring(0, strValidations.length() - 1);
    }
    ValidationService validationService =
        validationServiceRegistry.getService(validationTypes.getFirst());
    return validationService.process(
        customerOnboardingFlowDTO,
        RequestContextHolder.get().getSubDomainName(),
        ValidationAction.INITIATE.getAction(),
        strValidations);
  }

  @Transactional
  public Object completeOnboardingFlow(CustomerOnboardingFlowDTO customerOnboardingFlowDTO) {
    try {
      List<String> validationOperation = getValidationOperationType(customerOnboardingFlowDTO);
      String validationType = validationOperation.get(0);
      String action = validationOperation.get(1);
      String validationTypes = validationOperation.get(2);
      ValidationService validationService = validationServiceRegistry.getService(validationType);

      Object result = validationService.process(
          customerOnboardingFlowDTO,
          RequestContextHolder.get().getSubDomainName(),
          action,
          validationTypes);

      // Ensure transaction is properly committed before returning
      return result;
    } catch (Exception e) {
      // Log the exception for debugging
      log.error("Error in completeOnboardingFlow for sessionId: {}, error: {}",
          customerOnboardingFlowDTO.getSessionId(), e.getMessage(), e);
      throw e; // Re-throw to trigger transaction rollback
    }
  }

  private ValidationConfigProperties.ValidationConfig getValidationConfig() {
    return validationConfigProperties.getConfigBySubDomain(
        RequestContextHolder.get().getSubDomainName());
  }

  private List<String> getValidationType(
      ValidationConfigProperties.ValidationConfig validationConfig) {
    // Check validation type exists for subdomain
    List<ValidationConfigProperties.ValidationType> validationTypes =
        validationConfig.getValidationType();
    if (validationTypes.isEmpty()) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST,
          List.of(
              new ApiError(
                  "Validation type not found for subdomain: "
                      + RequestContextHolder.get().getSubDomainName())));
    }

    // Check that the validation type contains MOBILE_OTP or EMAIL_OTP
    Optional<String> requiredType =
        validationTypes.stream()
            .map(ValidationConfigProperties.ValidationType::getType)
            .filter(t -> t.equals(ValidationType.MOBILE_OTP.getType()))
            .findAny();

    if (requiredType.isEmpty()) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST,
          List.of(
              new ApiError(
                  "Mobile validation type not found for subdomain: "
                      + RequestContextHolder.get().getSubDomainName())));
    }

    // Check that the first validation type is either MOBILE_OTP or EMAIL_OTP
    if (!validationTypes.getFirst().getType().equals(ValidationType.MOBILE_OTP.getType())
        && !validationTypes.getFirst().getType().equals(ValidationType.EMAIL_OTP.getType())) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST,
          List.of(
              new ApiError(
                  "Email or Mobile validation type must be the first type for subdomain: "
                      + RequestContextHolder.get().getSubDomainName())));
    }

    // Check that the action contains INITIATE and VALIDATE
    validationTypes.stream()
        .filter(
            v ->
                v.getType().equals(ValidationType.MOBILE_OTP.getType())
                    || v.getType().equals(ValidationType.EMAIL_OTP.getType()))
        .map(ValidationConfigProperties.ValidationType::getAction)
        .forEach(
            a -> {
              if (!a.contains(ValidationAction.INITIATE.getAction())
                  && !a.contains(ValidationAction.VALIDATE.getAction())) {
                throw new ZeusRuntimeException(
                    HttpStatus.BAD_REQUEST,
                    List.of(new ApiError("Invalid action for validation type: " + a)));
              }
            });

    return validationTypes.stream()
        .map(ValidationConfigProperties.ValidationType::getType)
        .toList();
  }

  private List<String> getValidationOperationType(
      CustomerOnboardingFlowDTO customerOnboardingFlowDTO) {
    // Check session id is not null
    if (StringUtils.isBlank(customerOnboardingFlowDTO.getSessionId())) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Session id is required")));
    }

    // Check that the session id exists
    CustomerFlow customerFlow =
        customerFlowRepository
            .findBySessionIdAndIsDeletedFalse(customerOnboardingFlowDTO.getSessionId())
            .orElseThrow(
                () ->
                    new ZeusRuntimeException(
                        HttpStatus.BAD_REQUEST,
                        List.of(new ApiError("Session id does not exist"))));

    ValidationUtil.ensureOnboardingNotCompleted(customerFlow);

    String nextStep = customerFlow.getNextStep();
    String[] nextStepOperation = nextStep.split("\\.");

    // Get the validation type and action e.g. //nextStep=MOBILE_OTP.VALIDATE or
    // SECURITY_QUESTION.INITIATE ...
    if (nextStepOperation.length != 2) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Invalid next step: " + nextStep)));
    }

    String validationType = nextStepOperation[0];
    String action = nextStepOperation[1];
    String validationTypes = customerFlow.getValidations();
    return List.of(validationType, action, validationTypes);
  }

  public Object retrievePhishingImages() throws IOException {
    ValidationConfigProperties.ValidationConfig validationConfig =
        validationConfigProperties.getConfigBySubDomain(
            RequestContextHolder.get().getSubDomainName());
    String phishingImagePath =
        validationConfig.getValidationType().stream()
            .filter(t -> t.getType().equalsIgnoreCase(ValidationType.PHISHING_IMAGE.getType()))
            .map(ValidationConfigProperties.ValidationType::getSourcePath)
            .findFirst()
            .orElse(null);

    if (phishingImagePath == null) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Unable to retrieve images at this time")));
    }

    return ImageBase64Util.getAllImagesAsBase64(phishingImagePath);
  }

  public List<String> retrieveSecurityQuestions() {
    return securityQuestionsProperties.getAllQuestions();
  }
}
