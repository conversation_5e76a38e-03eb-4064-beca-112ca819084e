/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.authentication.controller.coronation;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.constant.system.SystemConstant.APPLICATION_ID;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.PREFERENCE_API;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.RETRIEVE_API;
import static com.digicore.lucid.common.lib.swagger.constant.authenetication.AuthenticationSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.profile.ProfileSwaggerDocConstant.PROFILE_CONTROLLER_VIEW_PREFERENCE_DESCRIPTION;
import static com.digicore.lucid.common.lib.swagger.constant.profile.ProfileSwaggerDocConstant.PROFILE_CONTROLLER_VIEW_PREFERENCE_TITLE;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.api.helper.response.ApiError;
import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.profile.dto.CustomerProfileDTO;
import com.digicore.lucid.common.lib.profile.service.OrganizationProfileService;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.customer.data.modules.device.dto.DeviceRequestDTO;
import com.digicore.lucid.customer.service.modules.authentication.dto.LoginApiRequest;
import com.digicore.lucid.customer.service.modules.authentication.service.coronation.CoronationUserAuthenticationService;
import com.digicore.registhentication.authentication.dtos.request.ResetPasswordFirstBaseRequestDTO;
import com.digicore.registhentication.authentication.dtos.request.ResetPasswordSecondBaseRequestDTO;
import com.digicore.registhentication.common.enums.Channel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-12(Wed)-2025
 */

@RestController
@RequestMapping(API_V1 + AUTHENTICATION_API)
@RequiredArgsConstructor
@ConditionalOnProperty(name = "custom-login.coronation.enabled", havingValue = "true")
@Tag(name = AUTHENTICATION_CONTROLLER_TITLE, description = AUTHENTICATION_CONTROLLER_DESCRIPTION)
public class CoronationCustomerUserAuthenticationController {
  private final CoronationUserAuthenticationService coronationUserAuthenticationService;
  private final OrganizationProfileService<CustomerProfileDTO> customerProfileService;
  private final RedissonClient redissonClient;

  @PostMapping(LOGIN_API)
  @Operation(
      summary = AUTHENTICATION_CONTROLLER_LOGIN_TITLE,
      description = AUTHENTICATION_CONTROLLER_LOGIN_DESCRIPTION)
  public ResponseEntity<Object> login(
      @RequestBody LoginApiRequest loginApiRequest, HttpServletRequest httpServletRequest) {
    if (StringUtils.isBlank(loginApiRequest.getPhishingImage())) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, List.of(new ApiError("Phishing image is required")));
    }
    setRequestContext(httpServletRequest, loginApiRequest.getUsername());
    return ControllerResponse.buildSuccessResponse(
        coronationUserAuthenticationService.authenticateCustomerUser(
            loginApiRequest, httpServletRequest),
        "Authentication Successful");
  }

  @GetMapping(PREFERENCE_API + RETRIEVE_API)
  @Operation(
      summary = PROFILE_CONTROLLER_VIEW_PREFERENCE_TITLE,
      description = PROFILE_CONTROLLER_VIEW_PREFERENCE_DESCRIPTION)
  public ResponseEntity<Object> retrievePreferences(HttpServletRequest httpServletRequest) {
    setRequestContext(httpServletRequest);
    return ControllerResponse.buildSuccessResponse(
        coronationUserAuthenticationService.retrieveLoginPreferences(),
        "Login preferences retrieved successfully.");
  }

  @PostMapping(FORGOT_PASSWORD_INIT_API)
  @Operation(
      summary = FORGOT_PASSWORD_INIT_CONTROLLER_TITLE,
      description = FORGOT_PASSWORD_INIT_CONTROLLER_DESCRIPTION)
  public ResponseEntity<Object> forgotPassword(
      @RequestParam String username, HttpServletRequest httpServletRequest) {
    setRequestContext(httpServletRequest, username);
    coronationUserAuthenticationService.initResetPassword(username);
    return ControllerResponse.buildSuccessResponse(
        "Success, a passcode will be sent to your email.");
  }

  //  @PostMapping(FORGOT_PASSWORD_PHONE_OTP_VALIDATE_API)
  //  @Operation(
  //      summary = FORGOT_PASSWORD_PHONE_OTP_VALIDATE_CONTROLLER_TITLE,
  //      description = FORGOT_PASSWORD_PHONE_OTP_VALIDATE_CONTROLLER_DESCRIPTION)
  //  public ResponseEntity<Object> validatePhoneNumberOTP(
  //      @RequestBody ResetPasswordFirstBaseRequestDTO resetPasswordFirstBaseRequestDTO,
  //      HttpServletRequest httpServletRequest) {
  //    setRequestContext(httpServletRequest, resetPasswordFirstBaseRequestDTO.getEmail());
  //    userAuthenticationService.validatePhoneNumber(resetPasswordFirstBaseRequestDTO);
  //    return ControllerResponse.buildSuccessResponse(
  //        "Success, a passcode will be sent to your email.");
  //  }

  @PostMapping(FORGOT_PASSWORD_EMAIL_PASSCODE_VALIDATE_API)
  @Operation(
      summary = FORGOT_PASSWORD_EMAIL_PASSCODE_VALIDATE_CONTROLLER_TITLE,
      description = FORGOT_PASSWORD_EMAIL_PASSCODE_VALIDATE_CONTROLLER_DESCRIPTION)
  public ResponseEntity<Object> validateEmailPassCode(
      @RequestBody ResetPasswordFirstBaseRequestDTO resetPasswordFirstBaseRequestDTO,
      HttpServletRequest httpServletRequest) {
    setRequestContext(httpServletRequest, resetPasswordFirstBaseRequestDTO.getEmail());
    return ControllerResponse.buildSuccessResponse(
        coronationUserAuthenticationService.validateEmail(resetPasswordFirstBaseRequestDTO));
  }

  @PostMapping(FORGOT_PASSWORD_COMPLETE_API)
  @Operation(
      summary = FORGOT_PASSWORD_COMPLETE_CONTROLLER_TITLE,
      description = FORGOT_PASSWORD_COMPLETE_CONTROLLER_DESCRIPTION)
  public ResponseEntity<Object> completeResetPassword(
      @RequestBody ResetPasswordSecondBaseRequestDTO resetPasswordSecondBaseRequestDTO,
      HttpServletRequest httpServletRequest) {
    setRequestContext(httpServletRequest, resetPasswordSecondBaseRequestDTO.getEmail());
    coronationUserAuthenticationService.completePasswordReset(resetPasswordSecondBaseRequestDTO);
    return ControllerResponse.buildSuccessResponse("Success, password has been reset.");
  }

  @PostMapping(FORGOT_PASSWORD_RESEND_CODE_API)
  @Operation(
      summary = FORGOT_PASSWORD_RESEND_CODE_CONTROLLER_TITLE,
      description = FORGOT_PASSWORD_RESEND_CODE_CONTROLLER_DESCRIPTION)
  public ResponseEntity<Object> resendPasswordResetCode(
      @RequestParam String username,
      //      @RequestParam boolean forEmail,
      HttpServletRequest httpServletRequest) {
    setRequestContext(httpServletRequest, username);
    coronationUserAuthenticationService.resendCodes(username);
    return ControllerResponse.buildSuccessResponse("Success, a new OTP will be sent.");
  }

  @GetMapping(DEVICE_REGISTRATION_OTP_API)
  @Operation(
      summary = DEVICE_REGISTRATION_CONTROLLER_TITLE,
      description = DEVICE_REGISTRATION_CONTROLLER_DESCRIPTION)
  public ResponseEntity<Object> deviceRegistrationOtp(
      @RequestParam String username, HttpServletRequest httpServletRequest) {
    setRequestContext(httpServletRequest, username);
    coronationUserAuthenticationService.deviceOtp(username);
    return ControllerResponse.buildSuccessResponse("Success, an OTP will be sent.");
  }

  @GetMapping(DEVICE_REGISTRATION_VALIDATION_API)
  @Operation(
      summary = DEVICE_REGISTRATION_CONTROLLER_TITLE,
      description = DEVICE_REGISTRATION_CONTROLLER_DESCRIPTION)
  public ResponseEntity<Object> deviceRegistrationOtpValidation(
      @RequestParam String username,
      @RequestParam String otp,
      HttpServletRequest httpServletRequest) {
    setRequestContext(httpServletRequest, username);
    HashMap<String, Object> content = new HashMap<>();
    content.put("code", coronationUserAuthenticationService.deviceOtpVerification(username, otp));
    return ControllerResponse.buildSuccessResponse(content);
  }

  @PostMapping(DEVICE_REGISTRATION_API)
  @Operation(
      summary = DEVICE_REGISTRATION_CONTROLLER_TITLE,
      description = DEVICE_REGISTRATION_CONTROLLER_DESCRIPTION)
  public ResponseEntity<Object> deviceRegistration(
      @RequestParam String username,
      @RequestParam String code,
      @Valid @RequestBody DeviceRequestDTO deviceRequestDTO,
      HttpServletRequest httpServletRequest) {
    setRequestContext(httpServletRequest);
    coronationUserAuthenticationService.deviceRegistration(username, code, deviceRequestDTO);
    return ControllerResponse.buildSuccessResponse("Success, device has been registered.");
  }

  private void setRequestContext(HttpServletRequest httpServletRequest) {
    ClientUtil.setRequestContext(httpServletRequest);
    RequestContextHolder.RequestContext requestContext =
        RequestContextHolder.get() != null
            ? RequestContextHolder.get()
            : new RequestContextHolder.RequestContext();
    requestContext.setApplicationId(httpServletRequest.getHeader(APPLICATION_ID));
    requestContext.setChannel(
        !ClientUtil.nullOrEmpty(httpServletRequest.getHeader(APPLICATION_ID))
            ? Channel.MOBILE
            : Channel.WEB);
    //    requestContext.setSubDomainName(
    //        !ClientUtil.nullOrEmpty(httpServletRequest.getHeader(APPLICATION_ID))
    //            ? (String)
    //                redissonClient
    //                    .getBucket(requestContext.getApplicationId().concat("-SUB_DOMAIN"))
    //                    .get()
    //            : requestContext.getSubDomainName());
    //    requestContext.setPlatform(CORPORATE);
    RequestContextHolder.set(requestContext);
  }

  private void setRequestContext(HttpServletRequest httpServletRequest, String username) {
    ClientUtil.setRequestContext(httpServletRequest);
    RequestContextHolder.RequestContext requestContext =
        RequestContextHolder.get() != null
            ? RequestContextHolder.get()
            : new RequestContextHolder.RequestContext();
    requestContext.setApplicationId(httpServletRequest.getHeader(APPLICATION_ID));
    requestContext.setChannel(
        !ClientUtil.nullOrEmpty(httpServletRequest.getHeader(APPLICATION_ID))
            ? Channel.MOBILE
            : Channel.WEB);
    //    requestContext.setSubDomainName(
    //        !ClientUtil.nullOrEmpty(httpServletRequest.getHeader(APPLICATION_ID))
    //            ? (String)
    //                redissonClient
    //                    .getBucket(requestContext.getApplicationId().concat("-SUB_DOMAIN"))
    //                    .get()
    //            : requestContext.getSubDomainName());
    //    requestContext.setPlatform(CORPORATE);
    RequestContextHolder.set(requestContext);
    customerProfileService.setOrganizationIdAndCbaTokenAndProvider(requestContext, username);
    RequestContextHolder.set(requestContext);
  }
}
