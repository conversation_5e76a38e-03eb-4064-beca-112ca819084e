/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.limit.service;

import com.digicore.lucid.common.lib.limit.dto.CustomerLimitConfigDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.limit.service.LimitConfigService;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.validator.enums.Currency;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Mar-10(Mon)-2025
 */

@Service
@RequiredArgsConstructor
public class CustomerLimitService {
  private final LimitConfigService<CustomerLimitConfigDTO> customerLimitConfigService;

  public PaginatedResponseDTO<CustomerLimitConfigDTO> retrieveCustomerLimitConfig(
      int pageNumber, int pageSize) {
    return customerLimitConfigService.retrieveLimitConfig(pageNumber, pageSize);
  }

  public CustomerLimitConfigDTO retrieveCustomerLimitConfig(
      String accountNumber, LimitType limitType, Currency currency) {
    return customerLimitConfigService.retrieveLimitConfig(accountNumber, limitType, currency);
  }
}
