/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.beneficiary.proxy;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.DUPLICATE;

import com.digicore.lucid.common.lib.beneficiary.dto.BeneficiaryCreateDTO;
import com.digicore.lucid.common.lib.beneficiary.dto.BeneficiaryEditDTO;
import com.digicore.lucid.common.lib.beneficiary.dto.CustomerBeneficiaryDTO;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.customer.data.modules.beneficiary.service.DataAccessService;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn 12/03/2025
 */

@Service
@RequiredArgsConstructor
public class CustomerBeneficiaryValidatorService {
  private final DataAccessService<CustomerBeneficiaryDTO> customerEmployeeDataAccessService;
  private final CustomerBeneficiaryProxyService customerBeneficiaryProxyService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  public void createBeneficiary(BeneficiaryCreateDTO createDTO) {
    if (customerEmployeeDataAccessService.exists(createDTO.getAccountNumber())) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getProfileMessage(DUPLICATE), HttpStatus.BAD_REQUEST);
    }
    customerBeneficiaryProxyService.createBeneficiary(null, createDTO);
  }

  public void editBeneficiary(BeneficiaryEditDTO editDTO) {
    CustomerBeneficiaryDTO currentData =
        customerEmployeeDataAccessService.retrieve(editDTO.getBeneficiaryId());
    customerBeneficiaryProxyService.editBeneficiary(currentData, editDTO);
  }

  public void removeBeneficiary(String beneficiaryId) {
    CustomerBeneficiaryDTO currentData = customerEmployeeDataAccessService.retrieve(beneficiaryId);
    customerBeneficiaryProxyService.removeBeneficiary(currentData, beneficiaryId);
  }
}
