/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.account.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;
import static com.digicore.lucid.common.lib.util.MoneyUtil.convertToFormattedMajor;
import static com.digicore.lucid.common.lib.util.MoneyUtil.convertToMinor;

import com.digicore.api.helper.response.ApiResponseJson;
import com.digicore.lucid.common.lib.account.dto.CustomerAccountDTO;
import com.digicore.lucid.common.lib.account.service.AccountService;
import com.digicore.lucid.common.lib.client.CbaFeignClient;
import com.digicore.lucid.common.lib.export.dto.CsvDto;
import com.digicore.lucid.common.lib.export.dto.TransactionPdfExportDTO;
import com.digicore.lucid.common.lib.export.enums.ExportFileType;
import com.digicore.lucid.common.lib.export.service.CsvExportService;
import com.digicore.lucid.common.lib.export.util.PdfUtil;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.notification.config.MailPropertyConfig;
import com.digicore.lucid.common.lib.notification.config.TemplatePropertyConfig;
import com.digicore.lucid.common.lib.notification.request.NotificationRequestFileDTO;
import com.digicore.lucid.common.lib.notification.request.NotificationRequestType;
import com.digicore.lucid.common.lib.notification.request.NotificationServiceRequest;
import com.digicore.lucid.common.lib.notification.service.NotificationDispatcher;
import com.digicore.lucid.common.lib.profile.dto.BankPreferenceDTO;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.profile.service.UserProfileService;
import com.digicore.lucid.common.lib.properties.FeaturePropertyConfig;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.util.MoneyUtil;
import com.digicore.lucid.customer.service.modules.account.dto.CustomerAccountApiResponse;
import com.digicore.lucid.customer.service.modules.account.dto.CustomerAccountReceiptRequest;
import com.digicore.lucid.customer.service.modules.account.dto.CustomerTransactionsApiResponse;
import com.digicore.lucid.integration.lib.modules.service.CbaProvider;
import com.digicore.lucid.integration.lib.modules.service.account.request.AccountServiceType;
import com.digicore.lucid.integration.lib.modules.service.account.request.OrganizationFetchAccountTransactionsRequest;
import com.digicore.lucid.integration.lib.modules.service.account.request.OrganizationFetchDetailRequest;
import com.digicore.lucid.integration.lib.modules.service.account.response.*;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.util.IDGeneratorUtil;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonClient;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ilori
 * @createdOn 24/02/2025
 */

@Service
@RequiredArgsConstructor
public class CustomerAccountOperations {
  private final AccountService<CustomerAccountDTO> customerAccountService;
  private final CbaFeignClient cbaFeignClient;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final TemplatePropertyConfig templatePropertyConfig;
  private final MailPropertyConfig mailPropertyConfig;
  private final CsvExportService csvExportService;
  private final PdfUtil pdfUtil;
  private final RedissonClient redissonClient;
  private final FeaturePropertyConfig featurePropertyConfig;
  private final NotificationDispatcher notificationDispatcher;
  private final UserProfileService<UserProfileDTO> customerUserProfileService;

  public List<CustomerAccountApiResponse> fetchAccounts() {
    List<CustomerAccountDTO> dbAccountDTOs =
        customerAccountService.fetchActiveViewableAccounts(
            RequestContextHolder.get().getOrganizationId());
    Map<String, CustomerAccountDTO> dbAccountMap =
        dbAccountDTOs.stream()
            .collect(Collectors.toMap(CustomerAccountDTO::getAccountNumber, account -> account));

    if (dbAccountDTOs.isEmpty()) {
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    }

    String cbaOrganizationId = dbAccountDTOs.getFirst().getCbaOrganizationId();

    OrganizationFetchDetailRequest cbaRequest =
        OrganizationFetchDetailRequest.builder().customerId(cbaOrganizationId).build();
    ApiResponseJson<Object> response =
        cbaFeignClient.processAccountRequest(AccountServiceType.FETCH_ALL_ACCOUNTS, cbaRequest);
    Object responseBody = response.getData();
    OrganizationFetchAccountsResponse accountsFromCBA =
        getObjectMapper().convertValue(responseBody, OrganizationFetchAccountsResponse.class);

    return accountsFromCBA.getAccounts().stream()
        .filter(cbaResult -> dbAccountMap.containsKey(cbaResult.getAccountNumber()))
        .map(
            account ->
                new CustomerAccountApiResponse(
                    account.getAccountNumber(),
                    account.getAccountName(),
                    account.getCurrency(),
                    account.getAccountType(),
                    convertToMinor(account.getWithdrawableAmount()),
                    convertToMinor(account.getAvailableBalance()),
                    convertToMinor(account.getLedgerBalance())))
        .toList();
  }

  public CustomerAccountApiResponse fetchAccountInflow(String accountNumber) {
    if (!customerAccountService.verifyAccountNumber(
        accountNumber, RequestContextHolder.get().getOrganizationId())) {
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    }

    OrganizationFetchDetailRequest cbaRequest =
        OrganizationFetchDetailRequest.builder().accountNumber(accountNumber).build();
    ApiResponseJson<Object> response =
        cbaFeignClient.processAccountRequest(AccountServiceType.FETCH_ACCOUNT_INFLOW, cbaRequest);
    Object responseBody = response.getData();
    OrganizationFetchDetailResponse organizationFetchAccountsResponse =
        getObjectMapper().convertValue(responseBody, OrganizationFetchDetailResponse.class);

    OrganizationFetchDetailResponse.Account account =
        organizationFetchAccountsResponse.getAccounts().getFirst();
    return new CustomerAccountApiResponse(
        convertToMinor(account.getInflowTotal()),
        convertToMinor(account.getOutflowTotal()),
        accountNumber);
  }

  public OrganizationFetchAccountRmResponse.RmDetail fetchAccountRm(String accountNumber) {
    if (!customerAccountService.verifyAccountNumber(
        accountNumber, RequestContextHolder.get().getOrganizationId())) {
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    }

    OrganizationFetchDetailRequest cbaRequest =
        OrganizationFetchDetailRequest.builder().accountNumber(accountNumber).build();
    ApiResponseJson<Object> response =
        cbaFeignClient.processAccountRequest(
            AccountServiceType.FETCH_ACCOUNT_RM_DETAIL, cbaRequest);
    Object responseBody = response.getData();
    OrganizationFetchAccountRmResponse organizationFetchAccountsResponse =
        getObjectMapper().convertValue(responseBody, OrganizationFetchAccountRmResponse.class);

    return organizationFetchAccountsResponse.getRmDetail();
  }

  public OrganizationFetchDetailResponse.Account fetchAccountBalance(String accountNumber) {
    if (!customerAccountService.verifyAccountNumber(
        accountNumber, RequestContextHolder.get().getOrganizationId())) {
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    }

    OrganizationFetchDetailRequest cbaRequest =
        OrganizationFetchDetailRequest.builder().accountNumber(accountNumber).build();
    ApiResponseJson<Object> response =
        cbaFeignClient.processAccountRequest(
            AccountServiceType.FETCH_ACCOUNT_BALANCE_DETAIL, cbaRequest);
    Object responseBody = response.getData();
    OrganizationFetchDetailResponse organizationFetchDetailResponse =
        getObjectMapper().convertValue(responseBody, OrganizationFetchDetailResponse.class);

    return organizationFetchDetailResponse.getAccounts().getFirst();
  }

  public OrganizationFetchFxRateResponse fetchFxRate() {
    OrganizationFetchDetailRequest cbaRequest =
        OrganizationFetchDetailRequest.builder().accountNumber("").build();
    ApiResponseJson<Object> response =
        cbaFeignClient.processAccountRequest(AccountServiceType.FETCH_FX_RATE, cbaRequest);
    Object responseBody = response.getData();

    return getObjectMapper().convertValue(responseBody, OrganizationFetchFxRateResponse.class);
  }

  public PaginatedResponseDTO<CustomerTransactionsApiResponse> fetchTransactionsPaginated(
      String accountNumber, int pageNo, int pageSize, LocalDate startDate, LocalDate endDate) {
    validateTransactionRange(startDate, endDate);
    List<CustomerAccountDTO> dbAccountDTOs =
        customerAccountService.fetchActiveViewableAccounts(
            RequestContextHolder.get().getOrganizationId());
    Map<String, CustomerAccountDTO> dbAccountMap =
        dbAccountDTOs.stream()
            .collect(Collectors.toMap(CustomerAccountDTO::getAccountNumber, account -> account));

    if (!dbAccountMap.containsKey(accountNumber)) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.NOT_FOUND);
    }

    OrganizationFetchAccountTransactionsRequest cbaRequest =
        OrganizationFetchAccountTransactionsRequest.builder()
            .accountNumber(accountNumber)
            .pageSize(pageSize)
            .pageNo(pageNo)
            .startDate(startDate)
            .endDate(endDate)
            .build();

    ApiResponseJson<Object> response =
        cbaFeignClient.processAccountRequest(
            AccountServiceType.FETCH_ACCOUNT_TRANSACTIONS, cbaRequest);
    Object responseBody = response.getData();

    OrganizationFetchAccountTransactionsResponse transactionsResponse =
        getObjectMapper()
            .convertValue(responseBody, OrganizationFetchAccountTransactionsResponse.class);

    if (CbaProvider.ResponseStatus.FAILED.equals(transactionsResponse.getResponseStatus())) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getTransactionMessage(NOT_FOUND), HttpStatus.NOT_FOUND);
    }
    return PaginatedResponseDTO.<CustomerTransactionsApiResponse>builder()
        .content(
            transactionsResponse.getData().stream()
                .map(
                    transData ->
                        CustomerTransactionsApiResponse.builder()
                            .accountNumber(transData.getAccountNumber())
                            .amount(transData.getAmount())
                            .isReversed(transData.isReversed())
                            .transactionDate(transData.getTransactionDate())
                            .transactionReference(transData.getTransactionReference())
                            .narration(transData.getNarration())
                            .recordType(transData.getRecordType())
                            .build())
                .toList())
        .currentPage(pageNo + 1)
        .totalPages(transactionsResponse.getPage().getTotalPages())
        .totalItems(transactionsResponse.getPage().getTotalCount())
        .build();
  }

  public List<OrganizationTransactionDataResponse> fetchTransactions(
      String accountNumber, LocalDate startDate, LocalDate endDate) {
    List<CustomerAccountDTO> dbAccountDTOs =
        customerAccountService.fetchActiveViewableAccounts(
            RequestContextHolder.get().getOrganizationId());
    Map<String, CustomerAccountDTO> dbAccountMap =
        dbAccountDTOs.stream()
            .collect(Collectors.toMap(CustomerAccountDTO::getAccountNumber, account -> account));

    if (!dbAccountMap.containsKey(accountNumber)) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.NOT_FOUND);
    }

    OrganizationFetchAccountTransactionsRequest cbaRequest =
        OrganizationFetchAccountTransactionsRequest.builder()
            .accountNumber(accountNumber)
            .startDate(startDate)
            .endDate(endDate)
            .pageSize(0)
            .build();

    ApiResponseJson<Object> response =
        cbaFeignClient.processAccountRequest(
            AccountServiceType.FETCH_ACCOUNT_TRANSACTIONS, cbaRequest);
    Object responseBody = response.getData();

    OrganizationFetchAccountTransactionsResponse transactionsResponse =
        getObjectMapper()
            .convertValue(responseBody, OrganizationFetchAccountTransactionsResponse.class);

    if (CbaProvider.ResponseStatus.FAILED.equals(transactionsResponse.getResponseStatus())
        || ClientUtil.nullOrEmpty(transactionsResponse.getData())) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getTransactionMessage(NOT_FOUND), HttpStatus.NOT_FOUND);
    }

    return transactionsResponse.getData();
  }

  public List<CustomerAccountDTO> fetchAllAccounts(String organizationId) {
    return customerAccountService.fetchAllAccounts(organizationId);
  }

  public byte[] exportAccountStatement(
      String accountNumber, LocalDate startDate, LocalDate endDate, ExportFileType exportType) {
    endDate = endDate != null ? endDate : LocalDate.now();
    startDate = startDate != null ? startDate : endDate.minusDays(10);
    validateTransactionRange(startDate, endDate);

    CustomerAccountDTO customerAccountDTO =
        customerAccountService.fetchAccount(
            accountNumber,
            RequestContextHolder.get().getOrganizationId(),
            RequestContextHolder.get().getBankOrganizationId());
    String customerAddress = customerUserProfileService.retrieveLoggedInUserProfile().getAddress();

    List<OrganizationTransactionDataResponse> transactionResults =
        fetchTransactions(accountNumber, startDate, endDate);

    return generateStatementFile(
        customerAccountDTO, startDate, endDate, exportType, customerAddress, transactionResults);
  }

  public void sendAccountStatement(
      String accountNumber, LocalDate startDate, LocalDate endDate, ExportFileType exportType) {
    endDate = endDate != null ? endDate : LocalDate.now();
    startDate = startDate != null ? startDate : endDate.minusDays(10);
    validateTransactionRange(startDate, endDate);

    List<OrganizationTransactionDataResponse> transactionResults =
        fetchTransactions(accountNumber, startDate, endDate);

    CustomerAccountDTO customerAccountDTO =
        customerAccountService.fetchAccount(
            accountNumber,
            RequestContextHolder.get().getOrganizationId(),
            RequestContextHolder.get().getBankOrganizationId());
    String customerAddress = customerUserProfileService.retrieveLoggedInUserProfile().getAddress();

    byte[] attachment =
        generateStatementFile(
            customerAccountDTO,
            startDate,
            endDate,
            exportType,
            customerAddress,
            transactionResults);
    String redisKey =
        RequestContextHolder.get()
            .getOrganizationId()
            .concat(IDGeneratorUtil.generateSystemId("STATEMENT-"));
    redissonClient.getBucket(redisKey).set(attachment);

    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();
    HashMap<String, Object> contextVariables = new HashMap<>();
    contextVariables.put("subDomain", RequestContextHolder.get().getSubDomainName());
    contextVariables.put("platformName", RequestContextHolder.get().getPlatform());
    contextVariables.put("startDate", startDate.toString());
    contextVariables.put("endDate", endDate.toString());
    contextVariables.put(
        "bankName",
        bankPreferenceDTO == null
            ? RequestContextHolder.get().getSubDomainName()
            : bankPreferenceDTO.getTheme().getBankName());
    contextVariables.put(
        "logoUrl", bankPreferenceDTO == null ? "" : bankPreferenceDTO.getTheme().getLogo());
    contextVariables.put(
        "supportMailLink", bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportEmail());
    notificationDispatcher.dispatchNotification(
        NotificationServiceRequest.builder()
            .recipients(List.of(ClientUtil.getValueFromAccessToken("email")))
            .notificationSubject(messagePropertyConfig.getEmailMessage(ACCOUNT_STATEMENT_SUBJECT))
            .dateTime(LocalDateTime.now())
            .attachments(
                List.of(
                    new NotificationRequestFileDTO(
                        null, "account_statement", exportType.name().toLowerCase(), redisKey)))
            .firstName(ClientUtil.getValueFromAccessToken("name"))
            .notificationRequestType(NotificationRequestType.SEND_ACCOUNT_STATEMENT_EMAIL)
            .context(contextVariables)
            .build(),
        templatePropertyConfig.getAccountTemplate(ACCOUNT_STATEMENT));
  }

  private byte[] generateStatementFile(
      CustomerAccountDTO customerAccountDTO,
      LocalDate startDate,
      LocalDate endDate,
      ExportFileType exportType,
      String customerAddress,
      List<OrganizationTransactionDataResponse> transactionResults) {
    if (ExportFileType.CSV.equals(exportType)) {
      CsvDto<OrganizationTransactionDataResponse> csvDto = new CsvDto<>();
      csvDto.setData(transactionResults);
      return csvExportService.prepareCSVExport(csvDto, this::prepareStatementCsvDto);

    } else if (ExportFileType.PDF.equals(exportType)) {
      Map<String, Object> contextVariables =
          prepareStatementPdfContextVariables(
              transactionResults,
              ClientUtil.getLoggedInUsername(),
              customerAccountDTO.getAccountNumber(),
              customerAccountDTO.getAccountType(),
              customerAddress,
              startDate.toString(),
              endDate.toString());
      return pdfUtil.generatePdf(
          contextVariables, templatePropertyConfig.getAccountTemplate(ACCOUNT_STATEMENT));
    } else {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.BAD_REQUEST);
    }
  }

  private CsvDto<OrganizationTransactionDataResponse> prepareStatementCsvDto(
      CsvDto<OrganizationTransactionDataResponse> parameter) {

    List<OrganizationTransactionDataResponse> data = parameter.getData();
    if (ClientUtil.nullOrEmpty(data)) {
      throw this.exceptionHandler.processCustomException(
          "No record found ", "GEN_006", HttpStatus.NOT_FOUND);
    } else {
      parameter.setCsvHeader(
          new String[] {
            "TransactionReference", "Amount", "Record Type", "Balance", "Narration", "Date"
          });
      parameter
          .getFieldMappings()
          .put(
              "TransactionReference", OrganizationTransactionDataResponse::getTransactionReference);
      parameter
          .getFieldMappings()
          .put("Amount", (transaction) -> convertToFormattedMajor(transaction.getAmount()));
      parameter
          .getFieldMappings()
          .put("Record Type", (OrganizationTransactionDataResponse::getRecordType));
      parameter
          .getFieldMappings()
          .put("Balance", (transaction) -> convertToFormattedMajor(transaction.getBalance()));
      parameter
          .getFieldMappings()
          .put(
              "Narration",
              (transaction) -> {
                if (transaction.getNarration() == null) {
                  return "N/A";
                }
                return transaction.getNarration();
              });
      parameter
          .getFieldMappings()
          .put(
              "Date",
              (transaction) -> {
                if (transaction.getTransactionDate() == null) {
                  return "N/A";
                }
                return transaction.getTransactionDate().toLocalDate().toString();
              });
      return parameter;
    }
  }

  private Map<String, Object> prepareStatementPdfContextVariables(
      List<OrganizationTransactionDataResponse> transactionResults,
      String customerName,
      String accountNumber,
      String accountType,
      String customerAddress,
      String startDate,
      String endDate) {
    HashMap<String, Object> contextVariables = new HashMap<>();
    List<TransactionPdfExportDTO> transactionsTableValues = new ArrayList<>();
    String openingBalance = transactionResults.getFirst().getOpeningBalance();
    String closingBalance = transactionResults.getLast().getOpeningBalance();
    int creditCount = 0;
    int debitCount = 0;
    BigDecimal totalCreditBalance = null;
    BigDecimal totalDebitBalance = null;
    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();
    for (OrganizationTransactionDataResponse transaction : transactionResults) {

      BigDecimal amount = new BigDecimal(transaction.getAmount());
      if ("Credit".equals(transaction.getRecordType())) {
        creditCount++;
        if (totalCreditBalance == null) {
          totalCreditBalance = new BigDecimal(transaction.getAmount());
        } else {
          totalCreditBalance = totalCreditBalance.add(amount);
        }
      } else {
        debitCount++;
        if (totalDebitBalance == null) {
          totalDebitBalance = new BigDecimal(transaction.getAmount());
        } else {
          totalDebitBalance = totalDebitBalance.add(amount);
        }
      }

      TransactionPdfExportDTO transactionRow = new TransactionPdfExportDTO();
      transactionRow.setTransactionReference(transaction.getCbaTransactionReference());
      transactionRow.setTransactionNarration(
          transaction.getNarration() == null ? "" : transaction.getNarration());
      transactionRow.setTransactionDateAsString(
          transaction.getTransactionDate() == null
              ? ""
              : transaction.getTransactionDate().toLocalDate().toString());
      transactionRow.setTransactionTypeDebitAsString(
          "Debit".equalsIgnoreCase(transaction.getRecordType())
              ? convertToFormattedMajor(transaction.getAmount())
              : "");
      transactionRow.setTransactionTypeCreditAsString(
          "Credit".equalsIgnoreCase(transaction.getRecordType())
              ? convertToFormattedMajor(transaction.getAmount())
              : "");
      transactionRow.setRunningBalance(convertToFormattedMajor(transaction.getBalance()));
      transactionsTableValues.add(transactionRow);
    }
    contextVariables.put("transactions", transactionsTableValues);
    contextVariables.put("openingBalance", convertToFormattedMajor(openingBalance));
    contextVariables.put("closingBalance", convertToFormattedMajor(closingBalance));
    contextVariables.put("creditCount", creditCount);
    contextVariables.put("debitCount", debitCount);
    contextVariables.put("currency", "NGN");
    contextVariables.put("startDate", startDate);
    contextVariables.put("endDate", endDate);
    contextVariables.put("accountNumber", accountNumber);
    contextVariables.put("accountType", accountType);
    contextVariables.put("customerAddress", customerAddress);
    contextVariables.put("customerName", customerName);
    contextVariables.put(
        "bankName",
        bankPreferenceDTO == null
            ? RequestContextHolder.get().getSubDomainName()
            : bankPreferenceDTO.getTheme().getBankName());
    contextVariables.put(
        "logoUrl", bankPreferenceDTO == null ? "" : bankPreferenceDTO.getTheme().getLogo());
    contextVariables.put(
        "supportMailLink", bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportEmail());
    contextVariables.put(
        "helpUrl", bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportUrl());
    contextVariables.put(
        "primaryColor",
        bankPreferenceDTO == null
            ? featurePropertyConfig.getPrimaryColor()
            : bankPreferenceDTO.getTheme().getPrimaryColor());
    contextVariables.put(
        "bankEmail", bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportEmail());
    contextVariables.put(
        "totalCreditBalance",
        totalCreditBalance == null
            ? "0.00"
            : convertToFormattedMajor(totalCreditBalance.toString()));
    contextVariables.put(
        "totalDebitBalance",
        totalDebitBalance == null ? "0.00" : convertToFormattedMajor(totalDebitBalance.toString()));
    return contextVariables;
  }

  public byte[] exportTransactionReceipt(CustomerAccountReceiptRequest transactionData) {
    Map<String, Object> contextVariables = prepareReceiptPdfContextVariables(transactionData);
    return pdfUtil.generatePdf(
        contextVariables, templatePropertyConfig.getAccountTemplate(RECEIPT));
  }

  private Map<String, Object> prepareReceiptPdfContextVariables(
      CustomerAccountReceiptRequest transactionData) {
    HashMap<String, Object> contextVariables = new HashMap<>();
    contextVariables.put(
        "date",
        transactionData
            .getTransactionDate()
            .format(DateTimeFormatter.ofPattern("MMM d, yyyy, HH:mm:ss")));
    contextVariables.put("transactionRef", transactionData.getTransactionReference());
    contextVariables.put(
        "amount",
        "NGN".concat(" ").concat(MoneyUtil.convertToFormattedMajor(transactionData.getAmount())));
    contextVariables.put("status", transactionData.isReversed() ? "REVERSED" : "SUCCESSFUL");
    contextVariables.put("recordType", transactionData.getRecordType());
    contextVariables.put("narration", transactionData.getNarration());
    contextVariables.put("accountNumber", transactionData.getAccountNumber());
    return contextVariables;
  }

  private void validateTransactionRange(LocalDate startDate, LocalDate endDate) {
    endDate = endDate != null ? endDate : LocalDate.now();
    startDate = startDate != null ? startDate : endDate.plusMonths(3);

    if (startDate.plusMonths(3).isBefore(endDate)) {
      throw exceptionHandler.processCustomException(
          "Date range cannot span more than 3 months", HttpStatus.BAD_REQUEST);
    }
  }
}
