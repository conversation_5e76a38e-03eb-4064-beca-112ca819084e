/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.account.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.account.AccountSwaggerDocConstant.*;
import static com.digicore.registhentication.util.PageableUtil.*;
import static com.digicore.registhentication.util.PageableUtil.PAGE_SIZE_DEFAULT_VALUE;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.export.enums.ExportFileType;
import com.digicore.lucid.customer.service.modules.account.dto.CustomerAccountReceiptRequest;
import com.digicore.lucid.customer.service.modules.account.service.CustomerAccountOperations;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> Ilori
 * @createdOn 24/02/2025
 */

@RestController
@RequestMapping(API_V1 + ACCOUNT_API)
@Tag(name = ACCOUNT_CONTROLLER_TITLE, description = ACCOUNT_CONTROLLER_DESCRIPTION)
@RequiredArgsConstructor
public class CustomerAccountController {
  private final CustomerAccountOperations customerAccountOperations;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-customer-account')")
  @Operation(summary = ACCOUNT_RETRIEVE_TITLE, description = ACCOUNT_RETRIEVE_DESCRIPTION)
  ResponseEntity<Object> fetchAccounts() {
    return ControllerResponse.buildSuccessResponse(customerAccountOperations.fetchAccounts());
  }

  @GetMapping(FETCH_FLOWS_API)
  @PreAuthorize("hasAuthority('view-customer-account')")
  @Operation(
      summary = ACCOUNT_INFLOW_RETRIEVE_TITLE,
      description = ACCOUNT_INFLOW_RETRIEVE_DESCRIPTION)
  ResponseEntity<Object> fetchAccountInflow(@RequestParam String accountNumber) {
    return ControllerResponse.buildSuccessResponse(
        customerAccountOperations.fetchAccountInflow(accountNumber));
  }

  @GetMapping(FETCH_RM_API)
  @PreAuthorize("hasAuthority('view-customer-account')")
  @Operation(
      summary = ACCOUNT_INFLOW_RETRIEVE_TITLE,
      description = ACCOUNT_INFLOW_RETRIEVE_DESCRIPTION)
  ResponseEntity<Object> fetchAccountRm(@RequestParam String accountNumber) {
    return ControllerResponse.buildSuccessResponse(
        customerAccountOperations.fetchAccountRm(accountNumber));
  }

  @GetMapping(FETCH_BALANCES_API)
  @PreAuthorize("hasAuthority('view-customer-account')")
  @Operation(
      summary = ACCOUNT_INFLOW_RETRIEVE_TITLE,
      description = ACCOUNT_INFLOW_RETRIEVE_DESCRIPTION)
  ResponseEntity<Object> fetchAccountBalance(@RequestParam String accountNumber) {
    return ControllerResponse.buildSuccessResponse(
        customerAccountOperations.fetchAccountBalance(accountNumber));
  }

  @GetMapping(FETCH_FX_RATE_API)
  @PreAuthorize("hasAuthority('view-customer-account')")
  @Operation(
      summary = ACCOUNT_INFLOW_RETRIEVE_TITLE,
      description = ACCOUNT_INFLOW_RETRIEVE_DESCRIPTION)
  ResponseEntity<Object> fetchFxRate() {
    return ControllerResponse.buildSuccessResponse(customerAccountOperations.fetchFxRate());
  }

  @GetMapping(FETCH_TRANSACTIONS_API)
  @PreAuthorize("hasAuthority('view-transactions')")
  ResponseEntity<Object> fetchTransactions(
      @RequestParam String accountNumber,
      @RequestParam(value = PAGE_NUMBER, defaultValue = PAGE_NUMBER_DEFAULT_VALUE, required = false)
          int pageNumber,
      @RequestParam(value = PAGE_SIZE, defaultValue = PAGE_SIZE_DEFAULT_VALUE, required = false)
          int pageSize,
      @RequestParam(required = false) LocalDate startDate,
      @RequestParam(required = false) LocalDate endDate) {
    validateDateOrder(startDate, endDate);
    return ControllerResponse.buildSuccessResponse(
        customerAccountOperations.fetchTransactionsPaginated(
            accountNumber, pageNumber, pageSize, startDate, endDate));
  }

  @GetMapping(EXPORT_API)
  @PreAuthorize("hasAuthority('export-transactions')")
  ResponseEntity<Object> exportStatement(
      @RequestParam String accountNumber,
      @RequestParam(required = false) LocalDate startDate,
      @RequestParam(required = false) LocalDate endDate,
      @RequestParam ExportFileType exportFileType) {

    validateDateOrder(startDate, endDate);
    if (startDate != null && startDate.isBefore(LocalDate.now().minusMonths(2))) {
      customerAccountOperations.sendAccountStatement(
          accountNumber, startDate, endDate, exportFileType);
      return ControllerResponse.buildSuccessResponse("Account statement has been sent to email");
    }
    return ResponseEntity.ok()
        .header(
            HttpHeaders.CONTENT_DISPOSITION,
            "attachment; filename=account_statement.".concat(exportFileType.name().toLowerCase()))
        .body(
            customerAccountOperations.exportAccountStatement(
                accountNumber, startDate, endDate, exportFileType));
  }

  @PostMapping(RECEIPT_API)
  @PreAuthorize("hasAuthority('export-transactions')")
  ResponseEntity<byte[]> exportReceipt(
      @RequestBody CustomerAccountReceiptRequest customerAccountReceiptRequest) {

    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=receipt.pdf")
        .body(customerAccountOperations.exportTransactionReceipt(customerAccountReceiptRequest));
  }

  private void validateDateOrder(LocalDate startDate, LocalDate endDate) {
    if (startDate != null && endDate != null && endDate.isBefore(startDate)) {
      throw exceptionHandler.processCustomException(
          "End date cannot be before start date", HttpStatus.BAD_REQUEST);
    }
  }
}
