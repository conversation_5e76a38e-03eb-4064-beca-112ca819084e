/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.onboarding.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.onboarding.OnboardingSwaggerDocConstant.*;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerOnboardingDTO;
import com.digicore.lucid.customer.service.modules.onboarding.service.CustomerOnboardingFeignService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR>
 * @createdOn Mar-05(Wed)-2025
 */
@Hidden
@RestController
@RequestMapping(API_V1 + CUSTOMER_API + ONBOARDING_API)
@RequiredArgsConstructor
public class CustomerOnboardingFeignController {
  private final CustomerOnboardingFeignService customerOnboardingFeignService;

  @PostMapping(CREATE_API)
  @PreAuthorize("hasAuthority('approve-onboard-customer')")
  public ResponseEntity<Object> onboardCustomer(
      @Valid @RequestBody CustomerOnboardingDTO customerOnboardingDTO) {
    customerOnboardingFeignService.onboardCustomer(customerOnboardingDTO);
    return ControllerResponse.buildSuccessResponse();
  }

  @PostMapping(VALIDATE_API)
  @PreAuthorize("hasAuthority('onboard-customer')")
  public ResponseEntity<Object> validateCustomerDetails(
      @Valid @RequestBody CustomerOnboardingDTO customerOnboardingDTO) {
    customerOnboardingFeignService.validateCustomerDetails(customerOnboardingDTO);
    return ControllerResponse.buildSuccessResponse();
  }

  @PostMapping(ADD_ACCOUNT_API)
  @PreAuthorize("hasAuthority('approve-add-customer-account')")
  public ResponseEntity<Object> updateCustomer(
      @Valid @RequestBody CustomerOnboardingDTO customerOnboardingDTO) {
    customerOnboardingFeignService.updateCustomer(customerOnboardingDTO);
    return ControllerResponse.buildSuccessResponse();
  }
}
