/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.account.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.DUPLICATE;
import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;

import com.digicore.lucid.common.lib.account.dto.CustomerAccountDTO;
import com.digicore.lucid.common.lib.account.service.AccountService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn 06/03/2025
 */

@Service
@RequiredArgsConstructor
public class CustomerAccountFeignOperations {
  private final AccountService<CustomerAccountDTO> customerAccountService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  public List<CustomerAccountDTO> fetchAccounts(String organizationId) {
    return customerAccountService.fetchAllAccounts(organizationId);
  }

  public void verifyAccountNumber(String accountNumber) {
    if (customerAccountService.verifyAccountNumber(accountNumber))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getAccountMessage(DUPLICATE), HttpStatus.BAD_REQUEST);
  }

  public void verifyAccountNumber(String accountNumber, String organizationId) {
    if (!customerAccountService.verifyAccountNumber(accountNumber, organizationId))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getAccountMessage(NOT_FOUND), HttpStatus.NOT_FOUND);
  }
}
