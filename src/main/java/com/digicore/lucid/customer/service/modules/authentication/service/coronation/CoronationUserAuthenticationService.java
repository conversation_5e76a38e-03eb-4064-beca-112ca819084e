/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.authentication.service.coronation;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.message.MessagePlaceHolderConstant.USER;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.AUTHENTICATION;
import static com.digicore.lucid.common.lib.constant.system.SystemConstant.CORPORATE;
import static com.digicore.lucid.common.lib.notification.constant.MailTemplateConstant.*;
import static com.digicore.lucid.common.lib.notification.constant.MailTemplateConstant.LOGIN;

import com.digicore.api.helper.response.ApiError;
import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.authentication.service.DeviceDetectionService;
import com.digicore.lucid.common.lib.authentication.service.LucidLoginAttemptService;
import com.digicore.lucid.common.lib.client.BackOfficeFeignClient;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.notification.config.MailPropertyConfig;
import com.digicore.lucid.common.lib.notification.request.NotificationRequestType;
import com.digicore.lucid.common.lib.notification.request.NotificationServiceRequest;
import com.digicore.lucid.common.lib.notification.service.NotificationDispatcher;
import com.digicore.lucid.common.lib.notification.util.NotificationHelper;
import com.digicore.lucid.common.lib.otp.enums.OtpType;
import com.digicore.lucid.common.lib.otp.service.OtpService;
import com.digicore.lucid.common.lib.processor.service.RegulatoryLoggingService;
import com.digicore.lucid.common.lib.profile.dto.BankPreferenceDTO;
import com.digicore.lucid.common.lib.profile.dto.LoginPreference;
import com.digicore.lucid.common.lib.properties.FeaturePropertyConfig;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.customer.data.modules.device.dto.DeviceRequestDTO;
import com.digicore.lucid.customer.data.modules.device.enums.DeviceAuthOptionEnum;
import com.digicore.lucid.customer.data.modules.device.service.DeviceDAS;
import com.digicore.lucid.customer.service.modules.authentication.dto.LoginApiRequest;
import com.digicore.lucid.customer.service.modules.authentication.enums.DeviceAuthenticationType;
import com.digicore.lucid.integration.lib.modules.config.properties.SecurityPropertyConfig;
import com.digicore.registhentication.authentication.dtos.request.LoginAttemptDTO;
import com.digicore.registhentication.authentication.dtos.request.LoginRequestDTO;
import com.digicore.registhentication.authentication.dtos.request.ResetPasswordFirstBaseRequestDTO;
import com.digicore.registhentication.authentication.dtos.request.ResetPasswordSecondBaseRequestDTO;
import com.digicore.registhentication.authentication.dtos.response.LoginResponse;
import com.digicore.registhentication.authentication.services.LoginService;
import com.digicore.registhentication.authentication.services.PasswordResetService;
import com.digicore.registhentication.common.enums.Channel;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.enums.Status;
import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Ucheagwu
 * @createdOn Jul-07(Mon)-2025
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoronationUserAuthenticationService {
  private final LoginService<LoginResponse, LoginRequestDTO> coronationCustomerUserLoginService;
  private final MessagePropertyConfig messagePropertyConfig;
  private final NotificationDispatcher notificationDispatcher;
  private final NotificationHelper notificationHelper;
  private final RegulatoryLoggingService regulatoryLoggingService;
  private final MailPropertyConfig mailPropertyConfig;
  private final OtpService otpService;
  private final PasswordResetService customerUserPasswordResetService;
  private final AuthProfileService<UserAuthProfileDTO> customerUserAuthProfileService;
  private final RedissonClient redissonClient;
  private final FeaturePropertyConfig featurePropertyConfig;
  private final SecurityPropertyConfig securityPropertyConfig;
  private final DeviceDetectionService deviceDetectionService;
  private final DeviceDAS deviceDataAccessService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final LucidLoginAttemptService loginAttemptService;
  private final BackOfficeFeignClient backOfficeFeignClient;

  // TODO SMS Template needs to be implemented

  /**
   * Authenticates a customer user based on the provided login request data.
   *
   * @param loginApiRequest The DTO containing login request details such as username and password.
   * @param httpServletRequest The HTTP request containing additional customer session details.
   * @return A {@link LoginResponse} containing authentication details for the customer user.
   */
  public LoginResponse authenticateCustomerUser(
      LoginApiRequest loginApiRequest, HttpServletRequest httpServletRequest) {

    LoginResponse loginResponse = coronationCustomerUserLoginService.authenticate(loginApiRequest);

    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();

    validateDevice(bankPreferenceDTO, loginApiRequest, loginResponse);

    notificationDispatcher.dispatchNotification(
        NotificationServiceRequest.builder()
            .recipients(List.of(loginResponse.getAdditionalInformation().get("email").toString()))
            .notificationSubject(messagePropertyConfig.getEmailMessage(LOGIN_SUCCESSFUL_SUBJECT))
            .dateTime(LocalDateTime.now())
            .device(deviceDetectionService.getDeviceInfo(httpServletRequest))
            .firstName(loginResponse.getAdditionalInformation().get("name").toString())
            .bankName(
                bankPreferenceDTO == null
                    ? RequestContextHolder.get().getSubDomainName()
                    : bankPreferenceDTO.getTheme().getBankName())
            .platformName(RequestContextHolder.get().getPlatform())
            .primaryColor(
                bankPreferenceDTO == null
                    ? featurePropertyConfig.getPrimaryColor()
                    : bankPreferenceDTO.getTheme().getPrimaryColor())
            .logoLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getTheme().getLogo())
            .subDomain(RequestContextHolder.get().getSubDomainName())
            .supportMailLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportEmail())
            .helpUrl(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportUrl())
            .notificationRequestType(NotificationRequestType.SEND_LOGIN_SUCCESS_EMAIL)
            .build(),
        mailPropertyConfig.getTemplate(LOGIN));

    regulatoryLoggingService.log(
        (String) loginResponse.getAdditionalInformation().get("role"),
        (String) loginResponse.getAdditionalInformation().get("email"),
        (String) loginResponse.getAdditionalInformation().get("name"),
        "LOGIN",
        AUTHENTICATION,
        messagePropertyConfig
            .getLoginMessage(LOGIN_SUCCESSFUL)
            .replace(USER, (String) loginResponse.getAdditionalInformation().get("name")));

    return loginResponse;
  }

  /**
   * Resends OTP codes for account recovery to a user identified by the given username.
   *
   * @param username The username of the user to whom the codes will be sent.
   */
  public void resendCodes(String username) {
    UserAuthProfileDTO authProfileDTO =
        customerUserAuthProfileService.retrieveAuthProfile(username, false);
    // if (isForEmail)
    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();
    otpService.send(
        NotificationServiceRequest.builder()
            .recipients(List.of(authProfileDTO.getUserProfile().getEmail()))
            .firstName(authProfileDTO.getUserProfile().getFirstName())
            .notificationSubject(
                messagePropertyConfig.getEmailMessage(PASSWORD_RESET_REQUEST_SUBJECT))
            .dateTime(LocalDateTime.now())
            .notificationRequestType(NotificationRequestType.SEND_ACCOUNT_RECOVERY_EMAIL)
            .bankName(
                bankPreferenceDTO == null
                    ? RequestContextHolder.get().getSubDomainName()
                    : bankPreferenceDTO.getTheme().getBankName())
            .primaryColor(
                bankPreferenceDTO == null
                    ? featurePropertyConfig.getPrimaryColor()
                    : bankPreferenceDTO.getTheme().getPrimaryColor())
            .platformName(RequestContextHolder.get().getPlatform())
            .logoLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getTheme().getLogo())
            .subDomain(RequestContextHolder.get().getSubDomainName())
            .supportMailLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportEmail())
            .helpUrl(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportUrl())
            .isHtml(true)
            .build(),
        OtpType.EMAIL_VERIFICATION,
        mailPropertyConfig.getTemplate(PASSWORD_RESET_OTP));
    //    else
    //      otpService.send(
    //          NotificationServiceRequest.builder()
    //              .receiver(authProfileDTO.getUserProfile().getPhoneNumber())
    //              .firstName(authProfileDTO.getUserProfile().getFirstName())
    //              .channel("SMS")
    //              .build(),
    //          OtpType.PHONE_NUMBER_VERIFICATION,
    //          "templateName");
  }

  /**
   * Initializes the password reset process by sending an OTP to the user's associated email.
   *
   * @param username The username of the user requesting the password reset.
   */
  public void initResetPassword(String username) {
    UserAuthProfileDTO authProfileDTO =
        customerUserAuthProfileService.retrieveAuthProfile(username, false);
    //    otpService.send(
    //        NotificationServiceRequest.builder()
    //            .receiver(authProfileDTO.getUserProfile().getPhoneNumber())
    //            .firstName(authProfileDTO.getUserProfile().getFirstName())
    //            .channel("SMS")
    //            .build(),
    //        OtpType.PHONE_NUMBER_VERIFICATION,
    //        "templateName");

    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();
    otpService.send(
        NotificationServiceRequest.builder()
            .recipients(List.of(authProfileDTO.getUserProfile().getEmail()))
            .firstName(authProfileDTO.getUserProfile().getFirstName())
            .notificationSubject(
                messagePropertyConfig.getEmailMessage(PASSWORD_RESET_REQUEST_SUBJECT))
            .dateTime(LocalDateTime.now())
            .notificationRequestType(NotificationRequestType.SEND_ACCOUNT_RECOVERY_EMAIL)
            .primaryColor(
                bankPreferenceDTO == null
                    ? featurePropertyConfig.getPrimaryColor()
                    : bankPreferenceDTO.getTheme().getPrimaryColor())
            .platformName(CORPORATE)
            .bankName(
                bankPreferenceDTO == null
                    ? RequestContextHolder.get().getSubDomainName()
                    : bankPreferenceDTO.getTheme().getBankName())
            .logoLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getTheme().getLogo())
            .subDomain(RequestContextHolder.get().getSubDomainName())
            .supportMailLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportEmail())
            .helpUrl(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportUrl())
            .isHtml(true)
            .build(),
        OtpType.EMAIL_VERIFICATION,
        mailPropertyConfig.getTemplate(PASSWORD_RESET_OTP));
  }

  /**
   * Retrieves login preferences such as theme and color settings for the currently active bank.
   *
   * @return A {@link LoginPreference} object containing login customization preferences.
   */
  public LoginPreference retrieveLoginPreferences() {
    BankPreferenceDTO bankPreferenceDTO = null;
    try {

      bankPreferenceDTO =
          (BankPreferenceDTO)
              redissonClient
                  .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                  .get();
    } catch (Exception e) {
      log.info("Redission exception occur");
      log.info("Fetch from redission exception: {}", e.getMessage());
      return getLoginPreference(bankPreferenceDTO);
    }
    return getLoginPreference(bankPreferenceDTO);
  }

  /**
   * Generates the login preferences for the user using the bank's preference details.
   *
   * @param bankPreferenceDTO The DTO containing the bank's theme and customization settings.
   * @return A {@link LoginPreference} object with fallback defaults if bank preferences are not
   *     available.
   */
  private LoginPreference getLoginPreference(BankPreferenceDTO bankPreferenceDTO) {
    LoginPreference loginPreference = new LoginPreference();
    if (bankPreferenceDTO == null) {
      BankPreferenceDTO.Theme bankTheme = new BankPreferenceDTO.Theme();
      bankTheme.setSecondaryColor(featurePropertyConfig.getSecondaryColor());
      bankTheme.setPrimaryColor(featurePropertyConfig.getPrimaryColor());
      bankTheme.setLogo(featurePropertyConfig.getLogo());
      bankTheme.setBankName(RequestContextHolder.get().getSubDomainName());
      loginPreference.setTheme(bankTheme);
    } else {
      loginPreference.setTheme(bankPreferenceDTO.getTheme());
    }
    return loginPreference;
  }

  //  public void validatePhoneNumber(
  //      ResetPasswordFirstBaseRequestDTO resetPasswordFirstBaseRequestDTO) {
  //    UserAuthProfileDTO authProfileDTO =
  //        customerUserAuthProfileService.retrieveAuthProfile(
  //            resetPasswordFirstBaseRequestDTO.getEmail(), false);
  //    if (!securityPropertyConfig.isSkipSmsValidation())
  //      otpService.effect(
  //          authProfileDTO.getUserProfile().getPhoneNumber(),
  //          OtpType.PHONE_NUMBER_VERIFICATION,
  //          resetPasswordFirstBaseRequestDTO.getOtp());
  //    otpService.send(
  //        NotificationServiceRequest.builder()
  //            .recipients(List.of(authProfileDTO.getUserProfile().getEmail()))
  //            .firstName(authProfileDTO.getUserProfile().getFirstName())
  //            .notificationSubject(
  //                messagePropertyConfig.getEmailMessage(PASSWORD_RESET_REQUEST_SUBJECT))
  //            .dateTime(LocalDateTime.now())
  //            .notificationRequestType(NotificationRequestType.SEND_ACCOUNT_RECOVERY_EMAIL)
  //            .isHtml(true)
  //            .build(),
  //        OtpType.EMAIL_VERIFICATION,
  //        mailPropertyConfig.getTemplate(PASSWORD_RESET));
  //  }

  /**
   * Validates the email address for password reset using the provided OTP.
   *
   * @param resetPasswordFirstBaseRequestDTO The DTO containing the email address and OTP for
   *     validation.
   * @return A map containing the response details such as reset code.
   */
  public Map<String, Object> validateEmail(
      ResetPasswordFirstBaseRequestDTO resetPasswordFirstBaseRequestDTO) {
    UserAuthProfileDTO authProfileDTO =
        customerUserAuthProfileService.retrieveAuthProfile(
            resetPasswordFirstBaseRequestDTO.getEmail(), false);
    otpService.effect(
        authProfileDTO.getUserProfile().getEmail(),
        OtpType.EMAIL_VERIFICATION,
        resetPasswordFirstBaseRequestDTO.getOtp());
    otpService.store(
        resetPasswordFirstBaseRequestDTO
            .getEmail()
            .concat(authProfileDTO.getUserProfile().getPhoneNumber()),
        OtpType.PASSWORD_UPDATE);
    Map<String, Object> response = new HashMap<>();
    response.put(
        "resetCode",
        otpService.store(resetPasswordFirstBaseRequestDTO.getEmail(), OtpType.PASSWORD_UPDATE));

    return response;
  }

  /**
   * Completes the password reset process and updates the user's account password.
   *
   * @param resetPasswordSecondBaseRequestDTO The DTO containing the new password and user
   *     identification details.
   */
  public void completePasswordReset(
      ResetPasswordSecondBaseRequestDTO resetPasswordSecondBaseRequestDTO) {
    UserAuthProfileDTO authProfileDTO =
        customerUserAuthProfileService.retrieveAuthProfile(
            resetPasswordSecondBaseRequestDTO.getEmail(), false);
    customerUserPasswordResetService.updateAccountPassword(resetPasswordSecondBaseRequestDTO);

    LoginAttemptDTO loginAttemptDTO = new LoginAttemptDTO();
    loginAttemptDTO.setUsername(authProfileDTO.getUsername());
    loginAttemptService.unlockUser(loginAttemptDTO);

    regulatoryLoggingService.log(
        authProfileDTO.getAssignedRole(),
        authProfileDTO.getUsername(),
        authProfileDTO
            .getUserProfile()
            .getFirstName()
            .concat(" ")
            .concat(authProfileDTO.getUserProfile().getLastName()),
        PASSWORD_UPDATED,
        AUTHENTICATION,
        messagePropertyConfig
            .getLoginMessage(PASSWORD_UPDATED)
            .replace(
                USER,
                authProfileDTO
                    .getUserProfile()
                    .getFirstName()
                    .concat(" ")
                    .concat(authProfileDTO.getUserProfile().getLastName())));
    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();
    notificationDispatcher.dispatchNotification(
        NotificationServiceRequest.builder()
            .notificationSubject(
                messagePropertyConfig.getEmailMessage(PASSWORD_RESET_SUCCESSFUL_SUBJECT))
            .recipients(List.of(authProfileDTO.getUserProfile().getEmail()))
            .dateTime(LocalDateTime.now())
            .primaryColor(
                bankPreferenceDTO == null
                    ? featurePropertyConfig.getPrimaryColor()
                    : bankPreferenceDTO.getTheme().getPrimaryColor())
            .platformName(CORPORATE)
            .bankName(
                bankPreferenceDTO == null
                    ? RequestContextHolder.get().getSubDomainName()
                    : bankPreferenceDTO.getTheme().getBankName())
            .logoLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getTheme().getLogo())
            .subDomain(RequestContextHolder.get().getSubDomainName())
            .supportMailLink(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportEmail())
            .helpUrl(bankPreferenceDTO == null ? "" : bankPreferenceDTO.getSupportUrl())
            .notificationRequestType(NotificationRequestType.SEND_PASSWORD_UPDATE_EMAIL)
            .build(),
        mailPropertyConfig.getTemplate(PASSWORD_UPDATE));
  }

  public void deviceOtp(String username) {
    UserAuthProfileDTO authProfileDTO =
        customerUserAuthProfileService.retrieveAuthProfile(username, false);
    otpService.send(
        notificationHelper
            .buildBasicRequest(authProfileDTO.getUserProfile().getEmail())
            .notificationRequestType(NotificationRequestType.SEND_DEVICE_REGISTRATION_EMAIL)
            .build(),
        OtpType.DEVICE_REGISTRATION,
        mailPropertyConfig.getTemplate(OTP_NOTIFICATION));
  }

  public String deviceOtpVerification(String username, String otp) {
    UserAuthProfileDTO authProfileDTO =
        customerUserAuthProfileService.retrieveAuthProfile(username, false);
    otpService.effect(authProfileDTO.getUserProfile().getEmail(), OtpType.DEVICE_REGISTRATION, otp);
    return otpService.store(username, OtpType.DEVICE_REGISTRATION);
  }

  public void deviceRegistration(String username, String code, DeviceRequestDTO requestDTO) {

    otpService.effect(username, OtpType.DEVICE_REGISTRATION, code);

    BankPreferenceDTO bankPreferenceDTO =
        (BankPreferenceDTO)
            redissonClient
                .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
                .get();

    requestDTO.setChannel(RequestContextHolder.get().getChannel());

    BankPreferenceDTO.DeviceAuthOptions deviceAuthOptions =
        bankPreferenceDTO.getDeviceAuthOptions();

    if (deviceAuthOptions == null) {
      BankPreferenceDTO.DeviceAuthOptions newdeviceAuthOptions =
          new BankPreferenceDTO.DeviceAuthOptions();
      newdeviceAuthOptions.setMobileDeviceAuthOptions(List.of("SWITCH_DEVICE"));
      newdeviceAuthOptions.setWebDeviceAuthOptions(List.of("SWITCH_DEVICE", "ADD_DEVICE"));
      bankPreferenceDTO.setDeviceAuthOptions(newdeviceAuthOptions);

      redissonClient
          .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
          .set(bankPreferenceDTO);
    }

    List<String> optionsByChannel =
        Channel.WEB.equals(RequestContextHolder.get().getChannel())
            ? deviceAuthOptions.getWebDeviceAuthOptions()
            : deviceAuthOptions.getMobileDeviceAuthOptions();

    if (optionsByChannel == null) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getDeviceMessage(NOT_CONFIGURED), HttpStatus.BAD_REQUEST);
    }

    if (optionsByChannel.stream()
        .noneMatch(option -> option.equalsIgnoreCase(requestDTO.getDeviceOption().name()))) {
      exceptionHandler.processCustomExceptions(
          optionsByChannel.toString(),
          HttpStatus.UNAUTHORIZED,
          List.of(new ApiError(messagePropertyConfig.getDeviceMessage(NOT_CONFIGURED))));
    }

    if (DeviceAuthOptionEnum.ADD_DEVICE.equals(requestDTO.getDeviceOption())) {
      deviceDataAccessService.create(
          requestDTO, username, RequestContextHolder.get().getSubDomainName());
    } else {
      deviceDataAccessService.switchLastLoggedInDevice(
          requestDTO, username, RequestContextHolder.get().getSubDomainName());
    }
  }

  private void validateDevice(
      BankPreferenceDTO bankPreferenceDTO,
      LoginApiRequest loginApiRequest,
      LoginResponse loginResponse) {
    if (securityPropertyConfig.isValidateDeviceOnLogin() && bankPreferenceDTO != null) {
      if (bankPreferenceDTO.getDeviceAuthOptions() == null) {
        BankPreferenceDTO.DeviceAuthOptions newdeviceAuthOptions =
            new BankPreferenceDTO.DeviceAuthOptions();
        newdeviceAuthOptions.setMobileDeviceAuthOptions(List.of("SWITCH_DEVICE"));
        newdeviceAuthOptions.setWebDeviceAuthOptions(List.of("SWITCH_DEVICE", "ADD_DEVICE"));
        bankPreferenceDTO.setDeviceAuthOptions(newdeviceAuthOptions);

        redissonClient
            .getBucket(RequestContextHolder.get().getSubDomainName().concat("-PREFERENCE"))
            .set(bankPreferenceDTO);
      }

      if ("true"
              .equalsIgnoreCase(
                  (String) loginResponse.getAdditionalInformation().get("profileActivated"))
          || "false"
              .equalsIgnoreCase((String) loginResponse.getAdditionalInformation().get("proceed"))) {
        if (DeviceAuthenticationType.OTP.equals(loginApiRequest.getDeviceAuthenticationType())) {
          otpService.effect(
              loginApiRequest.getUsername(), OtpType.DEVICE_REGISTRATION, loginApiRequest.getOtp());
        } else {
          if (!deviceDataAccessService.existsForUserByStatus(
              loginApiRequest.getDeviceId(),
              List.of(Status.ACTIVE),
              loginApiRequest.getUsername(),
              RequestContextHolder.get().getSubDomainName())) {
            List<String> deviceOptions =
                RequestContextHolder.get().getChannel().equals(Channel.MOBILE)
                    ? bankPreferenceDTO.getDeviceAuthOptions().getMobileDeviceAuthOptions()
                    : bankPreferenceDTO.getDeviceAuthOptions().getWebDeviceAuthOptions();
            String stringDeviceOptions = deviceOptions != null ? deviceOptions.toString() : "";
            exceptionHandler.processCustomExceptions(
                stringDeviceOptions,
                HttpStatus.UNAUTHORIZED,
                List.of(new ApiError(messagePropertyConfig.getDeviceMessage(INVALID))));
          }
        }
      }

      if (DeviceAuthenticationType.EXISTING.equals(loginApiRequest.getDeviceAuthenticationType())) {
        deviceDataAccessService.updateLastLogin(
            loginApiRequest.getDeviceId(),
            loginApiRequest.getUsername(),
            RequestContextHolder.get().getSubDomainName());
      }
    }
  }
}
