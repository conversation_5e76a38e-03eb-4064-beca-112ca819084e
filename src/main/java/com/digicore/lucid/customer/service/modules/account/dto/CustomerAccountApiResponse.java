/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.account.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn Feb-28(Fri)-2025
 */

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CustomerAccountApiResponse {

  public CustomerAccountApiResponse(
      String accountNumber,
      String accountName,
      String currency,
      String accountType,
      String withdrawalAmount,
      String availableBalance,
      String ledgerBalance) {
    this.accountNumber = accountNumber;
    this.accountName = accountName;
    this.currency = currency;
    this.accountType = accountType;
    this.withdrawalAmount = withdrawalAmount;
    this.availableBalance = availableBalance;
    this.ledgerBalance = ledgerBalance;
  }

  public CustomerAccountApiResponse(String inflowTotal, String outflowTotal, String accountNumber) {
    this.inflowTotal = inflowTotal;
    this.outflowTotal = outflowTotal;
    this.accountNumber = accountNumber;
  }

  private String accountNumber;
  private String accountName;
  private String currency;
  private String accountType;
  private String withdrawalAmount;
  private String availableBalance;
  private String ledgerBalance;
  private String inflowTotal;
  private String outflowTotal;
}
