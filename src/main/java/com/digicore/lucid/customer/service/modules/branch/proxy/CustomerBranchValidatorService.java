/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.branch.proxy;

import com.digicore.lucid.common.lib.branch.service.BranchService;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.customer.data.modules.branch.dto.CustomerBranchDTO;
import com.digicore.lucid.customer.service.modules.branch.dto.CustomerBranchCreateRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Apr-04(Fri)-2025
 */

@Service
@RequiredArgsConstructor
public class CustomerBranchValidatorService {
  private final BranchService<CustomerBranchDTO> customerBranchService;
  private final CustomerBranchProxyService customerBranchProxyService;

  public void createCustomerBranch(CustomerBranchCreateRequest request) {
    CustomerBranchDTO customerBranchToCreate = new CustomerBranchDTO();
    BeanUtilWrapper.copyNonNullProperties(request, customerBranchToCreate);
    customerBranchService.validateBranch(customerBranchToCreate, true);
    customerBranchProxyService.createBranch(null, customerBranchToCreate);
  }

  public void editCustomerBranch(CustomerBranchDTO customerBranchDTO) {
    customerBranchService.validateBranch(customerBranchDTO, false);
    CustomerBranchDTO customerBranchToEdit =
        customerBranchService.retrieveBranch(
            RequestContextHolder.get().getOrganizationId(), customerBranchDTO.getBranchId());
    customerBranchProxyService.editBranch(customerBranchToEdit, customerBranchDTO);
  }

  public void deleteCustomerBranch(String branchId) {
    CustomerBranchDTO customerBranchToDelete =
        customerBranchService.retrieveBranch(
            RequestContextHolder.get().getOrganizationId(), branchId);
    CustomerBranchDTO customerBranchDTO = new CustomerBranchDTO();
    customerBranchDTO.setBranchId(customerBranchToDelete.getBranchId());
    customerBranchDTO.setActive(customerBranchToDelete.isActive());
    customerBranchProxyService.deleteBranch(customerBranchToDelete, customerBranchDTO);
  }
}
