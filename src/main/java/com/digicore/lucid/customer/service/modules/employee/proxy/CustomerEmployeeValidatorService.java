/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.service.modules.employee.proxy;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.DUPLICATE;

import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.customer.data.modules.employee.dto.CustomerEmployeeDTO;
import com.digicore.lucid.customer.data.modules.employee.dto.EmployeeRequestDTO;
import com.digicore.lucid.customer.data.modules.employee.service.DataAccessService;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn 12/03/2025
 */

@Service
@RequiredArgsConstructor
public class CustomerEmployeeValidatorService {
  private final DataAccessService<CustomerEmployeeDTO> customerEmployeeDataAccessService;
  private final CustomerEmployeeProxyService customerEmployeeProxyService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  public void createEmployee(EmployeeRequestDTO createDTO) {
    if (customerEmployeeDataAccessService.exists(
        createDTO.getEmployeeId(), createDTO.getAccountNumber())) {
      throw exceptionHandler.processCustomException(
          messagePropertyConfig.getProfileMessage(DUPLICATE), HttpStatus.BAD_REQUEST);
    }
    customerEmployeeProxyService.createEmployee(null, createDTO);
  }

  public void editEmployee(EmployeeRequestDTO editDTO) {
    CustomerEmployeeDTO currentData =
        customerEmployeeDataAccessService.retrieve(editDTO.getEmployeeId());
    customerEmployeeProxyService.editEmployee(currentData, editDTO);
  }

  public void removeEmployee(String employeeId) {
    CustomerEmployeeDTO currentData = customerEmployeeDataAccessService.retrieve(employeeId);
    customerEmployeeProxyService.removeEmployee(currentData, employeeId);
  }
}
