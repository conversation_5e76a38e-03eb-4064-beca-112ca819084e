/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.service.modules.bank.onboarding.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.BANK_CONTROLLER_DESCRIPTION;
import static com.digicore.lucid.common.lib.swagger.constant.activation.ActivationSwaggerDocConstant.UPLOAD_DOCUMENT_API;
import static com.digicore.lucid.common.lib.swagger.constant.onboarding.OnboardingSwaggerDocConstant.*;

import com.digicore.api.helper.response.ApiError;
import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.administration.service.modules.bank.onboarding.proxy.BankOnboardingValidatorService;
import com.digicore.lucid.administration.service.modules.bank.onboarding.service.BankOnboardingService;
import com.digicore.lucid.common.lib.registration.dto.backoffice.BankRegistrationDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-04(Tue)-2025
 */

@RestController
@RequestMapping(API_V1 + BACKOFFICE_API + ONBOARDING_API)
@Tag(name = BANK_CONTROLLER_TITLE, description = BANK_CONTROLLER_DESCRIPTION)
@RequiredArgsConstructor
public class BankOnboardingController {
  private final BankOnboardingValidatorService bankOnboardingValidatorService;
  private final BankOnboardingService bankOnboardingService;

  @PostMapping(CREATE_API)
  @PreAuthorize("hasAuthority('create-bank')")
  @Operation(
      summary = BANK_ONBOARDING_CONTROLLER_INVITE_USER_TITLE,
      description = BANK_ONBOARDING_CONTROLLER_INVITE_USER_DESCRIPTION)
  public ResponseEntity<Object> onboardBank(@RequestBody BankRegistrationDTO bankRegistrationDTO) {
    bankOnboardingValidatorService.onboardBank(bankRegistrationDTO);
    return ControllerResponse.buildSuccessResponse();
  }

  @PostMapping(UPLOAD_DOCUMENT_API)
  @PreAuthorize("hasAuthority('create-bank')")
  @Operation(
      summary = BANK_ONBOARDING_CONTROLLER_INVITE_USER_TITLE,
      description = BANK_ONBOARDING_CONTROLLER_INVITE_USER_DESCRIPTION)
  public ResponseEntity<Object> uploadDocument(
      @RequestParam("file") MultipartFile bankCreationAttachment,
      @RequestParam("subDomain") String subDomain,
      @RequestParam("documentType") String documentType) {
    if (!Objects.requireNonNull(bankCreationAttachment.getContentType())
        .equalsIgnoreCase("text/csv")) {
      return ControllerResponse.buildFailureResponse(
          List.of(new ApiError("attachment must be .csv format")), HttpStatus.BAD_REQUEST);
    }
    return ControllerResponse.buildSuccessResponse(
        bankOnboardingService.uploadBankDocuments(bankCreationAttachment, subDomain, documentType));
  }
}
