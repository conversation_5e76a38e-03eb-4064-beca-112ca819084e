/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.service.modules.authentication.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.LOGIN_SUCCESSFUL;
import static com.digicore.lucid.common.lib.constant.message.MessageConstant.LOGIN_SUCCESSFUL_SUBJECT;
import static com.digicore.lucid.common.lib.constant.message.MessagePlaceHolderConstant.USER;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.AUTHENTICATION;
import static com.digicore.lucid.common.lib.notification.constant.MailTemplateConstant.LOGIN;

import com.digicore.lucid.common.lib.notification.service.NotificationDispatcher;
import com.digicore.lucid.common.lib.notification.util.NotificationHelper;
import com.digicore.lucid.common.lib.processor.service.RegulatoryLoggingService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.registhentication.authentication.dtos.request.LoginRequestDTO;
import com.digicore.registhentication.authentication.dtos.response.LoginResponse;
import com.digicore.registhentication.authentication.services.LoginService;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Jan-28(Tue)-2025
 */

@Service
@RequiredArgsConstructor
public class UserAuthenticationService {
  private final LoginService<LoginResponse, LoginRequestDTO> adminUserLoginService;
  private final MessagePropertyConfig messagePropertyConfig;
  private final NotificationDispatcher notificationDispatcher;
  private final RegulatoryLoggingService regulatoryLoggingService;
  private final NotificationHelper notificationHelper;

  public LoginResponse authenticateAdminUser(
      LoginRequestDTO loginRequestDTO, HttpServletRequest httpServletRequest) {
    LoginResponse loginResponse = adminUserLoginService.authenticate(loginRequestDTO);
    notificationDispatcher.dispatchNotification(
        notificationHelper.buildLoginRequest(
            List.of(loginResponse.getAdditionalInformation().get("email").toString()),
            loginResponse.getAdditionalInformation().get("name").toString(),
            LOGIN_SUCCESSFUL_SUBJECT,
            httpServletRequest),
        notificationHelper.getTemplateName(LOGIN));
    regulatoryLoggingService.log(
        (String) loginResponse.getAdditionalInformation().get("role"),
        (String) loginResponse.getAdditionalInformation().get("email"),
        (String) loginResponse.getAdditionalInformation().get("name"),
        "LOGIN",
        AUTHENTICATION,
        messagePropertyConfig
            .getLoginMessage(LOGIN_SUCCESSFUL)
            .replace(USER, (String) loginResponse.getAdditionalInformation().get("name")));
    return loginResponse;
  }
}
