/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.service.modules.limit.processor;

import com.digicore.lucid.administration.service.modules.limit.service.AdminLimitService;
import com.digicore.lucid.common.lib.processor.annotation.RequestHandler;
import com.digicore.lucid.common.lib.processor.annotation.RequestType;
import com.digicore.lucid.common.lib.processor.constant.RequestHandlerType;
import lombok.RequiredArgsConstructor;

/*
 * <AUTHOR>
 * @createdOn Mar-04(Tue)-2025
 */

@RequestHandler(type = RequestHandlerType.PROCESS_MAKER_REQUESTS)
@RequiredArgsConstructor
public class AdminLimitConfigProcessor {
  private final AdminLimitService adminLimitService;

  @RequestType(name = "updateLimitConfig")
  public Object updateLimitConfig(Object approvalDecisionDTO) {
    return adminLimitService.updateLimitConfig(null, approvalDecisionDTO);
  }
}
