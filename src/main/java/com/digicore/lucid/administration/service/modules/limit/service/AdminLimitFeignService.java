/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.service.modules.limit.service;

import com.digicore.api.helper.response.ApiError;
import com.digicore.lucid.common.lib.limit.dto.LimitConfigDTO;
import com.digicore.lucid.common.lib.limit.service.LimitConfigService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Mar-04(Tue)-2025
 */

@RequiredArgsConstructor
@Service
public class AdminLimitFeignService {
  private final LimitConfigService<LimitConfigDTO> adminLimitConfigService;

  public List<LimitConfigDTO> fetchLimitsConfigs() {
    return adminLimitConfigService.retrieveLimitConfig();
  }

  public List<ApiError> validateLimitConfig(LimitConfigDTO limitConfigDTO) {
    return adminLimitConfigService.validateLimitConfig(limitConfigDTO);
  }
}
