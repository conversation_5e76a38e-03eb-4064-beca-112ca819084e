/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.service.modules.bank.onboarding.proxy;

/*
 * <AUTHOR>
 * @createdOn Jan-28(Tue)-2025
 */

public interface BankOnboardingProxyService {
  default Object onboardBank(Object initialData, Object updateData, Object... files) {
    return null;
  }
}
