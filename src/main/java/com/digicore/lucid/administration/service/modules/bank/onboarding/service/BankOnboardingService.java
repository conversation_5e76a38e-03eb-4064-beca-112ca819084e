/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.service.modules.bank.onboarding.service;

import static com.digicore.lucid.common.lib.constant.file.FileConstant.BACKOFFICE_UPLOAD_PARENT_DIRECTORY;
import static com.digicore.lucid.common.lib.constant.message.MessageConstant.ONBOARD;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.BANK;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.BANK_REGISTRATION_DTO;

import com.digicore.lucid.administration.service.modules.bank.onboarding.proxy.BankOnboardingProxyService;
import com.digicore.lucid.common.lib.client.BackOfficeFeignClient;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.lucid.common.lib.registration.dto.backoffice.BankRegistrationDTO;
import com.digicore.lucid.common.lib.util.FileUtil;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Mar-08(Sat)-2025
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class BankOnboardingService implements BankOnboardingProxyService {
  private final BackOfficeFeignClient backOfficeFeignClient;
  private final FileUtil fileUtil;

  @Override
  @MakerChecker(
      checkerPermission = "approve-create-bank",
      makerPermission = "create-bank",
      requestClassName = BANK_REGISTRATION_DTO,
      activity = ONBOARD,
      module = BANK)
  public Object onboardBank(Object initialData, Object requestDTO, Object... args) {
    BankRegistrationDTO bankRegistrationDTO = (BankRegistrationDTO) requestDTO;
    backOfficeFeignClient.createBank(bankRegistrationDTO);
    return Optional.empty();
  }

  public String uploadBankDocuments(MultipartFile file, String subDomain, String documentType) {
    String fileId = UUID.randomUUID().toString();
    String filePath =
        BACKOFFICE_UPLOAD_PARENT_DIRECTORY.concat(subDomain).concat("/").concat(documentType);
    String fileName = fileUtil.fileName(fileId, file);
    return fileUtil.saveFile(filePath, file, fileName);
  }
}
