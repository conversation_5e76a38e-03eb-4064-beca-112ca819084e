/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.service.modules.authorization.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.ROLE;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.ROLE_CREATION_DTO;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.ROLE_DTO;

import com.digicore.lucid.administration.service.modules.authorization.proxy.AdminUserRoleProxyService;
import com.digicore.lucid.common.lib.authorization.dto.RoleCreationDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleDTO;
import com.digicore.lucid.common.lib.authorization.service.RoleService;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Jan-28(Tue)-2025
 */

@Service
@RequiredArgsConstructor
public class UserRoleService implements AdminUserRoleProxyService {
  private final RoleService<RoleDTO, RoleCreationDTO> adminUserRoleService;

  @MakerChecker(
      checkerPermission = "approve-create-admin-roles",
      makerPermission = "create-admin-roles",
      requestClassName = ROLE_CREATION_DTO,
      activity = CREATE,
      module = ROLE)
  public Object createRole(Object initialData, Object updateData, Object... files) {
    RoleCreationDTO roleDTO = (RoleCreationDTO) updateData;
    adminUserRoleService.createRole(roleDTO);
    return Optional.empty();
  }

  @MakerChecker(
      checkerPermission = "approve-delete-admin-role",
      makerPermission = "delete-admin-role",
      requestClassName = ROLE_DTO,
      activity = DELETE,
      module = ROLE)
  public Object deleteRole(Object initialData, Object updateData, Object... files) {
    RoleDTO roleDTO = (RoleDTO) updateData;
    adminUserRoleService.deleteRole(roleDTO.getName());
    return Optional.empty();
  }

  @MakerChecker(
      checkerPermission = "approve-edit-admin-role",
      makerPermission = "edit-admin-role",
      requestClassName = ROLE_CREATION_DTO,
      activity = EDIT,
      module = ROLE)
  public Object editRole(Object initialData, Object updateData, Object... files) {
    RoleCreationDTO roleDTO = (RoleCreationDTO) updateData;
    adminUserRoleService.editRole(roleDTO);
    return Optional.empty();
  }

  @MakerChecker(
      checkerPermission = "approve-disable-admin-role",
      makerPermission = "disable-admin-role",
      requestClassName = ROLE_DTO,
      activity = DISABLE,
      module = ROLE)
  public Object disableRole(Object initialData, Object updateData, Object... files) {
    RoleDTO roleDTO = (RoleDTO) updateData;
    adminUserRoleService.disableRole(roleDTO.getName());
    return Optional.empty();
  }

  @MakerChecker(
      checkerPermission = "approve-enable-admin-role",
      makerPermission = "enable-admin-role",
      requestClassName = ROLE_DTO,
      activity = ENABLE,
      module = ROLE)
  public Object enableRole(Object initialData, Object updateData, Object... files) {
    RoleDTO roleDTO = (RoleDTO) updateData;
    adminUserRoleService.enableRole(roleDTO.getName());
    return Optional.empty();
  }

  public Object getAllRoles(int pageNumber, int pageSize, String paginated) {
    if ("false".equalsIgnoreCase(paginated)) return adminUserRoleService.retrieveAllRoles();
    return adminUserRoleService.retrieveAllRoles(pageNumber, pageSize);
  }
}
