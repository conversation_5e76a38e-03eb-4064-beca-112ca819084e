/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.service.modules.authorization.proxy;

import com.digicore.lucid.common.lib.authorization.dto.PermissionDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleCreationDTO;
import com.digicore.lucid.common.lib.authorization.dto.RoleDTO;
import com.digicore.lucid.common.lib.authorization.service.PermissionService;
import com.digicore.lucid.common.lib.authorization.service.RoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn Jan-28(Tue)-2025
 */

@Service
@RequiredArgsConstructor
public class AdminUserRoleValidatorService {
  private final AdminUserRoleProxyService adminUserRoleProxyService;
  private final RoleService<RoleDTO, RoleCreationDTO> adminUserRoleService;
  private final PermissionService<PermissionDTO> adminUserPermissionServiceImpl;

  public void createRole(RoleCreationDTO roleDTO) {
    adminUserRoleService.validateRole(roleDTO.getName(), false, true);
    adminUserPermissionServiceImpl.validatePermissions(roleDTO.getPermissions().stream().toList());
    adminUserRoleProxyService.createRole(null, roleDTO);
  }

  public void deleteRole(String roleName) {
    adminUserRoleService.validateRole(roleName, false, false);
    RoleDTO roleToDelete = adminUserRoleService.retrieveRole(roleName);
    RoleDTO roleDTO = new RoleDTO();
    roleDTO.setName(roleName);
    adminUserRoleProxyService.deleteRole(roleToDelete, roleDTO);
  }

  public void editRole(RoleCreationDTO roleDTO) {
    adminUserRoleService.validateRole(roleDTO.getName(), false, false);
    RoleDTO roleToEdit = adminUserRoleService.retrieveRole(roleDTO.getName());
    adminUserPermissionServiceImpl.validatePermissions(roleDTO.getPermissions().stream().toList());
    adminUserRoleProxyService.editRole(roleToEdit, roleDTO);
  }

  public void disableRole(String roleName) {
    adminUserRoleService.validateRole(roleName, false, false);
    RoleDTO roleToDisable = adminUserRoleService.retrieveRole(roleName);
    RoleDTO roleDTO = new RoleDTO();
    roleDTO.setName(roleName);
    adminUserRoleProxyService.disableRole(roleToDisable, roleDTO);
  }

  public void enableRole(String roleName) {
    adminUserRoleService.validateRole(roleName, false, false);
    RoleDTO roleToEnable = adminUserRoleService.retrieveRole(roleName);
    RoleDTO roleDTO = new RoleDTO();
    roleDTO.setName(roleName);
    adminUserRoleProxyService.enableRole(roleToEnable, roleDTO);
  }
}
