/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.service.modules.limit.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.EDIT;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.LIMIT;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.LIMIT_CONFIG_DTO;

import com.digicore.lucid.administration.service.modules.limit.proxy.AdminLimitConfigProxyService;
import com.digicore.lucid.common.lib.limit.dto.LimitConfigDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.limit.service.LimitConfigService;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.validator.enums.Currency;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Feb-25(Tue)-2025
 */

@Service
@RequiredArgsConstructor
public class AdminLimitService implements AdminLimitConfigProxyService {
  private final LimitConfigService<LimitConfigDTO> adminLimitConfigService;

  public PaginatedResponseDTO<LimitConfigDTO> fetchLimitsConfigs(int pageNumber, int pageSize) {
    return adminLimitConfigService.retrieveLimitConfig(pageNumber, pageSize);
  }

  public LimitConfigDTO retrieveLimitConfig(
      LimitType limitType, Currency currency, boolean defaultLimit) {
    return adminLimitConfigService.retrieveLimitConfig(null, limitType, currency, defaultLimit);
  }

  @MakerChecker(
      checkerPermission = "approve-edit-admin-limit",
      makerPermission = "edit-admin-limit",
      requestClassName = LIMIT_CONFIG_DTO,
      activity = EDIT,
      module = LIMIT)
  public Object updateLimitConfig(Object initialData, Object updateData, Object... files) {
    LimitConfigDTO limitConfigDTO = (LimitConfigDTO) updateData;
    adminLimitConfigService.updateLimitConfig(limitConfigDTO);
    return Optional.empty();
  }
}
