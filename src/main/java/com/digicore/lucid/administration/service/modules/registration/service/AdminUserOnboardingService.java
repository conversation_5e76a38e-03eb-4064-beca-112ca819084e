/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.service.modules.registration.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.ONBOARD;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.USER;
import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.USER_REGISTRATION_DTO;
import static com.digicore.lucid.common.lib.notification.constant.MailTemplateConstant.ADMIN_INVITE;
import static com.digicore.lucid.common.lib.notification.constant.MailTemplateConstant.PASSWORD_UPDATE;

import com.digicore.lucid.administration.data.modules.service.AdminUserRegistrationService;
import com.digicore.lucid.administration.service.modules.registration.proxy.AdminUserOnboardingProxyService;
import com.digicore.lucid.common.lib.notification.service.NotificationDispatcher;
import com.digicore.lucid.common.lib.notification.util.NotificationHelper;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.registration.dto.UserRegistrationDTO;
import com.digicore.registhentication.authentication.dtos.request.ResetPasswordSecondBaseRequestDTO;
import com.digicore.registhentication.authentication.services.PasswordResetService;
import com.digicore.registhentication.util.IDGeneratorUtil;
import java.security.Principal;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Jan-28(Tue)-2025
 */

@Service
@RequiredArgsConstructor
public class AdminUserOnboardingService implements AdminUserOnboardingProxyService {
  private final AdminUserRegistrationService adminUserRegistrationService;
  private final NotificationDispatcher notificationDispatcher;
  private final NotificationHelper notificationHelper;
  private final PasswordResetService adminUserPasswordResetService;

  @MakerChecker(
      checkerPermission = "approve-invite-admin-user",
      makerPermission = "invite-admin-user",
      requestClassName = USER_REGISTRATION_DTO,
      activity = ONBOARD,
      module = USER)
  public Object onboardUser(Object initialData, Object requestDTO, Object... args) {
    UserRegistrationDTO userRegistrationDTO = (UserRegistrationDTO) requestDTO;
    userRegistrationDTO.setPassword(IDGeneratorUtil.generateTempId());
    UserProfileDTO result = adminUserRegistrationService.createProfile(userRegistrationDTO);
    notificationDispatcher.dispatchNotification(
        notificationHelper.buildOnboardingRequest(
            List.of(result.getEmail()),
            result.getPassword(),
            result.getAssignedRole(),
            result.getUsername(),
            result.getFirstName()),
        notificationHelper.getTemplateName(ADMIN_INVITE));
    return result;
  }

  public void updateDefaultPassword(
      ResetPasswordSecondBaseRequestDTO resetPasswordFirstBaseRequestDTO, Principal principal) {
    resetPasswordFirstBaseRequestDTO.setEmail(principal.getName());
    adminUserPasswordResetService.updateAccountPassword(resetPasswordFirstBaseRequestDTO);
    notificationDispatcher.dispatchNotification(
        notificationHelper.buildPasswordUpdateRequest(
            List.of(resetPasswordFirstBaseRequestDTO.getEmail())),
        notificationHelper.getTemplateName(PASSWORD_UPDATE));
  }
}
