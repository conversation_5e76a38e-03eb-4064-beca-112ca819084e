/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.service.modules.limit.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.limit.LimitSwaggerDocConstant.*;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.administration.service.modules.limit.service.AdminLimitFeignService;
import com.digicore.lucid.common.lib.limit.dto.LimitConfigDTO;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR>
 * @createdOn Mar-03(Mon)-2025
 */
@Hidden
@RestController
@RequestMapping(API_V1 + ADMIN_API + LIMIT_API)
@RequiredArgsConstructor
public class AdminLimitFeignController {
  private final AdminLimitFeignService adminLimitFeignService;

  @GetMapping(RETRIEVE_ALL_API)
  @PreAuthorize("hasAuthority('view-backoffice-limit')")
  public ResponseEntity<Object> fetchBankLimitConfigs() {
    return ControllerResponse.buildSuccessResponse(adminLimitFeignService.fetchLimitsConfigs());
  }

  @PostMapping(VALIDATE_API)
  @PreAuthorize("hasAuthority('view-backoffice-limit')")
  public ResponseEntity<Object> validateBankLimitUpdate(@RequestBody LimitConfigDTO request) {
    return ControllerResponse.buildSuccessResponse(
        adminLimitFeignService.validateLimitConfig(request));
  }
}
