/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.data.modules.limit.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.limit.constant.LimitConstant.*;
import static com.digicore.lucid.common.lib.util.ClientUtil.getPageable;
import static com.digicore.registhentication.util.PageableUtil.SORT_BY_CREATED_DATE;

import com.digicore.api.helper.response.ApiError;
import com.digicore.lucid.administration.data.modules.limit.model.AdminLimitConfig;
import com.digicore.lucid.administration.data.modules.limit.repository.AdminLimitConfigRepository;
import com.digicore.lucid.common.lib.limit.dto.LimitConfigDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.limit.service.LimitConfigService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.common.lib.util.MoneyUtil;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.validator.enums.Currency;
import java.math.BigDecimal;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> John
 * @createdOn Feb-25(Tue)-2025
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminLimitConfigService implements LimitConfigService<LimitConfigDTO> {
  private final AdminLimitConfigRepository adminLimitConfigRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  @Override
  public List<LimitConfigDTO> retrieveLimitConfig() {
    List<AdminLimitConfig> transactionLimits = adminLimitConfigRepository.findAll();
    return transactionLimits.stream().map(this::getLimitConfigDTO).toList();
  }

  @Override
  public PaginatedResponseDTO<LimitConfigDTO> retrieveLimitConfig(int pageNumber, int pageSize) {
    Pageable pageable = getPageable(pageNumber, pageSize, SORT_BY_CREATED_DATE);
    Page<AdminLimitConfig> transactionLimits = adminLimitConfigRepository.findAll(pageable);
    return getLimitPaginatedResponseDTO(transactionLimits);
  }

  @Override
  public LimitConfigDTO retrieveLimitConfig(
      String queryValue, LimitType limitType, Currency currency, boolean defaultLimit) {
    return getLimitConfigDTO(getAdminLimitConfig(limitType, currency, defaultLimit));
  }

  @Override
  public void updateLimitConfig(Set<LimitConfigDTO> newLimitConfigs) {
    Set<AdminLimitConfig> limitsToSave = new HashSet<>();

    List<AdminLimitConfig> existingConfigs = adminLimitConfigRepository.findAll();

    for (LimitConfigDTO newConfig : newLimitConfigs) {
      Optional<AdminLimitConfig> match =
          existingConfigs.stream()
              .filter(
                  existing ->
                      existing.getLimitType().equals(newConfig.getLimitType())
                          && existing.getCurrency().equals(newConfig.getCurrency())
                          && existing.isDefaultLimit() == newConfig.isDefaultLimit())
              .findFirst();

      if (match.isEmpty()) {
        AdminLimitConfig adminLimitConfig = new AdminLimitConfig();
        BeanUtilWrapper.copyNonNullProperties(newConfig, adminLimitConfig);
        adminLimitConfig.setLimitType(newConfig.getLimitType());
        adminLimitConfig.setCurrency(newConfig.getCurrency());
        adminLimitConfig.setUnlimited(false);
        limitsToSave.add(adminLimitConfig);
      }
    }

    adminLimitConfigRepository.saveAll(limitsToSave);
  }

  @Override
  public List<ApiError> validateLimitConfig(LimitConfigDTO updateRequest) {
    List<ApiError> apiErrors = new ArrayList<>();
    validateBackOfficeLimitConfig(updateRequest, apiErrors, false);
    return apiErrors;
  }

  @Override
  public List<ApiError> validateLimitConfig(LimitConfigDTO updateRequest, boolean unlimited) {
    List<ApiError> apiErrors = new ArrayList<>();
    validateBackOfficeLimitConfig(updateRequest, apiErrors, unlimited);
    return apiErrors;
  }

  private AdminLimitConfig getAdminLimitConfig(
      LimitType limitType, Currency currency, boolean defaultLimit) {
    return adminLimitConfigRepository
        .findFirstByLimitTypeAndCurrencyAndDefaultLimit(limitType, currency, defaultLimit)
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getLimitMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }

  @Override
  public LimitConfigDTO verifyLimitConfigExist(LimitConfigDTO limitConfigDTO) {
    LimitConfigDTO existingLimitConfig =
        getLimitConfigDTO(
            getAdminLimitConfig(
                limitConfigDTO.getLimitType(),
                limitConfigDTO.getCurrency(),
                limitConfigDTO.isDefaultLimit()));
    List<ApiError> apiErrors = new ArrayList<>();
    validateLimitConfig(limitConfigDTO, apiErrors);

    if (!ClientUtil.nullOrEmpty(apiErrors))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getLimitMessage(LIMIT_VIOLATION),
          HttpStatus.BAD_REQUEST,
          apiErrors);

    return existingLimitConfig;
  }

  @Override
  public void updateLimitConfig(LimitConfigDTO updateRequest) {
    AdminLimitConfig existingAdminLimitConfig =
        getAdminLimitConfig(
            updateRequest.getLimitType(),
            updateRequest.getCurrency(),
            updateRequest.isDefaultLimit());
    List<ApiError> apiErrors = new ArrayList<>();
    validateLimitConfig(updateRequest, apiErrors);

    if (!ClientUtil.nullOrEmpty(apiErrors))
      exceptionHandler.processCustomExceptions(
          messagePropertyConfig.getLimitMessage(LIMIT_VIOLATION),
          HttpStatus.BAD_REQUEST,
          apiErrors);

    BeanUtilWrapper.copyNonNullProperties(updateRequest, existingAdminLimitConfig);
    existingAdminLimitConfig.setUnlimited(false);
    adminLimitConfigRepository.save(existingAdminLimitConfig);
  }

  private void validateBackOfficeLimitConfig(
      LimitConfigDTO updateRequest, List<ApiError> apiErrors, boolean unlimited) {
    LimitValidationResult limitValidationResult = getLimitValidationResult(updateRequest);
    for (Map.Entry<String, BigDecimal> entry : limitValidationResult.updateConfigs().entrySet()) {
      String key = entry.getKey();

      enforceLimitRule(apiErrors, entry, key, limitValidationResult);

      if (!unlimited
          && entry.getValue().compareTo(limitValidationResult.globalConfigs().get(key)) >= 0) {
        apiErrors.add(new ApiError(getPrettyLimitViolationMessage(key)));
      }
    }
  }

  private static void enforceLimitRule(
      List<ApiError> apiErrors,
      Map.Entry<String, BigDecimal> entry,
      String key,
      LimitValidationResult limitValidationResult) {
    // Extract the prefix (Web/Mobile) and construct the cumulative key
    if (key.endsWith(SINGLE_CAP)) {
      String prefix = key.substring(0, key.indexOf(SINGLE_CAP)); // Extracts "Web" or "Mobile"
      String cumulativeKey =
          prefix + CUMULATIVE_CAP; // Constructs "WebCumulativeCap" or "MobileCumulativeCap"

      // Validate: Single Cap should not be greater than Cumulative Cap
      if (limitValidationResult
              .updateConfigs()
              .getOrDefault(cumulativeKey, BigDecimal.ZERO)
              .compareTo(entry.getValue())
          <= 0) {
        apiErrors.add(
            new ApiError(
                "The single "
                    + prefix.toLowerCase()
                    + " limit cannot be higher than the daily "
                    + prefix.toLowerCase()
                    + " limit."));
      }
    }
  }

  private LimitValidationResult getLimitValidationResult(LimitConfigDTO updateRequest) {
    AdminLimitConfig globalAdminLimitConfig =
        getAdminLimitConfig(updateRequest.getLimitType(), updateRequest.getCurrency(), false);
    Map<String, BigDecimal> updateConfigs =
        Map.of(
            WEB_SINGLE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(updateRequest.getMinorWebSingleCap()),
            MOBILE_SINGLE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(updateRequest.getMinorMobileSingleCap()),
            WEB_CUMULATIVE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(updateRequest.getMinorWebCumulativeCap()),
            MOBILE_CUMULATIVE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(updateRequest.getMinorMobileCumulativeCap()));

    Map<String, BigDecimal> globalConfigs =
        Map.of(
            WEB_SINGLE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(globalAdminLimitConfig.getMinorWebSingleCap()),
            MOBILE_SINGLE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(globalAdminLimitConfig.getMinorMobileSingleCap()),
            WEB_CUMULATIVE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(globalAdminLimitConfig.getMinorWebCumulativeCap()),
            MOBILE_CUMULATIVE_CAP,
            MoneyUtil.getAmountFromAmountInMinor(
                globalAdminLimitConfig.getMinorMobileCumulativeCap()));
    return new LimitValidationResult(updateConfigs, globalConfigs);
  }

  private record LimitValidationResult(
      Map<String, BigDecimal> updateConfigs, Map<String, BigDecimal> globalConfigs) {}

  private void validateLimitConfig(LimitConfigDTO updateRequest, List<ApiError> apiErrors) {
    LimitValidationResult limitValidationResult = getLimitValidationResult(updateRequest);
    for (Map.Entry<String, BigDecimal> entry : limitValidationResult.updateConfigs().entrySet()) {
      String key = entry.getKey();
      enforceLimitRule(apiErrors, entry, key, limitValidationResult);
      // Check global limit only if it's a default config
      if (updateRequest.isDefaultLimit()
          && entry.getValue().compareTo(limitValidationResult.globalConfigs().get(key)) >= 0) {
        apiErrors.add(new ApiError(getPrettyLimitViolationMessage(key)));
      }
    }
  }

  private LimitConfigDTO getLimitConfigDTO(AdminLimitConfig adminLimitConfig) {
    LimitConfigDTO limitConfigDTO = new LimitConfigDTO();
    BeanUtilWrapper.copyNonNullProperties(adminLimitConfig, limitConfigDTO);
    return limitConfigDTO;
  }

  private PaginatedResponseDTO<LimitConfigDTO> getLimitPaginatedResponseDTO(
      Page<AdminLimitConfig> transactionLimits) {
    return PaginatedResponseDTO.<LimitConfigDTO>builder()
        .content(transactionLimits.getContent().stream().map(this::getLimitConfigDTO).toList())
        .currentPage(transactionLimits.getNumber() + 1)
        .totalPages(transactionLimits.getTotalPages())
        .totalItems(transactionLimits.getTotalElements())
        .isFirstPage(transactionLimits.isFirst())
        .isLastPage(transactionLimits.isLast())
        .build();
  }
}
