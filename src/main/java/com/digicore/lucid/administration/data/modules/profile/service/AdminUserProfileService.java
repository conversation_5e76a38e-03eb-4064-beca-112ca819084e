/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.data.modules.profile.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.NOT_FOUND;

import com.digicore.lucid.administration.data.modules.profile.model.AdminUserProfile;
import com.digicore.lucid.administration.data.modules.profile.repository.AdminUserProfileRepository;
import com.digicore.lucid.common.lib.authentication.dto.UserAuthProfileDTO;
import com.digicore.lucid.common.lib.authentication.service.AuthProfileService;
import com.digicore.lucid.common.lib.authorization.dto.PermissionDTO;
import com.digicore.lucid.common.lib.profile.dto.LucidSearchRequest;
import com.digicore.lucid.common.lib.profile.dto.UserEditDTO;
import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.profile.service.UserProfileService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.BeanUtilWrapper;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import com.digicore.registhentication.registration.enums.Status;
import jakarta.transaction.Transactional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Jan-22(Wed)-2025
 */

@Service
@RequiredArgsConstructor
@Transactional
public class AdminUserProfileService implements UserProfileService<UserProfileDTO> {
  private final AdminUserProfileRepository adminUserProfileRepository;
  private final AuthProfileService<UserAuthProfileDTO> adminAuthProfileService;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;

  @Override
  public boolean profileExists(String email) {
    return adminUserProfileRepository.existsByEmail(email);
  }

  @Override
  public void editUserProfile(UserEditDTO userEditDTO) {
    AdminUserProfile adminUserProfileToUpdate = this.retrieveProfile(userEditDTO.getEmail());
    BeanUtilWrapper.copyNonNullProperties(userEditDTO, adminUserProfileToUpdate);
    adminUserProfileRepository.save(adminUserProfileToUpdate);
    UserAuthProfileDTO userAuthProfileDTO = new UserAuthProfileDTO();
    userAuthProfileDTO.setUsername(userEditDTO.getEmail());
    if (userEditDTO.getPermissions() != null)
      userAuthProfileDTO.setPermissions(
          userEditDTO.getPermissions().stream()
              .map(
                  x -> {
                    PermissionDTO permissionDTO = new PermissionDTO();
                    permissionDTO.setName(x);
                    return permissionDTO;
                  })
              .collect(Collectors.toSet()));
    userAuthProfileDTO.setPassword(null);
    userAuthProfileDTO.setAssignedRole(userEditDTO.getAssignedRole());
    userAuthProfileDTO.setSystemInitiated(userEditDTO.isSystemInitiated());
    adminAuthProfileService.updateAuthProfile(userAuthProfileDTO);
  }

  @Override
  public UserProfileDTO retrieveLoggedInUserProfile() {
    return null;
  }

  @Override
  public PaginatedResponseDTO<UserProfileDTO> retrieveAllUserProfiles(
      int pageNumber, int pageSize) {
    return null;
  }

  @Override
  public PaginatedResponseDTO<UserProfileDTO> filterOrSearch(
      LucidSearchRequest lucidSearchRequest) {
    return null;
  }

  // @Override
  public PaginatedResponseDTO<UserProfileDTO> filterOrSearch(
      LucidSearchRequest lucidSearchRequest, String organization) {
    return null;
  }

  @Override
  public void deleteUserProfile(String email) {}

  @Override
  public UserProfileDTO retrieveUserProfile(String email) {
    return null;
  }

  @Override
  public void enableUserProfile(String email) {
    adminAuthProfileService.updateAuthProfileStatus(email, Status.ACTIVE);
  }

  @Override
  public void disableUserProfile(String email) {
    adminAuthProfileService.updateAuthProfileStatus(email, Status.DEACTIVATED);
  }

  @Override
  public void profileExistenceCheckByEmail(String email) {}

  private AdminUserProfile retrieveProfile(String email) {
    return adminUserProfileRepository
        .findFirstByEmailOrderByCreatedDate(email)
        .orElseThrow(
            () ->
                exceptionHandler.processCustomException(
                    messagePropertyConfig.getUserMessage(NOT_FOUND), HttpStatus.BAD_REQUEST));
  }
}
