/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.administration.data.modules.authorization.repository;

import static com.digicore.lucid.common.lib.constant.payload.PayloadClassNameConstant.PERMISSION_DTO;

import com.digicore.lucid.administration.data.modules.authorization.model.AdminUserPermission;
import com.digicore.lucid.common.lib.authorization.dto.PermissionDTO;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

/*
 * <AUTHOR>
 * @createdOn Jan-22(Wed)-2025
 */

public interface AdminUserPermissionRepository extends JpaRepository<AdminUserPermission, Long> {

  @Query("SELECT new " + PERMISSION_DTO + "(p.name) FROM AdminUserPermission p")
  Set<PermissionDTO> retrieveAllPermissionName();

  Optional<AdminUserPermission> findFirstByNameOrderByCreatedDate(String name);
}
