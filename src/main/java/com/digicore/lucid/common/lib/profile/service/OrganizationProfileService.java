/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.profile.service;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.profile.dto.BankAccountOfficerDTO;
import com.digicore.lucid.common.lib.profile.dto.BankProductCodeDTO;
import com.digicore.lucid.common.lib.profile.dto.BankProfileDTOWithBankUsers;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import java.util.List;

/*
 * <AUTHOR>
 * @createdOn Feb-04(Tue)-2025
 */

public interface OrganizationProfileService<T> {

  default void setOrganizationIdAndCbaTokenAndProvider(
      RequestContextHolder.RequestContext requestContext) {}

  default RequestContextHolder.RequestContext getCbaTokenAndProvider(String subDomain) {
    return null;
  }

  default void setOrganizationIdAndCbaTokenAndProvider(
      RequestContextHolder.RequestContext requestContext, String username) {}

  default String getCbaProvider(String subDomain) {
    return null;
  }

  default String getOrganizationId(String subDomain) {
    return null;
  }

  default String getOrganizationName(String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default String getOrganizationNameBySubDomain(String subDomain) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default T retrieveProfile() {
    return null;
  }

  default LimitType retrieveProfileLimitType(String organizationId) {
    return null;
  }

  default String retrieveProfileAddress(String organizationId) {
    return null;
  }

  default T retrieveProfile(String organizationId) {
    return null;
  }

  default T retrieveProfile(String bankOrganizationId, String organizationId) {
    return null;
  }

  default PaginatedResponseDTO<T> retrieveProfiles(
      String bankOrganizationId, int pageNumber, int pageSize) {
    return null;
  }

  default PaginatedResponseDTO<T> retrieveProfiles(int pageNumber, int pageSize) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default PaginatedResponseDTO<BankProfileDTOWithBankUsers> retrieveAllProfiles(
      int pageNumber, int pageSize) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default void enableProfile(String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default void disableProfile(String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default List<BankAccountOfficerDTO> retrieveAccountOfficerCodes() {
    return List.of();
  }

  default List<BankProductCodeDTO> retrieveProductCodes() {
    return List.of();
  }

  default void toggleProfileMode(T request) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default boolean emailProfileExists(String email, String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default boolean phoneProfileExists(String phoneNumber, String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }
}
