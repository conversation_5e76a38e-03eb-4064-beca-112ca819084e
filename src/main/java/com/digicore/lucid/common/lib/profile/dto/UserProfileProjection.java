/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.profile.dto;

/*
 * <AUTHOR>
 * @createdOn Jan-28(Tue)-2025
 */

import com.digicore.registhentication.registration.enums.Status;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserProfileProjection {
  public UserProfileProjection(String firstName, String lastName) {
    this.firstName = firstName;
    this.lastName = lastName;
  }

  public UserProfileProjection(Status status) {
    this.status = status;
  }

  public UserProfileProjection(String firstName, String organizationId, String bankOrganizationId) {
    this.firstName = firstName;
    this.organizationId = organizationId;
    this.bankOrganizationId = bankOrganizationId;
  }

  public UserProfileProjection(
      String username, String assignedRole, String profileId, Status status) {
    this.username = username;
    this.assignedRole = assignedRole;
    this.status = status;
    this.profileId = profileId;
  }

  public UserProfileProjection(
      String firstName, String lastName, String email, Status status, String profileId) {
    this.firstName = firstName;
    this.lastName = lastName;
    this.email = email;
    this.status = status;
    this.profileId = profileId;
  }

  public UserProfileProjection(
      String username,
      Status status,
      String assignedRole,
      String bankOrganizationId,
      String organizationId) {
    this.username = username;
    this.assignedRole = assignedRole;
    this.status = status;
    this.bankOrganizationId = bankOrganizationId;
    this.organizationId = organizationId;
  }

  public UserProfileProjection(
      String username,
      String firstName,
      String lastName,
      String email,
      String assignedRole,
      String phoneNumber,
      Status status,
      LocalDateTime lastLoginDate) {
    this.username = username;
    this.firstName = firstName;
    this.lastName = lastName;
    this.email = email;
    this.assignedRole = assignedRole;
    this.phoneNumber = phoneNumber;
    this.status = status;
    this.lastLoginDate = lastLoginDate;
  }

  public UserProfileProjection(
      String username, String firstName, String lastName, String email, String phoneNumber) {
    this.username = username;
    this.firstName = firstName;
    this.lastName = lastName;
    this.email = email;
    this.phoneNumber = phoneNumber;
  }

  public UserProfileProjection(
      String username,
      String firstName,
      String lastName,
      String email,
      String assignedRole,
      Status status,
      LocalDateTime lastLoginDate) {
    this.username = username;
    this.firstName = firstName;
    this.lastName = lastName;
    this.email = email;
    this.assignedRole = assignedRole;
    this.status = status;
    this.lastLoginDate = lastLoginDate;
  }

  private String username;
  private String firstName;
  private String lastName;
  private String email;
  private LocalDateTime lastLoginDate;
  private String assignedRole;
  private Status status;
  private String organizationId;
  private String bankOrganizationId;
  private String cbaProvider;
  private String cbaToken;
  private String profileId;
  private String phoneNumber;
}
