/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.util;

import com.digicore.lucid.common.lib.profile.dto.UserProfileDTO;
import com.digicore.lucid.common.lib.registration.dto.UserRegistrationDTO;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;

/*
 * <AUTHOR>
 * @createdOn Jan-22(Wed)-2025
 */

public class RegistrationUtil {
  public static UserProfileDTO getUserProfileDTO(UserRegistrationDTO registrationRequest) {
    UserProfileDTO profileDto = new UserProfileDTO();
    BeanUtilWrapper.copyNonNullProperties(registrationRequest, profileDto);
    profileDto.setPassword(registrationRequest.getPassword());
    profileDto.setPin(null);
    return profileDto;
  }

  public static List<UserProfileDTO> getUserProfileDTO(
      List<UserRegistrationDTO> registrationRequest) {
    List<UserProfileDTO> userProfileDTOS = new ArrayList<>();
    registrationRequest.forEach(
        request -> {
          UserProfileDTO profileDto = new UserProfileDTO();
          BeanUtilWrapper.copyNonNullProperties(request, profileDto);
          profileDto.setPassword(request.getPassword());
          profileDto.setPin(null);
          userProfileDTOS.add(profileDto);
        });

    return userProfileDTOS;
  }

  public static String generateProfileId() {
    return LocalDateTime.now()
        .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
        .concat(RandomStringUtils.secure().nextAlphanumeric(5))
        .toUpperCase();
  }
}
