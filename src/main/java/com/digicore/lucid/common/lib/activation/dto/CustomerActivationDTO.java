/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.activation.dto;

import com.digicore.lucid.common.lib.activation.enums.CustomerActivationStatus;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerBusinessDetailDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn Feb-17(Mon)-2025
 */

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class CustomerActivationDTO {
  private String organizationId;
  private String bankOrganizationId;
  private CustomerBusinessDetailDTO customerBusinessDetailDTO;
  private List<CustomerUserDetailDTO> customerUserDetailDTO;
  private CustomerAccountPreferenceDTO customerAccountPreferenceDTO;
  private CustomerServiceRequiredDTO customerServiceRequiredDTO;
  private CustomerUploadedDocumentDTO customerUploadedDocumentDTO;
  private CustomerAgreementDTO customerAgreementDTO;
  private CustomerActivationStatus customerActivationStatus;
  private String accountOfficerCode;
  private String comment;
}
