/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.registration.dto.customer;

import lombok.Getter;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn Feb-15(Sat)-2025
 */

@Getter
@Setter
public class CustomerDocumentDTO {
  private String documentType;
  private String documentNumber;
  private String issueDate;
  private String expiryDate;
  private String issueCountry;
  private boolean nonExpiry;
  private String uploadedDocumentId;
}
