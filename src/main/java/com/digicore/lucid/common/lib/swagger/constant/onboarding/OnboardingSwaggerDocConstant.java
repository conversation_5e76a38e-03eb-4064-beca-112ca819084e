/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.swagger.constant.onboarding;

import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;

/*
 * <AUTHOR>
 * @createdOn Jan-28(<PERSON><PERSON>)-2025
 */

public class OnboardingSwaggerDocConstant {
  public static final String ONBOARDING_API = "onboarding/process/";
  public static final String EXISTING_ACCOUNT_ONBOARDING_API = ONBOARDING_API + "account/";
  public static final String SELF_ONBOARDING_API = ONBOARDING_API + "self/";
  public static final String BANK_CBA_PROVIDER_API = "provider/" + RETRIEVE_API;
  public static final String BANK_CBA_PROVIDER_INFO_API = "provider/" + RETRIEVE_ALL_API;
  public static final String BANK_ORGANIZATION_ID_API = RETRIEVE_API + "/organization-id";
  public static final String INVITE_API = "user-invitation";
  public static final String RESEND_INVITE_API = "resend-user-invitation";
  public static final String BVN_VALIDATION_API = "validate-bvn";
  public static final String SEND_OTP_TO_BVN_PHONE_NUMBER_API = "send-bvn-verification-code";
  public static final String SEND_OTP_TO_MAIL_API = "send-mail-verification-code";
  public static final String VERIFY_OTP_MAIL_API = "mail-verification-code";

  public static final String ONBOARDING_CONTROLLER_TITLE = "User-Onboarding-Module";
  public static final String ONBOARDING_CONTROLLER_DESCRIPTION =
      "This module contains all required APIs to invite new users into the system.";
  public static final String CUSTOMER_ONBOARDING_CONTROLLER_TITLE = "Customer-Onboarding-Module";
  public static final String ONBOARDING_EXISTING_CONTROLLER_DESCRIPTION =
      "This module contains all required APIs to onboard existing into the system.";
  public static final String BANK_ONBOARDING_CONTROLLER_TITLE = "Bank-Onboarding-Module";
  public static final String BANK_ONBOARDING_CONTROLLER_DESCRIPTION =
      "This module contains all required APIs to create new banks in the system.";
  public static final String ONBOARDING_CONTROLLER_INVITE_USER_TITLE =
      "Invite a user to the system, this module goes through maker checker process.";
  public static final String ONBOARDING_CONTROLLER_INVITE_USER_DESCRIPTION =
      "This API is used to invite a user to the system, it sends an invite mail to the user.";
  public static final String BANK_ONBOARDING_CONTROLLER_INVITE_USER_TITLE =
      "Create a bank, this module goes through maker checker process.";
  public static final String BANK_ONBOARDING_CONTROLLER_INVITE_USER_DESCRIPTION =
      "This API is used to create a bank, it sends an invite mail to the users.";
  public static final String CUSTOMER_ONBOARDING_CONTROLLER_INVITE_USER_TITLE =
      "Onboard a customer, this module goes through maker checker process.";
  public static final String CUSTOMER_ONBOARDING_CONTROLLER_INVITE_USER_DESCRIPTION =
      "This API is used to onboard a customer, it sends an invite mail to the users.";
  public static final String CUSTOMER_VALIDATION_CONTROLLER_INVITE_USER_TITLE =
      "Fetch customer details.";
  public static final String CUSTOMER_VALIDATION_CONTROLLER_INVITE_USER_DESCRIPTION =
      "This API is used to fetch the customer details.";
  public static final String ONBOARDING_CONTROLLER_RESET_DEFAULT_PASSWORD_TITLE =
      "Reset default password";
  public static final String ONBOARDING_CONTROLLER_RESET_DEFAULT_PASSWORD_DESCRIPTION =
      "This API is used to reset the default password of a new user, it requires a resetKey that can be found in the "
          + "access token after login";
  public static final String ONBOARDING_CONTROLLER_BVN_VALIDATION_TITLE = "Validate customer bvn";
  public static final String ONBOARDING_CONTROLLER_BVN_VALIDATION_DESCRIPTION =
      "This API is used to validate customer bvn";
  public static final String ONBOARDING_CONTROLLER_BVN_SEND_OTP_BVN_TITLE =
      "Send otp to customer bvn registered phone number";
  public static final String ONBOARDING_CONTROLLER_BVN_SEND_OTP_BVN_DESCRIPTION =
      "This API is used to send otp to the customer supplied bvn registered phone number";
  public static final String ONBOARDING_CONTROLLER_SEND_OTP_EMAIL_TITLE =
      "Send otp to customer email";
  public static final String ONBOARDING_CONTROLLER_SEND_OTP_EMAIL_DESCRIPTION =
      "This API is used to send otp to the customer supplied email";
  public static final String ONBOARDING_CONTROLLER_VALIDATE_OTP_EMAIL_TITLE =
      "Validate otp sent to customer email";
  public static final String ONBOARDING_CONTROLLER_VALIDATE_OTP_EMAIL_DESCRIPTION =
      "This API is used to validate otp sent to the customer supplied email";
  public static final String CUSTOMER_ADD_EXISTING_ACCOUNT_TITLE = "Add customer Existing account";
  public static final String CUSTOMER_ADD_EXISTING_ACCOUNT_DESCRIPTION =
      "This API is used to add a customer's existing account  to it's profile";
}
