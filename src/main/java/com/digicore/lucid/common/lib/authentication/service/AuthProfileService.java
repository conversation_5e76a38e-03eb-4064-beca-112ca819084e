/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.authentication.service;

/*
 * <AUTHOR>
 * @createdOn Jan-22(Wed)-2025
 */

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;
import com.digicore.registhentication.registration.enums.Status;
import java.util.List;

public interface AuthProfileService<T> {
  <V, K> void saveNewAuthProfile(K registrationRequest, V userProfile);

  default <V, K> void saveNewAuthProfile(List<K> registrationRequest, List<V> userProfile) {}

  default T retrieveAuthProfile(String username, String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  T retrieveAuthProfile(String username, boolean isForPasswordReset);

  default List<T> retrieveAuthProfile(
      List<Long> ids, String organizationId, String bankOrganizationId) {
    return List.of();
  }

  default void updateAuthProfileStatus(String email, Status status) {}

  default PaginatedResponseDTO<T> retrieveUserProfile(
      String organizationId, String bankOrganizationId, int pageNumber, int pageSize) {
    return null;
  }

  default T retrieveUserProfile(String userName, String bankOrganizationId, String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default void enableUserProfile(String userName, String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default void disableUserProfile(String userName, String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  void updateAuthProfile(T authProfile);

  default void updateUsernameAndRole(
      String username, String newUsername, String assignedRole, String organizationId) {}

  void updateAuthProfilePassword(T authProfile);

  default void updateAuthProfilePassword(T authProfile, String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  <V> V retrieveAuthProfileEntity(String email);

  default <V> V retrieveAuthProfileEntity(String organizationId, String email) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default boolean authProfileExists(String username, String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default boolean authProfileExist(String username, String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }
}
