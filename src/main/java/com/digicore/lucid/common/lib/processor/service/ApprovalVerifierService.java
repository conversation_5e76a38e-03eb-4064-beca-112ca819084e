/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.processor.service;

import static com.digicore.lucid.common.lib.util.ClientUtil.getObjectMapper;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.api.helper.response.ApiError;
import com.digicore.lucid.common.lib.approval.dto.ApprovalResponseDTO;
import com.digicore.lucid.common.lib.approval.enums.ApprovalRequestStatus;
import com.digicore.lucid.common.lib.approval.model.ApprovalRequest;
import com.digicore.lucid.common.lib.approval.repository.ApprovalRequestRepository;
import com.digicore.lucid.common.lib.approval.rule.service.ApprovalRuleService;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.processor.annotation.MakerChecker;
import com.digicore.lucid.common.lib.util.ClientUtil;
import jakarta.transaction.Transactional;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-22(Sat)-2025
 */

@Transactional
@Service
@RequiredArgsConstructor
@Slf4j
public class ApprovalVerifierService {
  private final ApprovalRequestRepository approvalRequestRepository;
  private final ApprovalRuleService approvalRuleService;
  private final RegulatoryLoggingService regulatoryLoggingService;

  public ApprovalResponseDTO triggerActualMethodCall(
      ProceedingJoinPoint joinPoint,
      String checker,
      MakerChecker makerChecker,
      ApprovalRequest approvalRequest) {
    ApprovalResponseDTO approvalResponseDTO =
        getApprovalResponseDTO(joinPoint, checker, makerChecker, approvalRequest);
    regulatoryLoggingService.log(
        ClientUtil.getValueFromAccessToken("role"),
        ClientUtil.getValueFromAccessToken("email"),
        ClientUtil.getValueFromAccessToken("name"),
        "APPROVE",
        makerChecker.module(),
        approvalRequest);

    return approvalResponseDTO;
  }

  @Async("approvalExecutor")
  public void triggerActualMethodCallSilently(
      ProceedingJoinPoint joinPoint,
      String checker,
      MakerChecker makerChecker,
      ApprovalRequest approvalRequest,
      RequestContextHolder.RequestContext requestContext,
      SecurityContext securityContext,
      String ipAddress) {
    log.info(
        "<<< processing approval request {},--> {} >>>",
        approvalRequest.getId(),
        approvalRequest.getApprovalRequestType());
    RequestContextHolder.set(requestContext);
    SecurityContextHolder.setContext(securityContext);
    getApprovalResponseDTO(joinPoint, checker, makerChecker, approvalRequest);
    regulatoryLoggingService.log(
        ipAddress,
        ClientUtil.getValueFromAccessToken("role"),
        ClientUtil.getValueFromAccessToken("email"),
        ClientUtil.getValueFromAccessToken("name"),
        "APPROVE",
        makerChecker.module(),
        approvalRequest);
    log.info(
        "<<< completed processing approval request {},--> {} >>>",
        approvalRequest.getId(),
        approvalRequest.getApprovalRequestType());
  }

  private ApprovalResponseDTO getApprovalResponseDTO(
      ProceedingJoinPoint joinPoint,
      String checker,
      MakerChecker makerChecker,
      ApprovalRequest approvalRequest) {
    Object[] args = getMethodActualArgs(joinPoint, makerChecker, approvalRequest);

    try {
      if (args.length > 0) joinPoint.proceed(args);
      else joinPoint.proceed();
    } catch (Throwable e) {

      if (e instanceof ZeusRuntimeException zeusRuntimeException) {
        // Already a ZeusRuntimeException, re-throw directly
        throw zeusRuntimeException;
      } else {
        log.error(
            "<<< error occurred while processing approval, see message : {} >>>", e.getMessage());
        throw new ZeusRuntimeException(
            HttpStatus.BAD_REQUEST, new ApiError(e.getMessage(), "MKT_003"));
      }
    }
    approvalRequest.setApproved(true);
    approvalRequest.setApprovalUsername(checker);
    approvalRequest.setApprovedDate(LocalDateTime.now());
    approvalRequest.setStatus(ApprovalRequestStatus.EXECUTED);

    approvalRequest.setApprovalName(ClientUtil.getValueFromAccessToken("name"));

    approvalRequestRepository.save(approvalRequest);

    return getApprovalResponse();
  }

  private static Object[] getMethodActualArgs(
      ProceedingJoinPoint joinPoint, MakerChecker makerChecker, ApprovalRequest approvalRequest) {
    try {

      if (makerChecker.requestClassName() != null && !makerChecker.requestClassName().isBlank()) {
        Class<?> c = Class.forName(makerChecker.requestClassName());
        Object requestArg = null;

        if (approvalRequest.getDataToUpdate() != null
            && !approvalRequest.getDataToUpdate().isEmpty()) {
          requestArg = getObjectMapper().readValue(approvalRequest.getDataToUpdate(), c);
        }

        Object[] args = joinPoint.getArgs();

        args[1] = requestArg;
        return args;
      }
      return new Object[0];

    } catch (Exception e) {
      throw new ZeusRuntimeException(
          HttpStatus.BAD_REQUEST, new ApiError(e.getMessage(), "MKT_004"));
    }
  }

  private ApprovalResponseDTO getApprovalResponse() {
    return ApprovalResponseDTO.builder()
        .description("Request treated successfully")
        .requestStatus(ApprovalRequestStatus.TREATED)
        .build();
  }
}
