/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.registration.dto.customer;

import com.digicore.lucid.common.lib.registration.dto.UserRegistrationDTO;
import com.digicore.lucid.common.lib.registration.enums.SignUpType;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn Feb-10(Mon)-2025
 */

@Getter
@Setter
public class CustomerPersonalDetailDTO extends UserRegistrationDTO {
  private SignUpType signUpType;
  private String title;
  private String dob;
  private String address;
  private String city;
  private String state;
  private String country;
  private String stateOfOrigin;
  private String motherMaidenName;
  private String bvn;
  private List<CustomerDocumentDTO> customerDocuments;
}
