package com.digicore.lucid.common.lib.swagger.constant.logonManagement;

public class LogonManagementSwaggerDocConstant {
  public static final String LOGON_MANAGEMENT_API = "user-logon-management/";
  public static final String LOGIN_ATTEMPTS_API = "attempts/";
  public static final String LOGON_MANAGEMENT_TITLE = "Logon-Management-Module";
  public static final String LOGON_MANAGEMENT_CONTROLLER_DESCRIPTION =
      "This module contains all required APIs to manage user login activities";
  public static final String LOGON_MANAGEMENT_CONTROLLER_RETRIEVE_ALL_TITLE =
      "Get all users login activities";
  public static final String LOGON_MANAGEMENT_CONTROLLER_RETRIEVE_ALL_DESCRIPTION =
      "This Api is used to get all users login activities";
  public static final String LOGON_MANAGEMENT_CONTROLLER_FILTER_TITLE =
      "filter login activities by username";
  public static final String LOGON_MANAGEMENT_CONTROLLER_FILTER_DESCRIPTION =
      "This Api is used to filter login activities by username";
  public static final String LOCK_USER_TITLE = "lock a user's account";
  public static final String LOCK_USER_DESCRIPTION = "This Api is used to lock a user's account";
}
