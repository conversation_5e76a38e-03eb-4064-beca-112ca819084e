/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.limit.constant;

import java.util.Map;

/*
 * <AUTHOR>
 * @createdOn Mar-10(Mon)-2025
 */

public class LimitConstant {
  public static final String CUSTOMER = "Customer";
  public static final String PROFILE = "Profile";
  public static final String DEFAULT = "Default";
  public static final String GLOBAL = "Global";
  public static final String SINGLE_CAP = "SingleCap";
  public static final String CUMULATIVE_CAP = "CumulativeCap";
  public static final String SINGLE_CAP_MESSAGE = "SingleCappedMessage";
  public static final String CUMULATIVE_CAP_MESSAGE = "CumulativeCappedMessage";
  public static final String WEB_SINGLE_CAP = "WebSingleCap";
  public static final String MOBILE_SINGLE_CAP = "MobileSingleCap";
  public static final String WEB_CUMULATIVE_CAP = "WebCumulativeCap";
  public static final String MOBILE_CUMULATIVE_CAP = "MobileCumulativeCap";
  public static final String PRETTY_WEB_SINGLE_CAP_MESSAGE =
      "The single web transfer limit you entered exceeds the maximum allowed single global limit.";
  public static final String PRETTY_MOBILE_SINGLE_CAP_MESSAGE =
      "The single mobile transfer limit you entered exceeds the maximum allowed single global limit.";
  public static final String PRETTY_WEB_CUMULATIVE_CAP_MESSAGE =
      "The daily web transfer limit you entered exceeds the maximum allowed daily global limit.";
  public static final String PRETTY_MOBILE_CUMULATIVE_CAP_MESSAGE =
      "The daily mobile transfer limit you entered exceeds the maximum allowed daily global limit.";

  private static final Map<String, String> PRETTY_MESSAGE_MAP;

  static {
    PRETTY_MESSAGE_MAP =
        Map.of(
            WEB_SINGLE_CAP,
            PRETTY_WEB_SINGLE_CAP_MESSAGE,
            MOBILE_SINGLE_CAP,
            PRETTY_MOBILE_SINGLE_CAP_MESSAGE,
            WEB_CUMULATIVE_CAP,
            PRETTY_WEB_CUMULATIVE_CAP_MESSAGE,
            MOBILE_CUMULATIVE_CAP,
            PRETTY_MOBILE_CUMULATIVE_CAP_MESSAGE);
  }

  public static String getPrettyLimitViolationMessage(String key) {
    return PRETTY_MESSAGE_MAP.getOrDefault(key, key + " has been violated.");
  }
}
