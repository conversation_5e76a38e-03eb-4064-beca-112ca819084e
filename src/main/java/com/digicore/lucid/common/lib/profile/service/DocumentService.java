/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.profile.service;

import com.digicore.api.helper.exception.ZeusRuntimeException;

/*
 * <AUTHOR>
 * @createdOn Mar-11(Tue)-2025
 */

public interface DocumentService<T> {
  default T retrieveDocument(String fileId, String subDomainName) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default String retrieveDocumentFilePath(String fileId, String subDomainName) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default T retrieveDocument(String fileId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default boolean documentExist(String fileId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }
}
