/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.util;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.BiFunction;
import java.util.regex.Pattern;
import lombok.SneakyThrows;

/*
 * <AUTHOR>
 * @createdOn Apr-15(Tue)-2025
 */

public class FieldValidatorUtil {

  private static final Map<String, BiFunction<Object, Object, String>> ruleHandlers =
      new HashMap<>();

  static {
    registerRuleHandlers();
  }

  private static void registerRuleHandlers() {
    ruleHandlers.put(
        "notEmpty",
        (value, expected) -> {
          if (value == null || (value instanceof String s && s.trim().isEmpty())) {
            return "must not be empty";
          }
          return null;
        });

    ruleHandlers.put("required", (value, expected) -> (value == null) ? "is required" : null);

    ruleHandlers.put(
        "size.min",
        (value, expected) -> {
          if (value instanceof Collection<?> col) {
            int min = (int) expected;
            return col.size() < min ? "must contain at least " + min + " item(s)" : null;
          }
          return "is not a collection";
        });

    ruleHandlers.put(
        "pattern",
        (value, expected) -> {
          if (value instanceof String s) {
            return Pattern.matches(expected.toString(), s) ? null : "does not match pattern";
          }
          return "is not a string";
        });

    ruleHandlers.put(
        "numeric.min",
        (value, expected) -> {
          if (value instanceof Number num) {
            return num.doubleValue() < Double.parseDouble(expected.toString())
                ? "must be >= " + expected
                : null;
          }
          return "is not numeric";
        });

    ruleHandlers.put(
        "numeric.max",
        (value, expected) -> {
          if (value instanceof Number num) {
            return num.doubleValue() > Double.parseDouble(expected.toString())
                ? "must be <= " + expected
                : null;
          }
          return "is not numeric";
        });
  }

  public static List<String> validateRules(Object obj, Map<String, Object> rules) {
    List<String> violations = new ArrayList<>();

    for (Map.Entry<String, Object> entry : rules.entrySet()) {
      String ruleKey = entry.getKey();
      Object expected = entry.getValue();

      int lastDot = ruleKey.lastIndexOf('.');
      if (lastDot == -1) continue;

      String fieldPath = ruleKey.substring(0, lastDot);
      String rulePath = ruleKey.substring(lastDot + 1);

      Object value = getValueAtPath(obj, fieldPath);
      BiFunction<Object, Object, String> handler = ruleHandlers.get(rulePath);

      if (handler != null) {
        String message = handler.apply(value, expected);
        if (message != null) violations.add(fieldPath + " " + message);
      }
    }

    return violations;
  }

  public static List<String> checkSpecificFields(Object obj, List<String> fieldsToCheck)
      throws IllegalAccessException, NoSuchFieldException {
    List<String> emptyFields = new ArrayList<>();
    for (String path : fieldsToCheck) {
      Object value = getValueAtPath(obj, path);
      if (value == null
          || (value instanceof String string && string.trim().isEmpty())
          || (value instanceof Integer integer && String.valueOf(integer).trim().isEmpty())) {
        emptyFields.add(path);
      }
    }
    return emptyFields;
  }

  @SneakyThrows
  private static Object getValueAtPath(Object obj, String path) {
    String[] tokens = path.split("\\.");
    Object current = obj;

    for (String token : tokens) {
      if (current == null) return null;

      if (token.matches("(.+)\\[(\\d+)]")) { // handle collections
        String fieldName = token.substring(0, token.indexOf("["));
        int index = Integer.parseInt(token.replaceAll(".*\\[(\\d+)]", "$1"));

        Field field = getField(current.getClass(), fieldName);
        field.setAccessible(true);
        Object collection = field.get(current);

        if (collection instanceof List<?> list) {
          if (index >= list.size()) return null;
          current = list.get(index);
        } else {
          return null;
        }
      } else {
        Field field = getField(current.getClass(), token);
        field.setAccessible(true);
        current = field.get(current);
      }
    }

    return current;
  }

  // Helper method to get field including inherited fields
  private static Field getField(Class<?> clazz, String fieldName) throws NoSuchFieldException {
    try {
      return clazz.getDeclaredField(fieldName);
    } catch (NoSuchFieldException e) {
      Class<?> superClass = clazz.getSuperclass();
      if (superClass == null) {
        throw e;
      } else {
        return getField(superClass, fieldName);
      }
    }
  }
}
