/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.registration.dto.customer;

import lombok.Getter;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn Feb-10(Mon)-2025
 */

@Getter
@Setter
public class CustomerRegistrationDTO {
  private String registrationCode;
  private String bankOrganizationId;
  private String subDomain;
  private CustomerPersonalDetailDTO customerPersonalDetailDTO;
  private CustomerBusinessDetailDTO customerBusinessDetailDTO;
}
