/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.spend_analysis.dto;

import com.digicore.lucid.common.lib.transaction.enums.TransferSpendCategory;

/*
 * <AUTHOR>
 * @createdOn 03/06/2025
 */
public interface CategorySumProjection {
  public Long getSumSpentInMinor();

  public TransferSpendCategory getTransferSpendCategory();
}
