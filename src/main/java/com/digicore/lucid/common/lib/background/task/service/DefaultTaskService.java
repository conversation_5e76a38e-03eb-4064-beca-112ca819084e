/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.background.task.service;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.lucid.common.lib.background.task.TaskService;
import com.digicore.lucid.common.lib.background.task.model.Task;
import com.digicore.lucid.common.lib.background.task.repository.TaskRepository;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @createdOn May-02(Fri)-2025
 */

@EntityScan("com.digicore.lucid.common.lib.background.task.model")
@EnableJpaRepositories(basePackages = {"com.digicore.lucid.common.lib.background.task.repository"})
@Service
@RequiredArgsConstructor
public class DefaultTaskService implements TaskService<Task> {
  private final TaskRepository taskRepository;

  public boolean taskExists(String key) {
    Task task = this.getTaskByKey(key);
    return task != null;
  }

  public void createTask(String key, String description) {
    Task task = new Task();
    task.setTaskKey(key);
    task.setTaskDescription(description);
    task.setRunning(false);
    task.setLastRan(null);
    task.setCreatedOn(LocalDateTime.now());
    this.save(task);
  }

  public void startTask(String key, String service) {
    Task task = this.getTaskByKey(key);
    if (!task.isRunning()) {
      task.setRunning(true);
      task.setServiceRunning(service);
      this.save(task);
    } else {
      throw new ZeusRuntimeException("task with key " + key + " is already running.");
    }
  }

  public void stopTask(String key) {
    Task task = this.getTaskByKey(key);
    if (task.isRunning()) {
      task.setRunning(false);
      task.setLastRan(LocalDateTime.now());
      this.save(task);
    } else {
      throw new ZeusRuntimeException("task with key " + key + " has already stopped.");
    }
  }

  public void updateTask(Task task) {
    this.save(task);
  }

  public void onRestartOfServiceCheckIfTaskWasTerminatedUnexpectedly(
      String taskKey, String service) {
    Task task = this.getTaskWithoutLockingDB(taskKey, service);
    if (task != null && task.getServiceRunning().equalsIgnoreCase(service) && task.isRunning()) {
      task.setRunning(false);
      this.updateTask(task);
    }
  }

  public Task getTask(String key) {
    return this.getTaskByKey(key);
  }

  public Task getTaskWithoutLockingDB(String taskKey, String serviceName) {
    return this.getTaskByKeyAndServiceRunning(taskKey, serviceName);
  }

  private void save(Task model) {
    this.taskRepository.save(model);
  }

  private Task getTaskByKey(String taskKey) {
    return this.taskRepository.findOneByTaskKey(taskKey);
  }

  private Task getTaskByKeyAndServiceRunning(String taskKey, String serviceName) {
    return this.taskRepository.findFirstByTaskKeyAndServiceRunning(taskKey, serviceName);
  }
}
