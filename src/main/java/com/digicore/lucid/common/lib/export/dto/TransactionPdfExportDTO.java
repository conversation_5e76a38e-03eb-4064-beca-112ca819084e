/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.export.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn 06/03/2025
 */

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TransactionPdfExportDTO {
  private String transactionDateAsString;
  private String transactionReference;
  private String transactionNarration;
  private String transactionTypeDebitAsString;
  private String transactionTypeCreditAsString;
  private String runningBalance;
}
