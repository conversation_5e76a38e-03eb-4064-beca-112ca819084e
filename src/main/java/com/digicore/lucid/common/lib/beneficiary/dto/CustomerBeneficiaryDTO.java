/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.beneficiary.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn 11/03/2025
 */

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomerBeneficiaryDTO {
  private String beneficiaryId;
  private String firstName;
  private String lastName;

  private String accountNumber;
  private String accountProviderName;
  private String accountProviderCode;
  private boolean sameBank;

  private String validatedAccountName;
}
