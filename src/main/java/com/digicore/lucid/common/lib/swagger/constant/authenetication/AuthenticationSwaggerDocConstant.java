/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.swagger.constant.authenetication;

/*
 * <AUTHOR>
 * @createdOn Jan-24(Fri)-2025
 */

public class AuthenticationSwaggerDocConstant {
  public static final String AUTHENTICATION_API = "authentication/process/";
  public static final String LOGIN_API = "login";
  public static final String FORGOT_PASSWORD_INIT_API = "forgot-password/init";
  public static final String FORGOT_PASSWORD_INIT_CONTROLLER_TITLE = "Forgot-password";
  public static final String FORGOT_PASSWORD_INIT_CONTROLLER_DESCRIPTION =
      "This API initiates reset process for forgotten password, it sends passcode to the registered email of the user.";
  public static final String FORGOT_PASSWORD_PHONE_OTP_VALIDATE_API =
      "forgot-password/phone-validate";
  public static final String FORGOT_PASSWORD_EMAIL_PASSCODE_VALIDATE_API =
      "forgot-password/email-validate";

  public static final String FORGOT_PASSWORD_PHONE_OTP_VALIDATE_CONTROLLER_TITLE =
      "Forgot-password-otp-validation";
  public static final String FORGOT_PASSWORD_PHONE_OTP_VALIDATE_CONTROLLER_DESCRIPTION =
      "This API validates otp sent to phone number and sends passcode to email automatically.";
  public static final String FORGOT_PASSWORD_EMAIL_PASSCODE_VALIDATE_CONTROLLER_TITLE =
      "Forgot-password-passcode-validation";
  public static final String FORGOT_PASSWORD_EMAIL_PASSCODE_VALIDATE_CONTROLLER_DESCRIPTION =
      "This API validates passcode sent to email and return a reset code which will be required to complete the password update.";
  public static final String FORGOT_PASSWORD_COMPLETE_API = "forgot-password/complete";
  public static final String FORGOT_PASSWORD_COMPLETE_CONTROLLER_TITLE = "Forgot-password-complete";
  public static final String FORGOT_PASSWORD_COMPLETE_CONTROLLER_DESCRIPTION =
      "This API validates reset code and completes password reset.";
  public static final String FORGOT_PASSWORD_RESEND_CODE_API = "forgot-password/resend";
  public static final String FORGOT_PASSWORD_RESEND_CODE_CONTROLLER_TITLE =
      "Forgot-password-resend-code";
  public static final String FORGOT_PASSWORD_RESEND_CODE_CONTROLLER_DESCRIPTION =
      "This API resend code to phone number or email.";
  public static final String AUTHENTICATION_CONTROLLER_TITLE = "Authentication-Module";
  public static final String AUTHENTICATION_CONTROLLER_DESCRIPTION =
      "This module contains all required APIs to complete authentication.";
  public static final String AUTHENTICATION_CONTROLLER_LOGIN_TITLE = "Authenticate a user";
  public static final String AUTHENTICATION_CONTROLLER_LOGIN_DESCRIPTION =
      "This API is used to authenticate a user.";
  public static final String CHANGE_PASSWORD_API = "change-password";
  public static final String CHANGE_PASSWORD_CONTROLLER_TITLE = "Change Password";
  public static final String CHANGE_PASSWORD_CONTROLLER_DESCRIPTION =
      "This API changes the password of a Customer User";
  public static final String CHANGE_PASSWORD_INIT_API = "change-password/init";
  public static final String CHANGE_PASSWORD_INIT_CONTROLLER_TITLE = "Initialize Change Password";
  public static final String CHANGE_PASSWORD_INIT_CONTROLLER_DESCRIPTION =
      "This Api is used to initialize the change password flow";
  public static final String CHANGE_PASSWORD_VALIDATE_OTP_API = "change-password/validate-otp";
  public static final String CHANGE_PASSWORD_OTP_VALIDATE_CONTROLLER_TITLE =
      "Change Password Otp Validate";
  public static final String CHANGE_PASSWORD_OTP_VALIDATE_CONTROLLER_DESCRIPTION =
      "This API validates passcode sent to email and return a code which will be required to complete the password update.";
  public static final String DEVICE_REGISTRATION_OTP_API = "device-registration/init";
  public static final String DEVICE_REGISTRATION_VALIDATION_API = "device-registration/validation";
  public static final String DEVICE_REGISTRATION_API = "device-registration/complete";
  public static final String DEVICE_REGISTRATION_CONTROLLER_TITLE = "device registration";
  public static final String DEVICE_REGISTRATION_CONTROLLER_DESCRIPTION =
      "This API performs an action for device registration";
}
