/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.limit.enums;

/*
 * <AUTHOR>
 * @createdOn Feb-25(Tue)-2025
 */

public enum LimitType {
  INTERBANK_TRANSFER_LIMIT("Inter-Bank transfers"),
  INTRABANK_TRANSFER_LIMIT("Intra-Bank transfers"),
  BULK_TRANSFER_LIMIT("Bulk transfers"),
  BVN_ONBOARDING_LIMIT("BVN Sign-up"),
  BACKOFFICE_ONBOARDING_LIMIT("Backoffice Onboarding");
  private final String prettyName;

  LimitType(String prettyName) {
    this.prettyName = prettyName;
  }

  public String getPrettyName() {
    return prettyName;
  }
}
