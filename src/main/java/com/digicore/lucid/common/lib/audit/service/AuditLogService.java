/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.audit.service;

import com.digicore.lucid.common.lib.processor.dto.AuditLogDTO;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;

/*
 * <AUTHOR>
 * @createdOn 06/03/2025
 */

public interface AuditLogService {
  default PaginatedResponseDTO<AuditLogDTO> fetchAuditLogs(String email, int pageNo, int pageSize) {
    return null;
  }

  default AuditLogDTO fetchAuditLog(Long auditLogId) {
    return null;
  }

  default PaginatedResponseDTO<AuditLogDTO> fetchAuditLogs(
      String organizationId, String email, int pageNo, int pageSize) {
    return null;
  }

  default AuditLogDTO fetchAuditLog(String organizationId, Long auditLogId) {
    return null;
  }
}
