/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.transaction.dto.transfer;

import com.digicore.lucid.common.lib.transaction.enums.TransferCategory;
import com.digicore.lucid.common.lib.transaction.enums.TransferType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

/*
 * <AUTHOR>
 * @createdOn Mar-12(Wed)-2025
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class NameEnquiryDTO {
  private String accountNumber;
  private String bankCode;
  private TransferCategory transferCategory;
  private TransferType transferType;
}
