/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.swagger.constant.account;

/*
 * <AUTHOR>
 * @createdOn 24/02/2025
 */

import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.RETRIEVE_ALL_API;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.RETRIEVE_API;

public class AccountSwaggerDocConstant {
  public static final String ACCOUNT_API = "account/process/";

  public static final String ACCOUNT_CONTROLLER_TITLE = "Account-Module";
  public static final String CUSTOMER_ACCOUNT_CONTROLLER_TITLE = "Customer-Account-Module";
  public static final String ACCOUNT_CONTROLLER_DESCRIPTION =
      "This module contains all required APIs to manage customer accounts system.";
  public static final String ACCOUNT_RETRIEVE_TITLE = "Fetch customer accounts with balance";
  public static final String ACCOUNT_RETRIEVE_DESCRIPTION =
      "This API is used to fetch active and viewable accounts of a customer in the system.";
  public static final String ACCOUNT_INFLOW_RETRIEVE_TITLE =
      "Fetch customer account inflow summary";
  public static final String ACCOUNT_INFLOW_RETRIEVE_DESCRIPTION =
      "This API is used to fetch inflow summary of a customer in the system.";
  public static final String FETCH_TRANSACTIONS_API = "transactions/" + RETRIEVE_ALL_API;
  public static final String FETCH_FLOWS_API = "flow-summary/" + RETRIEVE_API;
  public static final String FETCH_FX_RATE_API = "fx-rate/" + RETRIEVE_ALL_API;
  public static final String FETCH_RM_API = "fm/" + RETRIEVE_API;
  public static final String FETCH_BALANCES_API = "balances/" + RETRIEVE_API;
}
