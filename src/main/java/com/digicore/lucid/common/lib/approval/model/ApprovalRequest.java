/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.approval.model;

import com.digicore.lucid.common.lib.approval.converter.ApprovalRequestStatusConverter;
import com.digicore.lucid.common.lib.approval.enums.ApprovalRequestStatus;
import com.digicore.lucid.common.lib.approval.rule.model.ApprovalFlow;
import com.digicore.lucid.common.lib.processor.model.AuditLog;
import com.digicore.registhentication.registration.models.Auditable;
import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn Jan-26(Sun)-2025
 */

@Entity
@Table(name = "approval_request")
@Getter
@Setter
public class ApprovalRequest extends Auditable<String> implements Serializable {
  private String requesterName;
  private String approvalName;
  private String description;

  @Column(columnDefinition = "text")
  private String dataToUpdate;

  @Column(columnDefinition = "text")
  private String initialData;

  @NotNull @NotEmpty private String requesterUsername;
  private String approvalUsername;
  private int nextApprovalIndex;
  private boolean approved;

  @Convert(converter = ApprovalRequestStatusConverter.class)
  private ApprovalRequestStatus status;

  private String approvalRequestType;
  private String permission;
  private LocalDateTime createdOn;
  private LocalDateTime approvedDate;
  private String errorTrace;
  private String organizationId;
  private String platform;

  @OneToMany(mappedBy = "approvalRequest", fetch = FetchType.EAGER)
  private Collection<PendingFileRequest> pendingFileRequests = new ArrayList<>();

  @OneToMany(mappedBy = "approvalRequest", cascade = CascadeType.ALL)
  private List<AuditLog> auditLogs = new ArrayList<>();

  @OneToMany(mappedBy = "approvalRequest", cascade = CascadeType.ALL)
  private List<ApprovalFlow> approvalFlows = new ArrayList<>();

  private boolean requiresWorkFlow = false;
}
