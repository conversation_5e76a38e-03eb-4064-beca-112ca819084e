/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.properties;

import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.stereotype.Component;

/*
 * <AUTHOR>
 * @createdOn Mar-01(Sat)-2025
 */

@Component
@ConfigurationPropertiesScan
@ConfigurationProperties(prefix = "lucid.misc")
@Getter
@Setter
public class MiscPropertyConfig {
  private String qoreAccountOfficerCode;
  private String qoreAccountProductCode;
  private String locationsPath;
  private Map<String, Object> accountActivationRules;
  private String onboardingPath;

  public Object getAccountActivationRules(String key) {
    return accountActivationRules != null ? accountActivationRules.getOrDefault(key, "N/A") : null;
  }
}
