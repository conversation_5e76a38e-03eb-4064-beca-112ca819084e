/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.util;

/*
 * <AUTHOR>
 * @createdOn Jan-22(Wed)-2025
 */

import com.digicore.api.helper.exception.ZeusRuntimeException;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.BeanUtils;

public class BeanUtilWrapper {
  public static void copyNonNullProperties(Object source, Object target) {
    String[] nullFields = getNullFields(source);
    BeanUtils.copyProperties(source, target, nullFields);
  }

  private static String[] getNullFields(Object source) {
    Field[] declaredFields = source.getClass().getDeclaredFields();
    List<String> emptyFieldNames =
        Arrays.stream(declaredFields)
            .filter(
                (field) -> {
                  field.setAccessible(true);

                  Object o;
                  try {
                    o = field.get(source);
                  } catch (IllegalAccessException var4) {
                    IllegalAccessException e = var4;
                    throw new ZeusRuntimeException(e.getMessage(), e);
                  }

                  return Objects.isNull(o);
                })
            .map(Field::getName)
            .toList();
    String[] emptyFieldsArray = new String[emptyFieldNames.size()];
    return emptyFieldNames.toArray(emptyFieldsArray);
  }
}
