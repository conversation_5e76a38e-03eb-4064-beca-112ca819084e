/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.account.service;

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerOnboardingDTO;
import java.util.List;

/*
 * <AUTHOR>
 * @createdOn Feb-22(Sat)-2025
 */

public interface AccountService<T> {
  default void addAccount(String organizationId, String accountNumber) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default void addAccount(String organizationId, List<CustomerOnboardingDTO.Account> accounts) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default List<T> fetchActiveViewableAccounts(String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default List<T> fetchAllAccounts(String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default List<T> fetchAllAccounts(String bankOrganizationId, String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default boolean verifySelfAccountNumber(String accountNumber) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default boolean verifyAccountNumber(String accountNumber) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default boolean verifyAccountNumber(String accountNumber, String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default boolean verifyAccountNumberExists(String accountNumber, String organizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default <V> V fetchAccount(String accountNumber) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default T fetchAccount(String accountNumber, String organizationId, String bankOrganizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }
}
