/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.approval.rule.converter;

import com.digicore.lucid.common.lib.approval.enums.ApprovalStatus;
import com.digicore.registhentication.converter.StringEnumConverter;
import jakarta.persistence.Converter;

/*
 * <AUTHOR>
 * @createdOn Mar-03(Mon)-2025
 */

@Converter
public class ApprovalStatusConverter extends StringEnumConverter<ApprovalStatus> {
  public ApprovalStatusConverter() {
    super(ApprovalStatus.class);
  }
}
