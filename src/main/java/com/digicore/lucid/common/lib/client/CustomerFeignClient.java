/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.client;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.account.AccountSwaggerDocConstant.ACCOUNT_API;
import static com.digicore.lucid.common.lib.swagger.constant.activation.ActivationSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.audit.AuditTrailSwaggerDocConstant.AUDIT_TRAIL_API;
import static com.digicore.lucid.common.lib.swagger.constant.limit.LimitSwaggerDocConstant.LIMIT_API;
import static com.digicore.lucid.common.lib.swagger.constant.logonManagement.LogonManagementSwaggerDocConstant.LOGIN_ATTEMPTS_API;
import static com.digicore.lucid.common.lib.swagger.constant.logonManagement.LogonManagementSwaggerDocConstant.LOGON_MANAGEMENT_API;
import static com.digicore.lucid.common.lib.swagger.constant.onboarding.OnboardingSwaggerDocConstant.*;
import static com.digicore.lucid.common.lib.swagger.constant.profile.ProfileSwaggerDocConstant.PROFILE_API;
import static com.digicore.registhentication.util.PageableUtil.*;

import com.digicore.api.helper.response.ApiResponseJson;
import com.digicore.lucid.common.lib.activation.dto.CustomerActivationDTO;
import com.digicore.lucid.common.lib.approval.dto.ApprovalDecisionDTO;
import com.digicore.lucid.common.lib.client.config.FeignClientConfig;
import com.digicore.lucid.common.lib.limit.dto.CustomerLimitConfigDTO;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.lucid.common.lib.profile.dto.UserEditDTO;
import com.digicore.lucid.common.lib.registration.dto.UserRegistrationDTO;
import com.digicore.lucid.common.lib.registration.dto.UserResendInviteDTO;
import com.digicore.lucid.common.lib.registration.dto.customer.CustomerOnboardingDTO;
import com.digicore.registhentication.validator.enums.Currency;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-18(Tue)-2025
 */

@FeignClient(name = "lucid-customer", configuration = FeignClientConfig.class)
public interface CustomerFeignClient {
  @GetMapping(API_V1 + CUSTOMER_API + ACTIVATION_API + RETRIEVE_ACTIVATION_PROGRESS_API)
  ApiResponseJson<Object> retrieveProgress(
      @RequestHeader("bankOrganizationId") String bankOrganizationId,
      @RequestParam("customerActivationStatus") String status,
      @RequestParam int page,
      @RequestParam int size);

  @PostMapping(API_V1 + CUSTOMER_API + ACTIVATION_API + SAVE_PROGRESS_API)
  ApiResponseJson<Object> saveProgress(@RequestBody ApprovalDecisionDTO approvalDecisionDTO);

  @PostMapping(API_V1 + CUSTOMER_API + ACTIVATION_API + UPDATE_API)
  void saveProgress(
      @RequestParam String organizationId,
      @RequestParam String officerCode,
      @RequestParam String productCode);

  @GetMapping(API_V1 + CUSTOMER_API + ACTIVATION_API + RETRIEVE_ACTIVIATION_COMMENT_API)
  ApiResponseJson<Object> retrieveComment(
      @RequestHeader("bankOrganizationId") String bankOrganizationId,
      @RequestParam("organizationId") String organizationId);

  @PostMapping(API_V1 + CUSTOMER_API + ACTIVATION_API + COMPLETE_API)
  ApiResponseJson<Object> completeActivation(
      @RequestBody CustomerActivationDTO customerActivationDTO);

  @PostMapping(API_V1 + CUSTOMER_API + ACTIVATION_API + UPDATE_API)
  ApiResponseJson<Object> saveActivation(@RequestBody CustomerActivationDTO customerActivationDTO);

  @GetMapping(API_V1 + CUSTOMER_API + ACTIVATION_API + RETRIEVE_ACTIVATION_DOCUMENT_API)
  ApiResponseJson<Object> retrieveActivationDocument(
      @RequestParam String fileId, @RequestParam String organizationId);

  @PostMapping(API_V1 + CUSTOMER_API + ONBOARDING_API + CREATE_API)
  ApiResponseJson<Object> onboardCustomer(
      @Valid @RequestBody CustomerOnboardingDTO customerOnboardingDTO);

  @PostMapping(API_V1 + CUSTOMER_API + ONBOARDING_API + ADD_ACCOUNT_API)
  ApiResponseJson<Object> updateCustomerAccounts(
      @Valid @RequestBody CustomerOnboardingDTO customerOnboardingDTO);

  @PostMapping(API_V1 + CUSTOMER_API + ONBOARDING_API + VALIDATE_API)
  void validateCustomerDetails(@RequestBody CustomerOnboardingDTO customerOnboardingDTO);

  @GetMapping(API_V1 + CUSTOMER_API + ACCOUNT_API + VALIDATE_API)
  void validateAccountNumber(@RequestParam("accountNumber") String accountNumber);

  @GetMapping(API_V1 + CUSTOMER_API + ACCOUNT_API + RETRIEVE_ALL_API)
  ApiResponseJson<Object> fetchAccounts(@RequestParam String organizationId);

  @GetMapping(API_V1 + CUSTOMER_API + PROFILE_API + RETRIEVE_API)
  ApiResponseJson<Object> retrieveCustomerProfile(
      @RequestParam("organizationId") String organizationId);

  @GetMapping(API_V1 + CUSTOMER_API + PROFILE_API + RETRIEVE_ALL_API)
  ApiResponseJson<Object> retrieveCustomerProfiles(
      @RequestParam(value = PAGE_NUMBER) int pageNumber,
      @RequestParam(value = PAGE_SIZE) int pageSize);

  @PostMapping(API_V1 + CUSTOMER_API + PROFILE_API + ENABLE_API)
  void enableCustomer(@PathVariable("name") String organizationId);

  @PostMapping(API_V1 + CUSTOMER_API + PROFILE_API + DISABLE_API)
  void disableCustomer(@PathVariable("name") String organizationId);

  @PostMapping(API_V1 + CUSTOMER_API + PROFILE_API + ENABLE_API + COMPLETE_API)
  ApiResponseJson<Object> completeCustomerEnableRequest(
      @PathVariable("name") String bankOrganizationId,
      @RequestBody ApprovalDecisionDTO approvalDecisionDTO);

  @PostMapping(API_V1 + CUSTOMER_API + PROFILE_API + DISABLE_API + COMPLETE_API)
  ApiResponseJson<Object> completeCustomerDisableRequest(
      @PathVariable("name") String bankOrganizationId,
      @RequestBody ApprovalDecisionDTO approvalDecisionDTO);

  @GetMapping(API_V1 + CUSTOMER_API + USER_API + PROFILE_API + RETRIEVE_ALL_API)
  ApiResponseJson<Object> retrieveCustomerProfileUsers(
      @RequestParam String organizationId,
      @RequestParam(value = PAGE_NUMBER, defaultValue = PAGE_NUMBER_DEFAULT_VALUE, required = false)
          int pageNumber,
      @RequestParam(value = PAGE_SIZE, defaultValue = PAGE_SIZE_DEFAULT_VALUE, required = false)
          int pageSize);

  @GetMapping(API_V1 + CUSTOMER_API + USER_API + PROFILE_API + RETRIEVE_API)
  ApiResponseJson<Object> retrieveCustomerUserProfile(
      @RequestParam String organizationId, @RequestParam String username);

  @PostMapping(API_V1 + CUSTOMER_API + USER_API + PROFILE_API + EDIT_API)
  void editCustomerUserDetails(@RequestBody UserEditDTO userProfileDTO);

  @PostMapping(API_V1 + CUSTOMER_API + USER_API + PROFILE_API + EDIT_API + COMPLETE_API)
  ApiResponseJson<Object> completeEditCustomerUserDetails(
      @RequestBody ApprovalDecisionDTO approvalDecisionDTO);

  @PostMapping(API_V1 + CUSTOMER_API + USER_API + PROFILE_API + DISABLE_API)
  void disableCustomerUser(
      @PathVariable("name") String userName, @RequestParam String organizationId);

  @PostMapping(API_V1 + CUSTOMER_API + USER_API + PROFILE_API + ENABLE_API)
  void enableCustomerUser(
      @PathVariable("name") String userName, @RequestParam String organizationId);

  @PostMapping(API_V1 + CUSTOMER_API + USER_API + PROFILE_API + DISABLE_API + COMPLETE_API)
  ApiResponseJson<Object> completeDisableCustomerUserRequest(
      @PathVariable("name") String bankOrganizationId,
      @RequestBody ApprovalDecisionDTO approvalDecisionDTO);

  @PostMapping(API_V1 + CUSTOMER_API + USER_API + PROFILE_API + ENABLE_API + COMPLETE_API)
  ApiResponseJson<Object> completeEnableCustomerUserRequest(
      @PathVariable("name") String bankOrganizationId,
      @RequestBody ApprovalDecisionDTO approvalDecisionDTO);

  @GetMapping(API_V1 + CUSTOMER_API + ACCOUNT_API + VERIFY_API)
  void verifyAccountNumber(
      @RequestParam("organizationId") String organizationId,
      @RequestParam("accountNumber") String accountNumber);

  @GetMapping(API_V1 + CUSTOMER_API + LIMIT_API + RETRIEVE_ALL_API)
  ApiResponseJson<Object> retrieveLimitConfig(
      @RequestParam String organizationId, @RequestParam int page, @RequestParam int size);

  @GetMapping(API_V1 + CUSTOMER_API + LIMIT_API + RETRIEVE_API)
  ApiResponseJson<Object> retrieveLimitConfig(
      @RequestParam String accountNumber,
      @RequestParam String organizationId,
      @RequestParam LimitType limitType,
      @RequestParam Currency currency);

  @GetMapping(API_V1 + CUSTOMER_API + LIMIT_API + FETCH_API)
  ApiResponseJson<Object> retrieveCusomerLimit(
      @RequestParam String accountNumber,
      @RequestParam String organizationId,
      @RequestParam LimitType limitType,
      @RequestParam Currency currency);

  @GetMapping(API_V1 + CUSTOMER_API + PROFILE_API + FETCH_API)
  ApiResponseJson<Object> retrieveProfileLimitType(@RequestParam String organizationId);

  @PostMapping(API_V1 + CUSTOMER_API + LIMIT_API + VALIDATE_API)
  ApiResponseJson<Object> validateLimit(
      @Valid @RequestBody CustomerLimitConfigDTO customerLimitConfigDTO);

  @PostMapping(API_V1 + CUSTOMER_API + LIMIT_API + CREATE_API)
  void createLimit(@Valid @RequestBody CustomerLimitConfigDTO customerLimitConfigDTO);

  @PostMapping(API_V1 + CUSTOMER_API + LIMIT_API + EDIT_API)
  void editLimit(@Valid @RequestBody CustomerLimitConfigDTO customerLimitConfigDTO);

  @GetMapping(API_V1 + CUSTOMER_API + AUDIT_TRAIL_API + RETRIEVE_API)
  ApiResponseJson<Object> retrieveCustomerAuditTrail(
      @RequestHeader("customerOrganizationId") String customerOrganizationId,
      @RequestParam Long auditTrailId);

  @GetMapping(API_V1 + CUSTOMER_API + AUDIT_TRAIL_API + RETRIEVE_ALL_API)
  ApiResponseJson<Object> retrieveCustomerAuditTrails(
      @RequestHeader("customerOrganizationId") String customerOrganizationId,
      @RequestParam String email,
      @RequestParam(value = PAGE_NUMBER) int pageNumber,
      @RequestParam(value = PAGE_SIZE) int pageSize);

  @PostMapping(API_V1 + CUSTOMER_API + USER_API + ONBOARDING_API + INVITE_API)
  ApiResponseJson<Object> inviteCustomerUser(@RequestBody UserRegistrationDTO userRegistrationDTO);

  @PostMapping(API_V1 + CUSTOMER_API + USER_API + ONBOARDING_API + RESEND_INVITE_API)
  ApiResponseJson<Object> resendCustomerUserInvite(
      @RequestBody UserResendInviteDTO userResendInviteDTO);

  @PostMapping(API_V1 + CUSTOMER_API + USER_API + ONBOARDING_API + VALIDATE_API)
  void validateCustomerRoleAndProfileExistence(
      @RequestBody UserRegistrationDTO userRegistrationDTO);

  @PostMapping(API_V1 + CUSTOMER_API + USER_API + ONBOARDING_API + VERIFY_API)
  void verifyCustomerUserExistence(@RequestParam String email, @RequestParam String organizationId);

  @GetMapping(API_V1 + CUSTOMER_API + LOGON_MANAGEMENT_API + LOGIN_ATTEMPTS_API + RETRIEVE_ALL_API)
  ApiResponseJson<Object> getCustomersLoginAttempts(
      @RequestParam String bankOrganizationId,
      @RequestParam(value = PAGE_NUMBER) int pageNumber,
      @RequestParam(value = PAGE_SIZE) int pageSize);

  @GetMapping(API_V1 + CUSTOMER_API + LOGON_MANAGEMENT_API + LOGIN_ATTEMPTS_API + FILTER_API)
  ApiResponseJson<Object> searchCustomersLoginAttempts(
      @RequestParam String bankOrganizationId, @RequestParam String username);

  @GetMapping(API_V1 + CUSTOMER_API + LOGON_MANAGEMENT_API + LOGIN_ATTEMPTS_API + FETCH_API)
  ApiResponseJson<Object> getCustomersLoginAttemptsByUsername(
      @RequestParam String bankOrganizationId, @RequestParam String username);

  @GetMapping(API_V1 + CUSTOMER_API + LOGON_MANAGEMENT_API + LOGIN_ATTEMPTS_API + LOCK_API)
  void lockCustomerAccount(
      @RequestParam String username,
      @RequestParam String bankOrganizationId,
      @RequestParam String organizationId);

  @GetMapping(API_V1 + CUSTOMER_API + LOGON_MANAGEMENT_API + LOGIN_ATTEMPTS_API + UNLOCK_API)
  void unlockCustomerAccount(
      @RequestParam String username,
      @RequestParam String bankOrganizationId,
      @RequestParam String organizationId);
}
