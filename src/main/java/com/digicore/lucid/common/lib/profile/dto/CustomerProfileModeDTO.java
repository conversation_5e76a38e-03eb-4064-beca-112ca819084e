/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.profile.dto;

import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/*
 * <AUTHOR>
 * @createdOn May-19(Mon)-2025
 */
@Getter
@Setter
@NoArgsConstructor
public class CustomerProfileModeDTO {
  private String organizationId;

  private boolean multiUser;

  private List<CustomerUserDTO> customerUsers;

  @Getter
  @Setter
  public static class CustomerUserDTO {
    private String username;
    private String role;
  }
}
