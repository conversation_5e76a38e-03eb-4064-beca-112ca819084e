/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.location.controller;

import static com.digicore.lucid.common.lib.constant.api.ApiVersionConstant.API_V1;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.RETRIEVE_ALL_API;
import static com.digicore.lucid.common.lib.swagger.constant.location.LocationSwaggerDocConstant.*;

import com.digicore.api.helper.response.ControllerResponse;
import com.digicore.lucid.common.lib.location.service.LocationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/*
 * <AUTHOR>
 * @createdOn Apr-07(Mon)-2025
 */

@RestController
@RequestMapping(API_V1 + LOCATION_API)
@Tag(name = LOCATION_CONTROLLER_TITLE, description = LOCATION_CONTROLLER_DESCRIPTION)
@RequiredArgsConstructor
public class LocationController {
  private final LocationService locationService;

  @GetMapping(RETRIEVE_ALL_API)
  @Operation(summary = LOCATION_RETRIEVE_TITLE, description = LOCATION_RETRIEVE_DESCRIPTION)
  ResponseEntity<Object> fetchLocations() {
    return ControllerResponse.buildSuccessResponse(
        locationService.fetchLocations(), "Locations retrieved successfully.");
  }
}
