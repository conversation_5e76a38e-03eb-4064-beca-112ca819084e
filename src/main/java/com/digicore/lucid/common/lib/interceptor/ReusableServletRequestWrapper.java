/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.interceptor;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/*
 * <AUTHOR>
 * @createdOn Feb-14(Fri)-2025
 */

public class ReusableServletRequestWrapper extends HttpServletRequestWrapper {
  private final byte[] cachedBody;
  private final String cachedBodyString;

  public ReusableServletRequestWrapper(HttpServletRequest request, String bodyContent)
      throws IOException {
    super(request);
    this.cachedBodyString = bodyContent;
    this.cachedBody = bodyContent.getBytes(getCharacterEncoding());
  }

  @Override
  public ServletInputStream getInputStream() throws IOException {
    final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(cachedBody);
    return new ServletInputStream() {
      @Override
      public boolean isFinished() {
        return byteArrayInputStream.available() == 0;
      }

      @Override
      public boolean isReady() {
        return true; // Always ready for cached data
      }

      @Override
      public void setReadListener(ReadListener readListener) {
        // Not needed for synchronous reading
      }

      @Override
      public int read() throws IOException {
        return byteArrayInputStream.read();
      }
    };
  }

  @Override
  public BufferedReader getReader() throws IOException {
    return new BufferedReader(new InputStreamReader(getInputStream(), getCharacterEncoding()));
  }

  public String getCachedBody() {
    return this.cachedBodyString;
  }

  @Override
  public String getCharacterEncoding() {
    String encoding = super.getCharacterEncoding();
    return encoding != null ? encoding : StandardCharsets.UTF_8.name(); // Default to UTF-8
  }
}
