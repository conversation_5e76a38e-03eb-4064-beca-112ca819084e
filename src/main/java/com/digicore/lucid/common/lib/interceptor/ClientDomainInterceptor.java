/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.interceptor;

import static com.digicore.lucid.common.lib.constant.system.SystemConstant.*;

import com.digicore.lucid.common.lib.profile.service.OrganizationProfileService;
import com.digicore.lucid.common.lib.properties.EnterprisePropertyConfig;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.integration.lib.modules.config.properties.SecurityPropertyConfig;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Jan-30(Thu)-2025
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ClientDomainInterceptor extends OncePerRequestFilter {
  private final SecurityPropertyConfig securityPropertyConfig;

  @Autowired(required = false)
  private EnterprisePropertyConfig enterprisePropertyConfig;

  @Lazy
  @Autowired(required = false)
  private OrganizationProfileService organizationProfileService;

  @Autowired private Environment env;

  private final AntPathMatcher pathMatcher = new AntPathMatcher();

  @Override
  protected boolean shouldNotFilter(HttpServletRequest request) {
    String path = request.getRequestURI();
    return securityPropertyConfig.getFilteredUrls().stream()
        .anyMatch(pattern -> pathMatcher.match(pattern, path));
  }

  @Override
  protected void doFilterInternal(
      HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
      throws ServletException, IOException {
    RequestContextHolder.RequestContext requestContext = new RequestContextHolder.RequestContext();
    if (securityPropertyConfig.getPlatform().equalsIgnoreCase(ADMIN)) {
      log.info("<<< no extraction of subdomain >>>");
      requestContext.setPlatform(ADMIN);
      RequestContextHolder.set(requestContext);
      filterChain.doFilter(request, response);
    } else {
      EnterprisePropertyConfig propertyConfig =
          isProfileActive("enterprise") ? this.enterprisePropertyConfig : null;
      ClientUtil.validateSubDomainAndSetRequestContext(
          request, organizationProfileService, propertyConfig);
      try {
        filterChain.doFilter(request, response);
      } finally {
        RequestContextHolder.clear(); // Clean up ThreadLocal after request
      }
    }
  }

  public boolean isProfileActive(String profileName) {
    return env.acceptsProfiles(Profiles.of(profileName));
  }
}
