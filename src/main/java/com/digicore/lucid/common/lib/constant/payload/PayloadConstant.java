/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.constant.payload;

/*
 * <AUTHOR>
 * @createdOn Jan-22(Wed)-2025
 */

public class PayloadConstant {
  public static final String ROLE_NAME_IS_REQUIRED = "role name is required";
  public static final String ROLE_DESCRIPTION_IS_REQUIRED = "role description is required";
  public static final String ROLE_PERMISSIONS_ARE_REQUIRED = "role permissions are required";
  public static final String PERMISSION_NAME_IS_REQUIRED = "role name is required";
  public static final String PASSWORD_IS_REQUIRED_MESSAGE =
      "password is required, should contain special character, numbers and minimum length of password is 8";
  public static final String PASSWORD_PATTERN =
      "^(?=.*?[a-z])(?=.*?[A-Z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$";

  public static final String ROLE_NAME_PATTERN = "^(?:[a-zA-Z]\\s){0,34}[a-zA-Z]$";
  public static final String ROLE_DESCRIPTION_PATTERN = "^(?:[a-zA-Z]+\\s){0,24}[a-zA-Z]+$";

  public static final String ACCOUNT_NUMBER_IS_REQUIRED_MESSAGE =
      "valid NUBAN account number is required";
  public static final String ACCOUNT_NUMBER_PATTERN = "^\\d{10}$"; // NUBAN
  public static final String ACCOUNT_PROVIDER_NAME_IS_REQUIRED_MESSAGE =
      "valid account provider name is required";
  public static final String ACCOUNT_PROVIDER_CODE_IS_REQUIRED_MESSAGE =
      "valid account provider code is required";
  public static final String MINOR_SALARY_AMOUNT_IS_REQUIRED_MESSAGE =
      "valid minor salary amount is required";
  public static final String MINOR_AMOUNT_PATTERN = "^\\d{1,18}$";
  public static final String NAME_IS_REQUIRED = "name is required";
  public static final String PHONE_NUMBER_IS_REQUIRED = "valid phone number is required";
  public static final String PHONE_NUMBER_PATTERN = "^\\d{11}$";
}
