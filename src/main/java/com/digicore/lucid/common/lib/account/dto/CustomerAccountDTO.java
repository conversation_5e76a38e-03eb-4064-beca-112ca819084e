/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.account.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

/*
 * <AUTHOR>
 * @createdOn 24/02/2025
 */

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class CustomerAccountDTO {
  private String accountNumber;
  private String accountName;
  private String accountType;
  private String currency;
  private boolean active;
  private String cbaOrganizationId;
}
