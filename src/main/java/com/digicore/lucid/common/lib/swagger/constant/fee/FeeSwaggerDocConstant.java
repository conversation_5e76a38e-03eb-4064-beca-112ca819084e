/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.swagger.constant.fee;

/*
 * <AUTHOR>
 * @createdOn Mar-12(Wed)-2025
 */

public class FeeSwaggerDocConstant {
  public static final String FEE_API = "fee/process/";
  public static final String FEE_TRANSACTION_TYPES_API = FEE_API + "transaction-types";

  public static final String FEE_CONTROLLER_TITLE = "Fee-Module";
  public static final String FEE_CONTROLLER_DESCRIPTION =
      """
            This module contains all required APIs to complete operations surrounding fees
              """;
  public static final String FEE_CONTROLLER_CREATE_TITLE = "Create fee configuration";
  public static final String FEE_CONTROLLER_CREATE_DESCRIPTION =
      "This API is used to create a fee configuration.";

  public static final String FEE_CONTROLLER_VIEW_ALL_TITLE = "Retrieve fee configurations";
  public static final String FEE_CONTROLLER_VIEW_ALL_DESCRIPTION =
      "This API is used to retrieve all fee configurations.";
  public static final String FEE_CONTROLLER_VIEW_TITLE = "Retrieve a fee configuration";
  public static final String FEE_CONTROLLER_VIEW_DESCRIPTION =
      "This API is used to retrieve a fee configuration.";
  public static final String FEE_CONTROLLER_VIEW_TYPES_TITLE = "Retrieve fee transaction types";
  public static final String FEE_CONTROLLER_VIEW_TYPES_DESCRIPTION =
      "This API is used to retrieve fee transaction types.";
}
