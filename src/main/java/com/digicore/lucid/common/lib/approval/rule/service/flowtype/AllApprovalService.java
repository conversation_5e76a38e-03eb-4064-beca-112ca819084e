/// *
// * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
// * Unauthorized use or distribution is strictly prohibited.
// * For details, see the LICENSE file.
// */
//
// package com.digicore.lucid.common.lib.approval.rule.service.flowtype;
//
// import com.digicore.lucid.common.lib.approval.rule.service.ApprovalFlowType;
//
/// *
// * <AUTHOR>
// * @createdOn Feb-09(Sun)-2025
// */
// public class AllApprovalService implements ApprovalFlowType {}
