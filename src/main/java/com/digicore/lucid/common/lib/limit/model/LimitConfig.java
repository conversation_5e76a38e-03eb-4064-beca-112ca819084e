/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.limit.model;

import com.digicore.lucid.common.lib.limit.converter.LimitTypeConverter;
import com.digicore.lucid.common.lib.limit.enums.LimitType;
import com.digicore.registhentication.converter.CurrencyConverter;
import com.digicore.registhentication.registration.models.Auditable;
import com.digicore.registhentication.validator.enums.Currency;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/*
 * <AUTHOR> John
 * @createdOn Feb-27(Thu)-2025
 */

@Getter
@Setter
@MappedSuperclass
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class LimitConfig extends Auditable<String> implements Serializable {
  @Convert(converter = LimitTypeConverter.class)
  private LimitType limitType;

  @Convert(converter = CurrencyConverter.class)
  private Currency currency;

  private String minorMobileSingleCap;

  private String minorWebSingleCap;

  private String minorMobileCumulativeCap;

  private String minorWebCumulativeCap;

  private String minorMobileSingleCappedMessage;

  private String minorWebSingleCappedMessage;

  private String minorMobileCumulativeCappedMessage;

  private String minorWebCumulativeCappedMessage;

  private boolean unlimited;

  private boolean defaultLimit;
}
