/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.content.dto;

import com.digicore.lucid.common.lib.content.enums.ContentType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.time.LocalDateTime;
import lombok.*;

/*
 * <AUTHOR>
 * @createdOn Apr-01(Tue)-2025
 */

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BankContentDTO {
  private String contentId;
  private String title;
  private String subtitle;
  private String link;
  private LocalDateTime eventDate;
  private boolean status;
  private String webImage;
  private String mobileImage;
  private String webImageId;
  private String mobileImageId;
  private ContentType contentType;
}
