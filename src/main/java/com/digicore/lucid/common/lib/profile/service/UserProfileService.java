/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.profile.service;

/*
 * <AUTHOR>
 * @createdOn Jan-22(Wed)-2025
 */

import com.digicore.api.helper.exception.ZeusRuntimeException;
import com.digicore.lucid.common.lib.profile.dto.LucidSearchRequest;
import com.digicore.lucid.common.lib.profile.dto.UserEditDTO;
import com.digicore.registhentication.common.dto.response.PaginatedResponseDTO;

public interface UserProfileService<T> {
  default boolean profileExists(String email) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default void editUserProfile(UserEditDTO userEditDTO) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default void editUserProfile(String organizationId, UserEditDTO userEditDTO) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default T retrieveLoggedInUserProfile() {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default PaginatedResponseDTO<T> retrieveAllUserProfiles(int pageNumber, int pageSize) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default PaginatedResponseDTO<T> retrieveAllUserProfiles(
      String bankOrganizationId, String organizationId, int pageNumber, int pageSize) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default PaginatedResponseDTO<T> filterOrSearch(LucidSearchRequest lucidSearchRequest) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default void deleteUserProfile(String email) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default T retrieveUserProfile(String email) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default T retrieveUserProfile(String bankOrganizationId, String organizationId, String email) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default void enableUserProfile(String email) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default void disableUserProfile(String email) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default void profileExistenceCheckByEmail(String email) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default T retrieveUserPreference(
      String email, String organizationId, String bankOrganizationId, boolean forLogin) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default void validateNewUserPreference(
      String email, String organizationId, String bankOrganizationId) {}

  default T createUserPreference(
      T userPreference, String email, String organizationId, String bankOrganizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }

  default T editUserPreference(
      T userPreference, String email, String organizationId, String bankOrganizationId) {
    throw new ZeusRuntimeException("No Implementation Found");
  }
}
