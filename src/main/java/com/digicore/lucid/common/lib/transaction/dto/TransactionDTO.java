/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.transaction.dto;

import com.digicore.registhentication.common.enums.Channel;
import com.digicore.registhentication.registration.enums.Status;
import com.digicore.registhentication.validator.enums.Currency;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/*
 * <AUTHOR>
 * @createdOn Mar-03(Mon)-2025
 */
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionDTO {
  private Currency currency;
  private Status transactionStatus;
  private Channel channel;
  private String amountInMinor;
  private String vatAmountInMinor;
  private String feeAmountInMinor;
  private String transactionId;
  private String transactionReference;
  private String senderAccountName;
  private String senderAccountNumber;
  private String beneficiaryAccountName;
  private String beneficiaryAccountNumber;
  private String beneficiaryBankCode;
  private String beneficiaryBankName;
  private String nameEnquiryReference;
  private String narration;
  private String cbaTransactionReference;
  private String cbaTransactionResponseCode;
  private String cbaTransactionResponseMessage;
  private LocalDateTime transactionDate;
  private String sessionId;
}
