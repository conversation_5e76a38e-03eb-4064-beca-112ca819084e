/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.authentication.service;

import static com.digicore.lucid.common.lib.constant.message.MessageConstant.*;
import static com.digicore.lucid.common.lib.constant.message.MessagePlaceHolderConstant.*;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.ADMIN;
import static com.digicore.lucid.common.lib.constant.module.ModuleConstant.AUTHENTICATION;

import com.digicore.lucid.common.lib.authentication.model.LoginAttempt;
import com.digicore.lucid.common.lib.authentication.repository.LoginAttemptRepository;
import com.digicore.lucid.common.lib.interceptor.RequestContextHolder;
import com.digicore.lucid.common.lib.processor.service.RegulatoryLoggingService;
import com.digicore.lucid.common.lib.properties.MessagePropertyConfig;
import com.digicore.lucid.common.lib.util.ClientUtil;
import com.digicore.lucid.integration.lib.modules.config.properties.SecurityPropertyConfig;
import com.digicore.registhentication.authentication.dtos.request.LoginAttemptDTO;
import com.digicore.registhentication.authentication.services.LoginAttemptService;
import com.digicore.registhentication.common.enums.Channel;
import com.digicore.registhentication.exceptions.ExceptionHandler;
import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Jan-23(Thu)-2025
 */

@EntityScan("com.digicore.lucid.common.lib.authentication.model")
@EnableJpaRepositories(basePackages = {"com.digicore.lucid.common.lib.authentication.repository"})
@Service
@RequiredArgsConstructor
public class LucidLoginAttemptService implements LoginAttemptService {
  private final LoginAttemptRepository loginAttemptRepository;
  private final ExceptionHandler<String, String, HttpStatus, String> exceptionHandler;
  private final MessagePropertyConfig messagePropertyConfig;
  private final SecurityPropertyConfig securityPropertyConfig;
  private final RegulatoryLoggingService regulatoryLoggingService;
  private final HttpServletRequest httpServletRequest;

  private void systemLockUser(LoginAttempt loginAttempt) {
    loginAttempt.setLoginAccessDenied(true);
    loginAttempt.setAutomatedUnlockTime(null);
    this.save(loginAttempt);
  }

  private boolean shouldWarnUser(LoginAttempt loginAttempt) {
    return loginAttempt.getFailedAttemptCount()
            >= securityPropertyConfig.getLoginAttemptLimitBeforeWarning()
        && !loginAttempt.isLoginAccessDenied();
  }

  private LoginAttempt getOrCreateByUsername(LoginAttemptDTO loginAttemptDTO) {
    return this.findByUsername(loginAttemptDTO.getUsername())
        .orElseGet(
            () -> {
              LoginAttempt loginAttempt = new LoginAttempt();
              loginAttempt.setFailedAttemptCount(0);
              loginAttempt.setLoginAccessDenied(false);
              loginAttempt.setAutomatedUnlockTime(LocalDateTime.now());
              loginAttempt.setUsername(loginAttemptDTO.getUsername());
              loginAttempt.setAuthenticationType(loginAttemptDTO.getAuthenticationType());
              loginAttempt.setIpAddress(loginAttemptDTO.getIpAddress());
              loginAttempt.setUsername(loginAttemptDTO.getUsername());
              if (ADMIN.equalsIgnoreCase(securityPropertyConfig.getPlatform())) {
                loginAttempt.setPlatform(securityPropertyConfig.getPlatform());
                loginAttempt.setOrganizationId("N/A");
                loginAttempt.setChannel(Channel.WEB.toString());
              } else {
                loginAttempt.setPlatform(RequestContextHolder.get().getPlatform());
                loginAttempt.setOrganizationId(RequestContextHolder.get().getOrganizationId());
                loginAttempt.setBankOrganizationId(
                    RequestContextHolder.get().getBankOrganizationId());
                loginAttempt.setChannel(RequestContextHolder.get().getChannel().toString());
              }
              return this.save(loginAttempt);
            });
  }

  private LoginAttempt save(LoginAttempt userLoginAttempt) {
    return this.loginAttemptRepository.save(userLoginAttempt);
  }

  private Optional<LoginAttempt> findByUsername(String username) {
    return this.loginAttemptRepository.findFirstByUsernameOrderByCreatedDate(username);
  }

  /**
   * Unlock user.
   *
   * @param loginAttemptDTO the loginAttemptDTO
   */
  public void unlockUser(LoginAttemptDTO loginAttemptDTO) {
    LoginAttempt userLoginAttempt = this.getOrCreateByUsername(loginAttemptDTO);
    this.unlock(userLoginAttempt);
  }

  private void unlock(LoginAttempt userLoginAttempt) {
    userLoginAttempt.setFailedAttemptCount(0);
    userLoginAttempt.setLoginAccessDenied(false);
    userLoginAttempt.setAutomatedUnlockTime(LocalDateTime.now());
    this.save(userLoginAttempt);
  }

  @Override
  public void verifyLoginAccess(LoginAttemptDTO loginAttemptDTO, boolean credentialMatches) {
    loginAttemptDTO.setIpAddress(ClientUtil.getIpAddress(httpServletRequest));
    LoginAttempt loginAttempt = this.getOrCreateByUsername(loginAttemptDTO);
    //    long minutesRemaining =
    //        Duration.between(LocalDateTime.now(),
    // loginAttempt.getAutomatedUnlockTime()).toMinutes();
    if (!credentialMatches) {
      if (loginAttempt.isLoginAccessDenied()) {
        regulatoryLoggingService.log(
            loginAttemptDTO.getRole(),
            loginAttemptDTO.getUsername(),
            loginAttemptDTO.getName(),
            "LOGIN",
            AUTHENTICATION,
            messagePropertyConfig
                .getLoginMessage(ACCOUNT_LOCKED_DESCRIPTION)
                .replace(USER, loginAttemptDTO.getName()));
        exceptionHandler.processCustomExceptions(
            messagePropertyConfig.getLoginMessage(ACCOUNT_LOCKED), HttpStatus.UNAUTHORIZED);
      }

      loginAttempt.setFailedAttemptCount(loginAttempt.getFailedAttemptCount() + 1);
      this.save(loginAttempt);
      if (loginAttempt.getFailedAttemptCount()
          >= securityPropertyConfig.getLoginAttemptMaxCount()) {
        this.systemLockUser(loginAttempt);
        regulatoryLoggingService.log(
            loginAttemptDTO.getRole(),
            loginAttemptDTO.getUsername(),
            loginAttemptDTO.getName(),
            "LOGIN",
            AUTHENTICATION,
            messagePropertyConfig.getLoginMessage(ACCOUNT_LOCKED));
        exceptionHandler.processCustomExceptions(
            messagePropertyConfig.getLoginMessage(ACCOUNT_LOCKED), HttpStatus.UNAUTHORIZED);
      } else {
        regulatoryLoggingService.log(
            loginAttemptDTO.getRole(),
            loginAttemptDTO.getUsername(),
            loginAttemptDTO.getName(),
            "LOGIN",
            AUTHENTICATION,
            messagePropertyConfig
                .getLoginMessage(LOGIN_DENIED_DESCRIPTION)
                .replace(TIME, String.valueOf(loginAttempt.getFailedAttemptCount()))
                .replace(USER, loginAttemptDTO.getName()));

        if (shouldWarnUser(loginAttempt)) {
          int remainingAttempts =
              securityPropertyConfig.getLoginAttemptMaxCount()
                  - loginAttempt.getFailedAttemptCount();
          exceptionHandler.processCustomExceptions(
              messagePropertyConfig
                  .getLoginMessage(LOGIN_ATTEMPT_WARNING)
                  .replace(COUNT, String.valueOf(remainingAttempts)),
              HttpStatus.UNAUTHORIZED);
        }
      }
    } else {
      if (loginAttempt.isLoginAccessDenied()) {
        exceptionHandler.processCustomExceptions(
            messagePropertyConfig.getLoginMessage(ACCOUNT_LOCKED), HttpStatus.FORBIDDEN);
      }

      this.unlock(loginAttempt);
    }
  }

  private boolean shouldNotAutomaticallyUnlockProfile(LoginAttempt loginAttempt) {
    LocalDateTime unlockTime = loginAttempt.getAutomatedUnlockTime();
    if (loginAttempt.getAutomatedUnlockTime() == null) {
      return false;
    }
    return !LocalDateTime.now().isAfter(unlockTime);
  }

  public void manualLock(LoginAttempt loginAttempt) {
    loginAttempt.setLoginAccessDenied(true);
    loginAttempt.setAutomatedUnlockTime(null);
    this.save(loginAttempt);
  }

  public static LoginAttemptDTO convertToDto(LoginAttempt attempt) {
    LoginAttemptDTO dto = new LoginAttemptDTO();
    BeanUtils.copyProperties(attempt, dto);
    return dto;
  }
}
