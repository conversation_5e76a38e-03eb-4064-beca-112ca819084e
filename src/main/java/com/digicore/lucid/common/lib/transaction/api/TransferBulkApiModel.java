/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.transaction.api;

import com.digicore.lucid.common.lib.transaction.enums.TransferCategory;
import com.digicore.registhentication.registration.enums.Status;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/*
 * <AUTHOR>
 * @createdOn Apr-28(Mon)-2025
 */

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class TransferBulkApiModel {
  private TransferCategory transferCategory;
  private String senderAccountName;
  private String senderAccountNumber;
  private String bulkTransactionReference;
  private String totalAmountInMinor;
  private int transferEntriesCount;
  private Status transactionStatus;
  private LocalDateTime transactionDate;

  @Getter
  @Setter
  @SuperBuilder
  @NoArgsConstructor
  @AllArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  @JsonInclude(JsonInclude.Include.NON_DEFAULT)
  public static class TransferBulkEntryApiModel {
    private String amountInMinor;
    private String transactionId;
    private String transactionReference;
    private String beneficiaryAccountName;
    private String beneficiaryAccountNumber;
    private String beneficiaryBankCode;
    private String beneficiaryBankName;
    private String narration;
  }
}
