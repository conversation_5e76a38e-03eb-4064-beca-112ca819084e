/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.authentication.repository;

import com.digicore.lucid.common.lib.authentication.model.LoginAttempt;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/*
 * <AUTHOR>
 * @createdOn Jan-24(Fri)-2025
 */

public interface LoginAttemptRepository extends JpaRepository<LoginAttempt, Long> {
  Optional<LoginAttempt> findFirstByUsernameOrderByCreatedDate(String username);

  Optional<LoginAttempt> findFirstByUsernameAndOrganizationIdOrderByCreatedDate(
      String username, String organizationId);

  Optional<LoginAttempt>
      findFirstByUsernameAndOrganizationIdAndBankOrganizationIdOrderByCreatedDate(
          String username, String organizationId, String bankOrganizationId);

  List<LoginAttempt> findByOrganizationId(String organizationId);

  List<LoginAttempt> findByBankOrganizationId(String BankOrganizationId);

  @Query(
      "SELECT l FROM LoginAttempt l WHERE l.organizationId = :organizationId AND LOWER(l.username) LIKE LOWER(CONCAT('%', :username, '%'))")
  List<LoginAttempt> searchByUsernameAndOrganizationId(
      @Param("organizationId") String organizationId, @Param("username") String username);

  @Query(
      "SELECT l FROM LoginAttempt l WHERE l.bankOrganizationId = :bankOrganizationId AND LOWER(l.username) LIKE LOWER(CONCAT('%', :username, '%'))")
  List<LoginAttempt> searchByUsernameAndBankOrganizationId(
      @Param("bankOrganizationId") String bankOrganizationId, @Param("username") String username);
}
