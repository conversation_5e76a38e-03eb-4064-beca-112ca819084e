/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.common.lib.swagger.constant.activation;

import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.RETRIEVE_API;
import static com.digicore.lucid.common.lib.swagger.constant.CommonSwaggerDocConstant.UPDATE_API;

/*
 * <AUTHOR>
 * @createdOn Feb-17(Mon)-2025
 */

public class ActivationSwaggerDocConstant {
  public static final String ACTIVATION_API = "activation/process/";
  public static final String BANK_ACTIVATION_API = "bank/activation/process/";
  public static final String RETRIEVE_ACTIVATION_PROGRESS_API = "progress/" + RETRIEVE_API;
  public static final String RETRIEVE_ACTIVATION_DOCUMENT_API = "document/" + RETRIEVE_API;
  public static final String RETRIEVE_ACTIVATION_STATUS_API = "status/" + RETRIEVE_API;
  public static final String RETRIEVE_ACTIVATION_CUSTOMER_API = "signatory/" + RETRIEVE_API;
  public static final String RETRY_FAILED_ACTIVATION_API = "progress/retry";
  public static final String SAVE_PROGRESS_API = "progress/save";
  public static final String SAVE_SIGNATORY_API = "signatory/" + UPDATE_API;
  public static final String INVITE_SIGNATORY_API = "signatory/invite";
  public static final String RETRIEVE_ACTIVIATION_COMMENT_API = "progress/comment/" + RETRIEVE_API;
  public static final String UPLOAD_DOCUMENT_API = "document/upload";
  public static final String DOWNLOAD_DOCUMENT_API = "document/download";

  public static final String ACTIVATION_CONTROLLER_TITLE = "Activation-Module";
  public static final String ACTIVATION_CONTROLLER_DESCRIPTION =
      "This module contains all required APIs to complete profile activation.";
  public static final String ACTIVATION_CONTROLLER_SAVE_PROGRESS_TITLE = "Save activation progress";
  public static final String ACTIVATION_CONTROLLER_SAVE_PROGRESS_DESCRIPTION =
      "This API is used to save activation progress.";
  public static final String ACTIVATION_CONTROLLER_RETRY_PROGRESS_TITLE = "Retry activation";
  public static final String ACTIVATION_CONTROLLER_RETRY_PROGRESS_DESCRIPTION =
      "This API is used to retry failed activations.";
  public static final String ACTIVATION_CONTROLLER_INVITE_SIGNATORY_TITLE =
      "Invite signatories/directors";
  public static final String ACTIVATION_CONTROLLER_INVITE_SIGNATORY_DESCRIPTION =
      "This API is used to invite signatories or directors";
  public static final String ACTIVATION_CONTROLLER_RETRIEVE_PROGRESS_TITLE =
      "Retrieve activation progress";
  public static final String ACTIVATION_CONTROLLER_RETRIEVE_PROGRESS_DESCRIPTION =
      "This API is used to retrieve activation progress.";
  public static final String ACTIVATION_CONTROLLER_RETRIEVE_STATUS_TITLE =
      "Retrieve activation status";
  public static final String ACTIVATION_CONTROLLER_RETRIEVE_STATUS_DESCRIPTION =
      "This API is used to retrieve activation status.";
  public static final String ACTIVATION_CONTROLLER_RETRIEVE_FAILED_TITLE =
      "Retrieve failed activation";
  public static final String ACTIVATION_CONTROLLER_RETRIEVE_FAILED_DESCRIPTION =
      "This API is used to retrieve failed activation.";
  public static final String ACTIVATION_CONTROLLER_UPLOAD_DOCUMENT_TITLE = "Upload documents";
  public static final String ACTIVATION_CONTROLLER_UPLOAD_DOCUMENT_DESCRIPTION =
      "This API is used to upload documents.";
  public static final String ACTIVATION_CONTROLLER_DOWNLOAD_DOCUMENT_TITLE = "Download documents";
  public static final String ACTIVATION_CONTROLLER_DOWNLOAD_DOCUMENT_DESCRIPTION =
      "This API is used to download documents.";
  public static final String ACTIVATION_CONTROLLER_RETRIEVE_COMMENT_TITLE =
      "Retrieve activation comment";
  public static final String ACTIVATION_CONTROLLER_RETRIEVE_COMMENT_DESCRIPTION =
      "This API is used to retrieve activation comments.";
  public static final String ACTIVATION_CONTROLLER_RETRIEVE_ACCOUNT_OFFICERS_TITLE =
      "Retrieve account officers";
  public static final String ACTIVATION_CONTROLLER_RETRIEVE_ACCOUNT_OFFICERS_DESCRIPTION =
      "This API is used to retrieve account officers.";
  public static final String ACTIVATION_CONTROLLER_RETRIEVE_PRODUCT_CODES_TITLE =
      "Retrieve product codes";
  public static final String ACTIVATION_CONTROLLER_RETRIEVE_PRODUCT_CODES_DESCRIPTION =
      "This API is used to retrieve product codes.";
}
