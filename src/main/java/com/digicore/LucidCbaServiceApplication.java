package com.digicore;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableFeignClients
@SpringBootApplication(
    exclude = {UserDetailsServiceAutoConfiguration.class, DataSourceAutoConfiguration.class})
public class LucidCbaServiceApplication {

  public static void main(String[] args) {
    SpringApplication.run(LucidCbaServiceApplication.class, args);
  }
}
