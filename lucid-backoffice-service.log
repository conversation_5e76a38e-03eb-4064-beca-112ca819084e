2025-07-07T17:56:12.087+01:00  INFO 31524 --- [lucid-backoffice] [main] c.d.LucidBackofficeServiceApplication    : Starting LucidBackofficeServiceApplication using Java 21.0.1 with PID 31524 (/Users/<USER>/Desktop/LUCID/lucid-backoffice-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T17:56:12.088+01:00  INFO 31524 --- [lucid-backoffice] [main] c.d.LucidBackofficeServiceApplication    : The following 6 profiles are active: "dev", "messages", "mails", "coronation", "coronation-provider", "enterprise"
2025-07-07T17:56:12.115+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T17:56:12.116+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-backoffice, profiles=[default], label=null, version=null, state=null
2025-07-07T17:56:12.116+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T17:56:12.116+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-backoffice, profiles=[dev,messages,mails,coronation,coronation-provider,enterprise], label=null, version=null, state=null
2025-07-07T17:56:12.908+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:56:13.068+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 152 ms. Found 16 JPA repository interfaces.
2025-07-07T17:56:13.072+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:56:13.083+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T17:56:13.083+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:56:13.086+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:56:13.086+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:56:13.089+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:56:13.117+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:56:13.121+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:56:13.121+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:56:13.125+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:56:13.835+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=a351f2d9-f035-3602-8ffd-3d1da8d64acf
2025-07-07T17:56:14.060+01:00  WARN 31524 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:56:14.063+01:00  WARN 31524 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:56:14.065+01:00  WARN 31524 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000b80167d770] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:56:14.068+01:00  WARN 31524 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:56:14.073+01:00  WARN 31524 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:56:14.076+01:00  WARN 31524 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:56:14.078+01:00  WARN 31524 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T17:56:14.635+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8074 (http)
2025-07-07T17:56:14.662+01:00  INFO 31524 --- [lucid-backoffice] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T17:56:14.662+01:00  INFO 31524 --- [lucid-backoffice] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T17:56:14.723+01:00  INFO 31524 --- [lucid-backoffice] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T17:56:14.723+01:00  INFO 31524 --- [lucid-backoffice] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2606 ms
2025-07-07T17:56:15.246+01:00  INFO 31524 --- [lucid-backoffice] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T17:56:15.321+01:00  INFO 31524 --- [lucid-backoffice] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.4.Final
2025-07-07T17:56:15.368+01:00  INFO 31524 --- [lucid-backoffice] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T17:56:15.569+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T17:56:15.597+01:00  INFO 31524 --- [lucid-backoffice] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T17:56:16.158+01:00  INFO 31524 --- [lucid-backoffice] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3884e858
2025-07-07T17:56:16.162+01:00  INFO 31524 --- [lucid-backoffice] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T17:56:16.327+01:00  INFO 31524 --- [lucid-backoffice] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T17:56:17.931+01:00  INFO 31524 --- [lucid-backoffice] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T17:56:18.119+01:00  INFO 31524 --- [lucid-backoffice] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T17:56:18.399+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T17:56:19.829+01:00  INFO 31524 --- [lucid-backoffice] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T17:56:19.996+01:00  WARN 31524 --- [lucid-backoffice] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T17:56:20.593+01:00  INFO 31524 --- [lucid-backoffice] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T17:56:20.845+01:00  INFO 31524 --- [lucid-backoffice] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T17:56:22.797+01:00  INFO 31524 --- [lucid-backoffice] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T17:56:37.960+01:00  WARN 31524 --- [lucid-backoffice] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T17:56:38.124+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-customer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:56:38.369+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:56:38.563+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:56:38.661+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-administration' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:56:39.182+01:00  WARN 31524 --- [lucid-backoffice] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T17:56:40.238+01:00  INFO 31524 --- [lucid-backoffice] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name backOfficeUserLoginService
2025-07-07T17:56:40.963+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T17:56:41.520+01:00  WARN 31524 --- [lucid-backoffice] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T17:56:41.711+01:00  INFO 31524 --- [lucid-backoffice] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T17:56:41.841+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T17:56:41.854+01:00  INFO 31524 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T17:56:41.856+01:00  INFO 31524 --- [lucid-backoffice] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:56:41.860+01:00  INFO 31524 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T17:56:41.860+01:00  INFO 31524 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T17:56:41.860+01:00  INFO 31524 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T17:56:41.860+01:00  INFO 31524 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T17:56:41.860+01:00  INFO 31524 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T17:56:41.860+01:00  INFO 31524 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T17:56:41.860+01:00  INFO 31524 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T17:56:42.003+01:00  INFO 31524 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T17:56:42.004+01:00  INFO 31524 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T17:56:42.005+01:00  INFO 31524 --- [lucid-backoffice] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T17:56:42.006+01:00  INFO 31524 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751907402006 with initial instances count: 1
2025-07-07T17:56:42.012+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-BACKOFFICE with eureka with status UP
2025-07-07T17:56:42.013+01:00  INFO 31524 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751907402013, current=UP, previous=STARTING]
2025-07-07T17:56:42.013+01:00  INFO 31524 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-BACKOFFICE/*************:lucid-backoffice:8074: registering service...
2025-07-07T17:56:42.023+01:00  INFO 31524 --- [lucid-backoffice] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8074 (http) with context path '/'
2025-07-07T17:56:42.024+01:00  INFO 31524 --- [lucid-backoffice] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8074
2025-07-07T17:56:42.054+01:00  INFO 31524 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-BACKOFFICE/*************:lucid-backoffice:8074 - registration status: 204
2025-07-07T17:56:42.191+01:00  INFO 31524 --- [lucid-backoffice] [main] c.d.LucidBackofficeServiceApplication    : Started LucidBackofficeServiceApplication in 30.708 seconds (process running for 36.426)
2025-07-07T17:56:42.598+01:00  INFO 31524 --- [lucid-backoffice] [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T17:56:42.598+01:00  INFO 31524 --- [lucid-backoffice] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T17:56:42.602+01:00  INFO 31524 --- [lucid-backoffice] [RMI TCP Connection(3)-*************] o.s.a.r.c.CachingConnectionFactory       : Attempting to connect to: [localhost:5672]
2025-07-07T17:56:42.603+01:00  INFO 31524 --- [lucid-backoffice] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 5 ms
2025-07-07T17:56:43.105+01:00  INFO 31524 --- [lucid-backoffice] [RMI TCP Connection(3)-*************] o.s.a.r.c.CachingConnectionFactory       : Created new connection: rabbitConnectionFactory#35d0f108:0/SimpleConnection@35b7572b [delegate=amqp://guest@127.0.0.1:5672/, localPort=58439]
2025-07-07T17:56:43.149+01:00  INFO 31524 --- [lucid-backoffice] [RMI TCP Connection(3)-*************] o.s.amqp.rabbit.core.RabbitAdmin         : Auto-declaring a non-durable, auto-delete, or exclusive Queue (lucidNotification) durable:false, auto-delete:false, exclusive:false. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-07T17:59:23.183+01:00  INFO 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-BACKOFFICE with eureka with status DOWN
2025-07-07T17:59:23.192+01:00  INFO 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751907563192, current=DOWN, previous=UP]
2025-07-07T17:59:23.198+01:00  INFO 31524 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-BACKOFFICE/*************:lucid-backoffice:8074: registering service...
2025-07-07T17:59:23.737+01:00  INFO 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T17:59:23.763+01:00  INFO 31524 --- [lucid-backoffice] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T17:59:23.786+01:00  INFO 31524 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-BACKOFFICE/*************:lucid-backoffice:8074 - registration status: 204
2025-07-07T17:59:24.119+01:00  INFO 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T17:59:24.132+01:00  INFO 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T17:59:24.158+01:00  INFO 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T17:59:24.161+01:00  INFO 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T17:59:27.197+01:00  INFO 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T17:59:27.210+01:00  INFO 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/} exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE/*************:lucid-backoffice:8074": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE/*************:lucid-backoffice:8074": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:91)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 37 more

2025-07-07T17:59:27.210+01:00  WARN 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE/*************:lucid-backoffice:8074": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T17:59:27.218+01:00  INFO 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE/*************:lucid-backoffice:8074": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE/*************:lucid-backoffice:8074": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 38 more

2025-07-07T17:59:27.218+01:00  WARN 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE/*************:lucid-backoffice:8074": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T17:59:27.218+01:00 ERROR 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-BACKOFFICE/*************:lucid-backoffice:8074 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-3.4.1.jar:3.4.1]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147) ~[spring-boot-3.4.1.jar:3.4.1]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116) ~[spring-boot-3.4.1.jar:3.4.1]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T17:59:27.222+01:00  INFO 31524 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T19:23:39.344+01:00  INFO 49744 --- [lucid-backoffice] [main] c.d.LucidBackofficeServiceApplication    : Starting LucidBackofficeServiceApplication using Java 21.0.1 with PID 49744 (/Users/<USER>/Desktop/LUCID/lucid-backoffice-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T19:23:39.346+01:00  INFO 49744 --- [lucid-backoffice] [main] c.d.LucidBackofficeServiceApplication    : The following 6 profiles are active: "dev", "messages", "mails", "coronation", "coronation-provider", "enterprise"
2025-07-07T19:23:39.368+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T19:23:39.368+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-backoffice, profiles=[default], label=null, version=null, state=null
2025-07-07T19:23:39.369+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T19:23:39.369+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-backoffice, profiles=[dev,messages,mails,coronation,coronation-provider,enterprise], label=null, version=null, state=null
2025-07-07T19:23:40.136+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T19:23:40.280+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 137 ms. Found 16 JPA repository interfaces.
2025-07-07T19:23:40.289+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T19:23:40.308+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 18 ms. Found 4 JPA repository interfaces.
2025-07-07T19:23:40.309+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T19:23:40.313+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-07-07T19:23:40.313+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T19:23:40.317+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-07-07T19:23:40.347+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T19:23:40.354+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-07-07T19:23:40.354+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T19:23:40.359+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-07T19:23:41.001+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=a351f2d9-f035-3602-8ffd-3d1da8d64acf
2025-07-07T19:23:41.237+01:00  WARN 49744 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T19:23:41.245+01:00  WARN 49744 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T19:23:41.251+01:00  WARN 49744 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000e00167ea48] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T19:23:41.260+01:00  WARN 49744 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T19:23:41.273+01:00  WARN 49744 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T19:23:41.284+01:00  WARN 49744 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T19:23:41.291+01:00  WARN 49744 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T19:23:41.520+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8074 (http)
2025-07-07T19:23:41.529+01:00  INFO 49744 --- [lucid-backoffice] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T19:23:41.529+01:00  INFO 49744 --- [lucid-backoffice] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T19:23:41.590+01:00  INFO 49744 --- [lucid-backoffice] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T19:23:41.591+01:00  INFO 49744 --- [lucid-backoffice] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2221 ms
2025-07-07T19:23:41.924+01:00  INFO 49744 --- [lucid-backoffice] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T19:23:41.982+01:00  INFO 49744 --- [lucid-backoffice] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.4.Final
2025-07-07T19:23:42.010+01:00  INFO 49744 --- [lucid-backoffice] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T19:23:42.183+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T19:23:42.207+01:00  INFO 49744 --- [lucid-backoffice] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T19:23:42.326+01:00  INFO 49744 --- [lucid-backoffice] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@509bf2d5
2025-07-07T19:23:42.327+01:00  INFO 49744 --- [lucid-backoffice] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T19:23:42.420+01:00  INFO 49744 --- [lucid-backoffice] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T19:23:43.405+01:00  INFO 49744 --- [lucid-backoffice] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T19:23:43.524+01:00  INFO 49744 --- [lucid-backoffice] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T19:23:43.768+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T19:23:44.985+01:00  INFO 49744 --- [lucid-backoffice] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T19:23:45.027+01:00  WARN 49744 --- [lucid-backoffice] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T19:23:45.121+01:00  INFO 49744 --- [lucid-backoffice] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T19:23:45.172+01:00  INFO 49744 --- [lucid-backoffice] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T19:23:45.808+01:00  INFO 49744 --- [lucid-backoffice] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T19:24:08.179+01:00  WARN 49744 --- [lucid-backoffice] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T19:24:08.638+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-customer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T19:24:08.828+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T19:24:09.090+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T19:24:09.221+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-administration' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T19:24:10.675+01:00  WARN 49744 --- [lucid-backoffice] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T19:24:12.502+01:00  INFO 49744 --- [lucid-backoffice] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name backOfficeUserLoginService
2025-07-07T19:24:13.959+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T19:24:17.097+01:00  WARN 49744 --- [lucid-backoffice] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T19:24:17.861+01:00  INFO 49744 --- [lucid-backoffice] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T19:24:19.528+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T19:24:19.569+01:00  INFO 49744 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T19:24:19.573+01:00  INFO 49744 --- [lucid-backoffice] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T19:24:19.584+01:00  INFO 49744 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T19:24:19.584+01:00  INFO 49744 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T19:24:19.584+01:00  INFO 49744 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T19:24:19.584+01:00  INFO 49744 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T19:24:19.584+01:00  INFO 49744 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T19:24:19.584+01:00  INFO 49744 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T19:24:19.584+01:00  INFO 49744 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T19:24:20.336+01:00  INFO 49744 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T19:24:20.338+01:00  INFO 49744 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T19:24:20.340+01:00  INFO 49744 --- [lucid-backoffice] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T19:24:20.341+01:00  INFO 49744 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751912660341 with initial instances count: 1
2025-07-07T19:24:20.363+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-BACKOFFICE with eureka with status UP
2025-07-07T19:24:20.365+01:00  INFO 49744 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751912660365, current=UP, previous=STARTING]
2025-07-07T19:24:20.367+01:00  INFO 49744 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-BACKOFFICE/*************:lucid-backoffice:8074: registering service...
2025-07-07T19:24:20.399+01:00  INFO 49744 --- [lucid-backoffice] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8074 (http) with context path '/'
2025-07-07T19:24:20.400+01:00  INFO 49744 --- [lucid-backoffice] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8074
2025-07-07T19:24:20.448+01:00  INFO 49744 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-BACKOFFICE/*************:lucid-backoffice:8074 - registration status: 204
2025-07-07T19:24:20.828+01:00  INFO 49744 --- [lucid-backoffice] [main] c.d.LucidBackofficeServiceApplication    : Started LucidBackofficeServiceApplication in 41.93 seconds (process running for 47.551)
2025-07-07T19:24:25.411+01:00  INFO 49744 --- [lucid-backoffice] [RMI TCP Connection(1)-*************] o.s.a.r.c.CachingConnectionFactory       : Attempting to connect to: [localhost:5672]
2025-07-07T19:24:25.630+01:00  INFO 49744 --- [lucid-backoffice] [RMI TCP Connection(2)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T19:24:25.631+01:00  INFO 49744 --- [lucid-backoffice] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T19:24:25.644+01:00  INFO 49744 --- [lucid-backoffice] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 11 ms
2025-07-07T19:24:27.881+01:00  INFO 49744 --- [lucid-backoffice] [RMI TCP Connection(1)-*************] o.s.a.r.c.CachingConnectionFactory       : Created new connection: rabbitConnectionFactory#349114be:0/SimpleConnection@a48da34 [delegate=amqp://guest@127.0.0.1:5672/, localPort=59702]
2025-07-07T19:24:28.047+01:00  INFO 49744 --- [lucid-backoffice] [RMI TCP Connection(1)-*************] o.s.amqp.rabbit.core.RabbitAdmin         : Auto-declaring a non-durable, auto-delete, or exclusive Queue (lucidNotification) durable:false, auto-delete:false, exclusive:false. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-07T19:29:19.596+01:00  INFO 49744 --- [lucid-backoffice] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T19:36:29.520+01:00  WARN 49744 --- [lucid-backoffice] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m46s973ms).
2025-07-07T19:40:53.311+01:00  WARN 49744 --- [lucid-backoffice] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=4m23s799ms).
2025-07-07T19:51:58.199+01:00  WARN 49744 --- [lucid-backoffice] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=11m4s889ms).
2025-07-07T19:52:05.219+01:00  INFO 49744 --- [lucid-backoffice] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T19:55:22.352+01:00  WARN 49744 --- [lucid-backoffice] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m24s153ms).
2025-07-07T21:01:13.245+01:00  WARN 49744 --- [lucid-backoffice] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h5m50s892ms).
2025-07-07T21:47:43.926+01:00  WARN 49744 --- [lucid-backoffice] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=46m677ms).
2025-07-07T21:48:59.337+01:00  WARN 49744 --- [lucid-backoffice] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m15s413ms).
2025-07-07T21:49:17.479+01:00  INFO 49744 --- [lucid-backoffice] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-BACKOFFICE with eureka with status DOWN
2025-07-07T21:49:17.510+01:00  INFO 49744 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751921357510, current=DOWN, previous=UP]
2025-07-07T21:49:17.516+01:00  INFO 49744 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-BACKOFFICE/*************:lucid-backoffice:8074: registering service...
2025-07-07T21:49:18.405+01:00  INFO 49744 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/} exception=I/O error on POST request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.register(RestTemplateEurekaHttpClient.java:87)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:91)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:828)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:125)
	at com.netflix.discovery.InstanceInfoReplicator$2.run(InstanceInfoReplicator.java:105)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 21 more

2025-07-07T21:49:18.410+01:00  WARN 49744 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on POST request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T21:49:18.435+01:00  INFO 49744 --- [lucid-backoffice] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T21:49:18.491+01:00  INFO 49744 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on POST request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.register(RestTemplateEurekaHttpClient.java:87)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:828)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:125)
	at com.netflix.discovery.InstanceInfoReplicator$2.run(InstanceInfoReplicator.java:105)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 22 more

2025-07-07T21:49:18.491+01:00  WARN 49744 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on POST request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T21:49:18.555+01:00  INFO 49744 --- [lucid-backoffice] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T21:49:18.493+01:00  WARN 49744 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-BACKOFFICE/*************:lucid-backoffice:8074 - registration failed Cannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:828) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:125) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.InstanceInfoReplicator$2.run(InstanceInfoReplicator.java:105) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T21:49:18.690+01:00  WARN 49744 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] c.n.discovery.InstanceInfoReplicator     : There was a problem with the instance info replicator

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:828) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:125) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.InstanceInfoReplicator$2.run(InstanceInfoReplicator.java:105) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T21:49:19.111+01:00  INFO 49744 --- [lucid-backoffice] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T21:49:19.161+01:00  INFO 49744 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T21:49:19.186+01:00  INFO 49744 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T21:49:19.188+01:00  INFO 49744 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T21:49:22.199+01:00  INFO 49744 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T21:49:22.402+01:00  INFO 49744 --- [lucid-backoffice] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE/*************:lucid-backoffice:8074": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE/*************:lucid-backoffice:8074": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 38 more

2025-07-07T21:49:22.403+01:00  WARN 49744 --- [lucid-backoffice] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-BACKOFFICE/*************:lucid-backoffice:8074": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T21:49:22.404+01:00 ERROR 49744 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-BACKOFFICE/*************:lucid-backoffice:8074 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-3.4.1.jar:3.4.1]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147) ~[spring-boot-3.4.1.jar:3.4.1]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116) ~[spring-boot-3.4.1.jar:3.4.1]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T21:49:22.409+01:00  INFO 49744 --- [lucid-backoffice] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T21:58:47.189+01:00  INFO 54722 --- [lucid-backoffice] [main] c.d.LucidBackofficeServiceApplication    : Starting LucidBackofficeServiceApplication using Java 21.0.1 with PID 54722 (/Users/<USER>/Desktop/LUCID/lucid-backoffice-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T21:58:47.190+01:00  INFO 54722 --- [lucid-backoffice] [main] c.d.LucidBackofficeServiceApplication    : The following 6 profiles are active: "dev", "messages", "mails", "coronation", "coronation-provider", "enterprise"
2025-07-07T21:58:47.212+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T21:58:47.212+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-backoffice, profiles=[default], label=null, version=null, state=null
2025-07-07T21:58:47.212+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T21:58:47.212+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-backoffice, profiles=[dev,messages,mails,coronation,coronation-provider,enterprise], label=null, version=null, state=null
2025-07-07T21:58:48.059+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T21:58:48.191+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 127 ms. Found 16 JPA repository interfaces.
2025-07-07T21:58:48.195+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T21:58:48.209+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 4 JPA repository interfaces.
2025-07-07T21:58:48.210+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T21:58:48.213+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T21:58:48.213+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T21:58:48.218+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-07T21:58:48.246+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T21:58:48.249+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T21:58:48.250+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T21:58:48.252+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T21:58:48.796+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=a351f2d9-f035-3602-8ffd-3d1da8d64acf
2025-07-07T21:58:49.009+01:00  WARN 54722 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T21:58:49.013+01:00  WARN 54722 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T21:58:49.015+01:00  WARN 54722 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x0000007001696558] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T21:58:49.017+01:00  WARN 54722 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T21:58:49.022+01:00  WARN 54722 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T21:58:49.025+01:00  WARN 54722 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T21:58:49.027+01:00  WARN 54722 --- [lucid-backoffice] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T21:58:49.221+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8074 (http)
2025-07-07T21:58:49.232+01:00  INFO 54722 --- [lucid-backoffice] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T21:58:49.232+01:00  INFO 54722 --- [lucid-backoffice] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T21:58:49.282+01:00  INFO 54722 --- [lucid-backoffice] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T21:58:49.282+01:00  INFO 54722 --- [lucid-backoffice] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2069 ms
2025-07-07T21:58:49.611+01:00  INFO 54722 --- [lucid-backoffice] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T21:58:49.653+01:00  INFO 54722 --- [lucid-backoffice] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.4.Final
2025-07-07T21:58:49.675+01:00  INFO 54722 --- [lucid-backoffice] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T21:58:49.817+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T21:58:49.844+01:00  INFO 54722 --- [lucid-backoffice] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T21:58:50.023+01:00  INFO 54722 --- [lucid-backoffice] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3b2317b7
2025-07-07T21:58:50.024+01:00  INFO 54722 --- [lucid-backoffice] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T21:58:50.131+01:00  INFO 54722 --- [lucid-backoffice] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T21:58:51.030+01:00  INFO 54722 --- [lucid-backoffice] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T21:58:51.239+01:00  INFO 54722 --- [lucid-backoffice] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T21:58:51.439+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T21:58:53.005+01:00  INFO 54722 --- [lucid-backoffice] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T21:58:53.237+01:00  WARN 54722 --- [lucid-backoffice] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T21:58:53.922+01:00  INFO 54722 --- [lucid-backoffice] [redisson-netty-1-6] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T21:58:54.582+01:00  INFO 54722 --- [lucid-backoffice] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T21:58:55.400+01:00  INFO 54722 --- [lucid-backoffice] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T21:59:04.102+01:00  WARN 54722 --- [lucid-backoffice] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T21:59:04.239+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-customer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T21:59:04.347+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T21:59:04.445+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T21:59:04.494+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-administration' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T21:59:04.789+01:00  WARN 54722 --- [lucid-backoffice] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T21:59:05.073+01:00  INFO 54722 --- [lucid-backoffice] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name backOfficeUserLoginService
2025-07-07T21:59:05.545+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T21:59:06.083+01:00  WARN 54722 --- [lucid-backoffice] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T21:59:06.254+01:00  INFO 54722 --- [lucid-backoffice] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T21:59:06.376+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T21:59:06.390+01:00  INFO 54722 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T21:59:06.392+01:00  INFO 54722 --- [lucid-backoffice] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T21:59:06.397+01:00  INFO 54722 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T21:59:06.397+01:00  INFO 54722 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T21:59:06.397+01:00  INFO 54722 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T21:59:06.397+01:00  INFO 54722 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T21:59:06.397+01:00  INFO 54722 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T21:59:06.397+01:00  INFO 54722 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T21:59:06.397+01:00  INFO 54722 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T21:59:06.727+01:00  INFO 54722 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T21:59:06.727+01:00  INFO 54722 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T21:59:06.728+01:00  INFO 54722 --- [lucid-backoffice] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T21:59:06.729+01:00  INFO 54722 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751921946728 with initial instances count: 0
2025-07-07T21:59:06.736+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-BACKOFFICE with eureka with status UP
2025-07-07T21:59:06.736+01:00  INFO 54722 --- [lucid-backoffice] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751921946736, current=UP, previous=STARTING]
2025-07-07T21:59:06.737+01:00  INFO 54722 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-BACKOFFICE/*************:lucid-backoffice:8074: registering service...
2025-07-07T21:59:06.744+01:00  INFO 54722 --- [lucid-backoffice] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8074 (http) with context path '/'
2025-07-07T21:59:06.745+01:00  INFO 54722 --- [lucid-backoffice] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8074
2025-07-07T21:59:06.815+01:00  INFO 54722 --- [lucid-backoffice] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-BACKOFFICE/*************:lucid-backoffice:8074 - registration status: 204
2025-07-07T21:59:06.878+01:00  INFO 54722 --- [lucid-backoffice] [main] c.d.LucidBackofficeServiceApplication    : Started LucidBackofficeServiceApplication in 20.194 seconds (process running for 25.943)
2025-07-07T21:59:07.387+01:00  INFO 54722 --- [lucid-backoffice] [RMI TCP Connection(2)-*************] o.s.a.r.c.CachingConnectionFactory       : Attempting to connect to: [localhost:5672]
2025-07-07T21:59:07.388+01:00  INFO 54722 --- [lucid-backoffice] [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T21:59:07.389+01:00  INFO 54722 --- [lucid-backoffice] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T21:59:07.394+01:00  INFO 54722 --- [lucid-backoffice] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 5 ms
2025-07-07T21:59:08.460+01:00  INFO 54722 --- [lucid-backoffice] [RMI TCP Connection(2)-*************] o.s.a.r.c.CachingConnectionFactory       : Created new connection: rabbitConnectionFactory#34afee7c:0/SimpleConnection@71fa5413 [delegate=amqp://guest@127.0.0.1:5672/, localPort=60926]
2025-07-07T21:59:08.542+01:00  INFO 54722 --- [lucid-backoffice] [RMI TCP Connection(2)-*************] o.s.amqp.rabbit.core.RabbitAdmin         : Auto-declaring a non-durable, auto-delete, or exclusive Queue (lucidNotification) durable:false, auto-delete:false, exclusive:false. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-07T21:59:36.734+01:00  INFO 54722 --- [lucid-backoffice] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T21:59:36.736+01:00  INFO 54722 --- [lucid-backoffice] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T21:59:36.736+01:00  INFO 54722 --- [lucid-backoffice] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T21:59:36.736+01:00  INFO 54722 --- [lucid-backoffice] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T21:59:36.737+01:00  INFO 54722 --- [lucid-backoffice] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T21:59:36.737+01:00  INFO 54722 --- [lucid-backoffice] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application version is -1: false
2025-07-07T21:59:36.737+01:00  INFO 54722 --- [lucid-backoffice] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T21:59:36.793+01:00  INFO 54722 --- [lucid-backoffice] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T22:04:06.395+01:00  INFO 54722 --- [lucid-backoffice] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T22:09:06.356+01:00  INFO 54722 --- [lucid-backoffice] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
