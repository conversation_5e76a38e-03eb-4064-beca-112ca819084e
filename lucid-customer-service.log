2025-07-07T15:34:35.220+01:00  INFO 97896 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 97896 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T15:34:35.221+01:00  INFO 97896 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T15:34:35.242+01:00  INFO 97896 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:34:35.242+01:00  INFO 97896 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T15:34:35.242+01:00  INFO 97896 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:34:35.242+01:00  INFO 97896 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T15:34:36.057+01:00  INFO 97896 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:34:36.213+01:00  INFO 97896 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 151 ms. Found 19 JPA repository interfaces.
2025-07-07T15:34:36.217+01:00  INFO 97896 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:34:36.228+01:00  INFO 97896 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T15:34:36.228+01:00  INFO 97896 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:34:36.231+01:00  INFO 97896 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:34:36.232+01:00  INFO 97896 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:34:36.234+01:00  INFO 97896 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-07T15:34:36.260+01:00  INFO 97896 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:34:36.264+01:00  INFO 97896 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:34:36.264+01:00  INFO 97896 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:34:36.268+01:00  INFO 97896 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:34:36.887+01:00  INFO 97896 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=2491e3bc-2786-3d2a-8f3e-140b5552247a
2025-07-07T15:34:37.098+01:00  WARN 97896 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:34:37.102+01:00  WARN 97896 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:34:37.104+01:00  WARN 97896 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x00000070016a7dc0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:34:37.107+01:00  WARN 97896 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:34:37.123+01:00  WARN 97896 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:34:37.127+01:00  WARN 97896 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:34:37.141+01:00  WARN 97896 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T15:34:37.416+01:00  INFO 97896 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T15:34:37.423+01:00  INFO 97896 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T15:34:37.423+01:00  INFO 97896 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T15:34:37.459+01:00  INFO 97896 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T15:34:37.459+01:00  INFO 97896 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2215 ms
2025-07-07T15:34:37.752+01:00  INFO 97896 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T15:34:37.792+01:00  INFO 97896 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T15:34:37.812+01:00  INFO 97896 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T15:34:37.952+01:00  INFO 97896 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T15:34:37.976+01:00  INFO 97896 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T15:34:38.124+01:00  INFO 97896 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5096be74
2025-07-07T15:34:38.125+01:00  INFO 97896 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T15:34:38.200+01:00  INFO 97896 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T15:34:39.071+01:00  INFO 97896 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T15:34:39.331+01:00  INFO 97896 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:34:39.540+01:00  INFO 97896 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T15:34:40.656+01:00  INFO 97896 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T15:34:40.910+01:00  INFO 97896 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T15:34:40.946+01:00  WARN 97896 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T15:34:41.057+01:00  INFO 97896 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:34:41.154+01:00  INFO 97896 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:34:42.383+01:00  WARN 97896 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T15:34:42.490+01:00  INFO 97896 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:34:42.763+01:00  INFO 97896 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:34:42.846+01:00  WARN 97896 --- [lucid-customer] [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'customLoginSanityCheck': Invocation of init method failed
2025-07-07T15:34:42.847+01:00  WARN 97896 --- [lucid-customer] [main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'meterRegistryCloser': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:275) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:267) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:223) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:460) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:114) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:640) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.2.jar:3.4.2]
	at com.digicore.LucidCustomerServiceApplication.main(LucidCustomerServiceApplication.java:27) ~[classes/:na]

2025-07-07T15:34:42.852+01:00  WARN 97896 --- [lucid-customer] [main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'meterRegistryCloser': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:275) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:267) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:223) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:460) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:114) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:640) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.2.jar:3.4.2]
	at com.digicore.LucidCustomerServiceApplication.main(LucidCustomerServiceApplication.java:27) ~[classes/:na]

2025-07-07T15:34:42.866+01:00  INFO 97896 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:34:42.867+01:00  INFO 97896 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T15:34:42.871+01:00  INFO 97896 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T15:34:42.874+01:00  INFO 97896 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-07-07T15:34:42.887+01:00  INFO 97896 --- [lucid-customer] [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-07T15:34:42.902+01:00 ERROR 97896 --- [lucid-customer] [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'customLoginSanityCheck': Invocation of init method failed
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:222) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:423) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1122) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1093) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1030) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.2.jar:3.4.2]
	at com.digicore.LucidCustomerServiceApplication.main(LucidCustomerServiceApplication.java:27) ~[classes/:na]
Caused by: java.lang.IllegalStateException: Cannot enable default login when a custom login is active.
	at com.digicore.lucid.customer.service.config.CustomLoginSanityCheck.validate(CustomLoginSanityCheck.java:59) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219) ~[spring-beans-6.2.2.jar:6.2.2]
	... 20 common frames omitted

2025-07-07T15:35:14.845+01:00  INFO 98025 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 98025 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T15:35:14.846+01:00  INFO 98025 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T15:35:14.868+01:00  INFO 98025 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:35:14.868+01:00  INFO 98025 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T15:35:14.869+01:00  INFO 98025 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:35:14.869+01:00  INFO 98025 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T15:35:15.654+01:00  INFO 98025 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:35:15.810+01:00  INFO 98025 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 150 ms. Found 19 JPA repository interfaces.
2025-07-07T15:35:15.813+01:00  INFO 98025 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:35:15.825+01:00  INFO 98025 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 4 JPA repository interfaces.
2025-07-07T15:35:15.826+01:00  INFO 98025 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:35:15.829+01:00  INFO 98025 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:35:15.829+01:00  INFO 98025 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:35:15.831+01:00  INFO 98025 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-07T15:35:15.858+01:00  INFO 98025 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:35:15.862+01:00  INFO 98025 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:35:15.862+01:00  INFO 98025 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:35:15.866+01:00  INFO 98025 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-07-07T15:35:16.432+01:00  INFO 98025 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=23bc0fb5-637d-308d-8a9f-28ae5f7dca9b
2025-07-07T15:35:16.633+01:00  WARN 98025 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:35:16.635+01:00  WARN 98025 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:35:16.636+01:00  WARN 98025 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000700169ec40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:35:16.638+01:00  WARN 98025 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:35:16.645+01:00  WARN 98025 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:35:16.649+01:00  WARN 98025 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:35:16.651+01:00  WARN 98025 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T15:35:16.883+01:00  INFO 98025 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T15:35:16.892+01:00  INFO 98025 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T15:35:16.892+01:00  INFO 98025 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T15:35:16.952+01:00  INFO 98025 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T15:35:16.952+01:00  INFO 98025 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2082 ms
2025-07-07T15:35:17.231+01:00  INFO 98025 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T15:35:17.270+01:00  INFO 98025 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T15:35:17.291+01:00  INFO 98025 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T15:35:17.432+01:00  INFO 98025 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T15:35:17.455+01:00  INFO 98025 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T15:35:17.563+01:00  INFO 98025 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1df77353
2025-07-07T15:35:17.564+01:00  INFO 98025 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T15:35:17.616+01:00  INFO 98025 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T15:35:18.485+01:00  INFO 98025 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T15:35:18.625+01:00  INFO 98025 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:35:18.840+01:00  INFO 98025 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T15:35:19.952+01:00  INFO 98025 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T15:35:20.196+01:00  INFO 98025 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T15:35:20.230+01:00  WARN 98025 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T15:35:20.314+01:00  INFO 98025 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:35:20.422+01:00  INFO 98025 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:35:21.648+01:00  WARN 98025 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T15:35:21.774+01:00  INFO 98025 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:35:22.021+01:00  INFO 98025 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:35:22.138+01:00  WARN 98025 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T15:35:22.151+01:00  WARN 98025 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T15:35:28.100+01:00  INFO 98025 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:35:28.568+01:00  WARN 98025 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T15:35:29.024+01:00  INFO 98025 --- [lucid-customer] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T15:35:29.517+01:00  WARN 98025 --- [lucid-customer] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T15:35:29.669+01:00  INFO 98025 --- [lucid-customer] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T15:35:29.786+01:00  INFO 98025 --- [lucid-customer] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T15:35:29.800+01:00  INFO 98025 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T15:35:29.802+01:00  INFO 98025 --- [lucid-customer] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T15:35:29.806+01:00  INFO 98025 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T15:35:29.806+01:00  INFO 98025 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T15:35:29.806+01:00  INFO 98025 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T15:35:29.806+01:00  INFO 98025 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T15:35:29.806+01:00  INFO 98025 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T15:35:29.806+01:00  INFO 98025 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T15:35:29.806+01:00  INFO 98025 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T15:35:30.099+01:00  INFO 98025 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T15:35:30.101+01:00  INFO 98025 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T15:35:30.102+01:00  INFO 98025 --- [lucid-customer] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T15:35:30.102+01:00  INFO 98025 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751898930102 with initial instances count: 0
2025-07-07T15:35:30.110+01:00  INFO 98025 --- [lucid-customer] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CUSTOMER with eureka with status UP
2025-07-07T15:35:30.110+01:00  INFO 98025 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751898930110, current=UP, previous=STARTING]
2025-07-07T15:35:30.111+01:00  INFO 98025 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T15:35:30.118+01:00  INFO 98025 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8075 (http) with context path '/'
2025-07-07T15:35:30.118+01:00  INFO 98025 --- [lucid-customer] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8075
2025-07-07T15:35:30.157+01:00  INFO 98025 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T15:35:30.227+01:00  INFO 98025 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Started LucidCustomerServiceApplication in 15.876 seconds (process running for 21.525)
2025-07-07T15:35:30.856+01:00  INFO 98025 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T15:35:30.856+01:00  INFO 98025 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T15:35:30.859+01:00  INFO 98025 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-07T15:35:30.860+01:00  INFO 98025 --- [lucid-customer] [RMI TCP Connection(1)-*************] o.s.a.r.c.CachingConnectionFactory       : Attempting to connect to: [localhost:5672]
2025-07-07T15:35:31.488+01:00  INFO 98025 --- [lucid-customer] [RMI TCP Connection(1)-*************] o.s.a.r.c.CachingConnectionFactory       : Created new connection: rabbitConnectionFactory#3631dfac:0/SimpleConnection@215dc953 [delegate=amqp://guest@127.0.0.1:5672/, localPort=52344]
2025-07-07T15:35:31.498+01:00  INFO 98025 --- [lucid-customer] [RMI TCP Connection(1)-*************] o.s.amqp.rabbit.core.RabbitAdmin         : Auto-declaring a non-durable, auto-delete, or exclusive Queue (lucidNotification) durable:false, auto-delete:false, exclusive:false. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-07T15:35:46.459+01:00  INFO 98025 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CUSTOMER with eureka with status DOWN
2025-07-07T15:35:46.460+01:00  INFO 98025 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751898946460, current=DOWN, previous=UP]
2025-07-07T15:35:46.460+01:00  INFO 98025 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T15:35:46.497+01:00  INFO 98025 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T15:35:46.504+01:00  INFO 98025 --- [lucid-customer] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T15:35:46.519+01:00  INFO 98025 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T15:35:46.579+01:00  INFO 98025 --- [lucid-customer] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:35:46.582+01:00  INFO 98025 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T15:35:46.588+01:00  INFO 98025 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T15:35:46.589+01:00  INFO 98025 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T15:35:49.595+01:00  INFO 98025 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T15:35:49.626+01:00  INFO 98025 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - deregister  status: 200
2025-07-07T15:35:49.627+01:00  INFO 98025 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T15:35:56.860+01:00  INFO 98164 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 98164 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T15:35:56.862+01:00  INFO 98164 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T15:35:56.882+01:00  INFO 98164 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:35:56.882+01:00  INFO 98164 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T15:35:56.883+01:00  INFO 98164 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:35:56.883+01:00  INFO 98164 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T15:35:57.637+01:00  INFO 98164 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:35:57.783+01:00  INFO 98164 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 140 ms. Found 19 JPA repository interfaces.
2025-07-07T15:35:57.787+01:00  INFO 98164 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:35:57.798+01:00  INFO 98164 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T15:35:57.798+01:00  INFO 98164 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:35:57.801+01:00  INFO 98164 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:35:57.801+01:00  INFO 98164 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:35:57.804+01:00  INFO 98164 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-07T15:35:57.831+01:00  INFO 98164 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:35:57.835+01:00  INFO 98164 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:35:57.835+01:00  INFO 98164 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:35:57.838+01:00  INFO 98164 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:35:58.385+01:00  INFO 98164 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=23bc0fb5-637d-308d-8a9f-28ae5f7dca9b
2025-07-07T15:35:58.592+01:00  WARN 98164 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:35:58.595+01:00  WARN 98164 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:35:58.597+01:00  WARN 98164 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x0000009801696e98] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:35:58.599+01:00  WARN 98164 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:35:58.605+01:00  WARN 98164 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:35:58.608+01:00  WARN 98164 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:35:58.610+01:00  WARN 98164 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T15:35:58.805+01:00  INFO 98164 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T15:35:58.814+01:00  INFO 98164 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T15:35:58.815+01:00  INFO 98164 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T15:35:58.864+01:00  INFO 98164 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T15:35:58.864+01:00  INFO 98164 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1980 ms
2025-07-07T15:35:59.153+01:00  INFO 98164 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T15:35:59.187+01:00  INFO 98164 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T15:35:59.205+01:00  INFO 98164 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T15:35:59.334+01:00  INFO 98164 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T15:35:59.352+01:00  INFO 98164 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T15:35:59.466+01:00  INFO 98164 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@66e5441e
2025-07-07T15:35:59.467+01:00  INFO 98164 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T15:35:59.518+01:00  INFO 98164 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T15:36:00.360+01:00  INFO 98164 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T15:36:00.488+01:00  INFO 98164 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:36:00.741+01:00  INFO 98164 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T15:36:01.796+01:00  INFO 98164 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T15:36:02.026+01:00  INFO 98164 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T15:36:02.058+01:00  WARN 98164 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T15:36:02.141+01:00  INFO 98164 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:36:02.200+01:00  INFO 98164 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:36:03.406+01:00  WARN 98164 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T15:36:03.522+01:00  INFO 98164 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:36:03.764+01:00  INFO 98164 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:36:03.832+01:00  WARN 98164 --- [lucid-customer] [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'customLoginSanityCheck': Invocation of init method failed
2025-07-07T15:36:03.833+01:00  WARN 98164 --- [lucid-customer] [main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'meterRegistryCloser': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:275) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:267) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:223) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:460) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:114) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:640) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.2.jar:3.4.2]
	at com.digicore.LucidCustomerServiceApplication.main(LucidCustomerServiceApplication.java:27) ~[classes/:na]

2025-07-07T15:36:03.836+01:00  WARN 98164 --- [lucid-customer] [main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'meterRegistryCloser': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:275) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:267) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:223) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:460) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:114) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:640) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.2.jar:3.4.2]
	at com.digicore.LucidCustomerServiceApplication.main(LucidCustomerServiceApplication.java:27) ~[classes/:na]

2025-07-07T15:36:03.846+01:00  INFO 98164 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:36:03.847+01:00  INFO 98164 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T15:36:03.849+01:00  INFO 98164 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T15:36:03.852+01:00  INFO 98164 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-07-07T15:36:03.863+01:00  INFO 98164 --- [lucid-customer] [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-07T15:36:03.876+01:00 ERROR 98164 --- [lucid-customer] [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'customLoginSanityCheck': Invocation of init method failed
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:222) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:423) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1122) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1093) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1030) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.2.jar:3.4.2]
	at com.digicore.LucidCustomerServiceApplication.main(LucidCustomerServiceApplication.java:27) ~[classes/:na]
Caused by: java.lang.IllegalStateException: Only one custom login can be active at a time.
	at com.digicore.lucid.customer.service.config.CustomLoginSanityCheck.validate(CustomLoginSanityCheck.java:63) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219) ~[spring-beans-6.2.2.jar:6.2.2]
	... 20 common frames omitted

2025-07-07T15:36:20.459+01:00  INFO 98248 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 98248 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T15:36:20.461+01:00  INFO 98248 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T15:36:20.482+01:00  INFO 98248 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:36:20.482+01:00  INFO 98248 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T15:36:20.483+01:00  INFO 98248 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:36:20.483+01:00  INFO 98248 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T15:36:21.212+01:00  INFO 98248 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:36:21.352+01:00  INFO 98248 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 134 ms. Found 19 JPA repository interfaces.
2025-07-07T15:36:21.355+01:00  INFO 98248 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:36:21.367+01:00  INFO 98248 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T15:36:21.367+01:00  INFO 98248 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:36:21.370+01:00  INFO 98248 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:36:21.370+01:00  INFO 98248 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:36:21.372+01:00  INFO 98248 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-07T15:36:21.398+01:00  INFO 98248 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:36:21.401+01:00  INFO 98248 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:36:21.402+01:00  INFO 98248 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:36:21.404+01:00  INFO 98248 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:36:21.956+01:00  INFO 98248 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=23bc0fb5-637d-308d-8a9f-28ae5f7dca9b
2025-07-07T15:36:22.154+01:00  WARN 98248 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:36:22.156+01:00  WARN 98248 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:36:22.157+01:00  WARN 98248 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000d00169d2e8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:36:22.159+01:00  WARN 98248 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:36:22.164+01:00  WARN 98248 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:36:22.168+01:00  WARN 98248 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:36:22.171+01:00  WARN 98248 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T15:36:22.392+01:00  INFO 98248 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T15:36:22.400+01:00  INFO 98248 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T15:36:22.401+01:00  INFO 98248 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T15:36:22.452+01:00  INFO 98248 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T15:36:22.452+01:00  INFO 98248 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1968 ms
2025-07-07T15:36:22.739+01:00  INFO 98248 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T15:36:22.774+01:00  INFO 98248 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T15:36:22.792+01:00  INFO 98248 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T15:36:22.917+01:00  INFO 98248 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T15:36:22.935+01:00  INFO 98248 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T15:36:23.045+01:00  INFO 98248 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@13e12ab6
2025-07-07T15:36:23.046+01:00  INFO 98248 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T15:36:23.098+01:00  INFO 98248 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T15:36:23.946+01:00  INFO 98248 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T15:36:24.079+01:00  INFO 98248 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:36:24.280+01:00  INFO 98248 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T15:36:25.360+01:00  INFO 98248 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T15:36:25.596+01:00  INFO 98248 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T15:36:25.627+01:00  WARN 98248 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T15:36:25.706+01:00  INFO 98248 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:36:25.777+01:00  INFO 98248 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:36:26.947+01:00  WARN 98248 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T15:36:27.051+01:00  INFO 98248 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:36:27.300+01:00  INFO 98248 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:36:27.395+01:00  WARN 98248 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T15:36:27.410+01:00  WARN 98248 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T15:36:34.213+01:00  INFO 98248 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:36:34.641+01:00  WARN 98248 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T15:36:35.062+01:00  INFO 98248 --- [lucid-customer] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T15:36:35.533+01:00  WARN 98248 --- [lucid-customer] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T15:36:35.708+01:00  INFO 98248 --- [lucid-customer] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T15:36:35.829+01:00  INFO 98248 --- [lucid-customer] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T15:36:35.843+01:00  INFO 98248 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T15:36:35.844+01:00  INFO 98248 --- [lucid-customer] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T15:36:35.848+01:00  INFO 98248 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T15:36:35.848+01:00  INFO 98248 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T15:36:35.848+01:00  INFO 98248 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T15:36:35.848+01:00  INFO 98248 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T15:36:35.848+01:00  INFO 98248 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T15:36:35.848+01:00  INFO 98248 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T15:36:35.848+01:00  INFO 98248 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T15:36:35.961+01:00  INFO 98248 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T15:36:35.961+01:00  INFO 98248 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T15:36:35.962+01:00  INFO 98248 --- [lucid-customer] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T15:36:35.963+01:00  INFO 98248 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751898995962 with initial instances count: 0
2025-07-07T15:36:35.969+01:00  INFO 98248 --- [lucid-customer] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CUSTOMER with eureka with status UP
2025-07-07T15:36:35.969+01:00  INFO 98248 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751898995969, current=UP, previous=STARTING]
2025-07-07T15:36:35.970+01:00  INFO 98248 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T15:36:35.977+01:00  INFO 98248 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8075 (http) with context path '/'
2025-07-07T15:36:35.978+01:00  INFO 98248 --- [lucid-customer] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8075
2025-07-07T15:36:36.002+01:00  INFO 98248 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T15:36:36.124+01:00  INFO 98248 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Started LucidCustomerServiceApplication in 16.116 seconds (process running for 21.64)
2025-07-07T15:36:36.603+01:00  INFO 98248 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.a.r.c.CachingConnectionFactory       : Attempting to connect to: [localhost:5672]
2025-07-07T15:36:36.610+01:00  INFO 98248 --- [lucid-customer] [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T15:36:36.610+01:00  INFO 98248 --- [lucid-customer] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T15:36:36.612+01:00  INFO 98248 --- [lucid-customer] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-07T15:36:36.688+01:00  INFO 98248 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.a.r.c.CachingConnectionFactory       : Created new connection: rabbitConnectionFactory#4226d302:0/SimpleConnection@130b321 [delegate=amqp://guest@127.0.0.1:5672/, localPort=52463]
2025-07-07T15:36:36.695+01:00  INFO 98248 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.amqp.rabbit.core.RabbitAdmin         : Auto-declaring a non-durable, auto-delete, or exclusive Queue (lucidNotification) durable:false, auto-delete:false, exclusive:false. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-07T15:37:05.963+01:00  INFO 98248 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T15:37:05.965+01:00  INFO 98248 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T15:37:05.966+01:00  INFO 98248 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T15:37:05.966+01:00  INFO 98248 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T15:37:05.966+01:00  INFO 98248 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T15:37:05.966+01:00  INFO 98248 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application version is -1: false
2025-07-07T15:37:05.966+01:00  INFO 98248 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T15:37:06.018+01:00  INFO 98248 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T15:37:12.464+01:00  INFO 98248 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CUSTOMER with eureka with status DOWN
2025-07-07T15:37:12.465+01:00  INFO 98248 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751899032465, current=DOWN, previous=UP]
2025-07-07T15:37:12.465+01:00  INFO 98248 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T15:37:12.480+01:00  INFO 98248 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T15:37:12.487+01:00  INFO 98248 --- [lucid-customer] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T15:37:12.504+01:00  INFO 98248 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T15:37:12.570+01:00  INFO 98248 --- [lucid-customer] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:37:12.573+01:00  INFO 98248 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T15:37:12.578+01:00  INFO 98248 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T15:37:12.579+01:00  INFO 98248 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T15:37:15.585+01:00  INFO 98248 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T15:37:15.597+01:00  INFO 98248 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - deregister  status: 200
2025-07-07T15:37:15.597+01:00  INFO 98248 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T15:37:26.464+01:00  INFO 98539 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 98539 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T15:37:26.466+01:00  INFO 98539 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T15:37:26.491+01:00  INFO 98539 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:37:26.491+01:00  INFO 98539 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T15:37:26.491+01:00  INFO 98539 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:37:26.491+01:00  INFO 98539 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T15:37:27.318+01:00  INFO 98539 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:37:27.470+01:00  INFO 98539 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 145 ms. Found 19 JPA repository interfaces.
2025-07-07T15:37:27.474+01:00  INFO 98539 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:37:27.484+01:00  INFO 98539 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 4 JPA repository interfaces.
2025-07-07T15:37:27.485+01:00  INFO 98539 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:37:27.488+01:00  INFO 98539 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:37:27.488+01:00  INFO 98539 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:37:27.491+01:00  INFO 98539 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-07T15:37:27.520+01:00  INFO 98539 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:37:27.523+01:00  INFO 98539 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:37:27.523+01:00  INFO 98539 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:37:27.533+01:00  INFO 98539 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 1 JPA repository interface.
2025-07-07T15:37:28.128+01:00  INFO 98539 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=23bc0fb5-637d-308d-8a9f-28ae5f7dca9b
2025-07-07T15:37:28.424+01:00  WARN 98539 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:37:28.427+01:00  WARN 98539 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:37:28.428+01:00  WARN 98539 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000070169e360] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:37:28.430+01:00  WARN 98539 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:37:28.436+01:00  WARN 98539 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:37:28.440+01:00  WARN 98539 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:37:28.442+01:00  WARN 98539 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T15:37:28.701+01:00  INFO 98539 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T15:37:28.711+01:00  INFO 98539 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T15:37:28.712+01:00  INFO 98539 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T15:37:28.762+01:00  INFO 98539 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T15:37:28.762+01:00  INFO 98539 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2270 ms
2025-07-07T15:37:29.070+01:00  INFO 98539 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T15:37:29.114+01:00  INFO 98539 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T15:37:29.137+01:00  INFO 98539 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T15:37:29.299+01:00  INFO 98539 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T15:37:29.336+01:00  INFO 98539 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T15:37:29.434+01:00  INFO 98539 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1007954a
2025-07-07T15:37:29.435+01:00  INFO 98539 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T15:37:29.487+01:00  INFO 98539 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T15:37:30.432+01:00  INFO 98539 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T15:37:30.567+01:00  INFO 98539 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:37:30.821+01:00  INFO 98539 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T15:37:31.913+01:00  INFO 98539 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T15:37:32.173+01:00  INFO 98539 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T15:37:32.212+01:00  WARN 98539 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T15:37:32.308+01:00  INFO 98539 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:37:32.368+01:00  INFO 98539 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:37:33.605+01:00  WARN 98539 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T15:37:33.713+01:00  INFO 98539 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:37:33.967+01:00  INFO 98539 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:37:34.059+01:00  WARN 98539 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T15:37:34.074+01:00  WARN 98539 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T15:37:46.428+01:00  INFO 98539 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:37:47.979+01:00  WARN 98539 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T15:37:49.664+01:00  INFO 98539 --- [lucid-customer] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T15:37:50.441+01:00  WARN 98539 --- [lucid-customer] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T15:37:50.686+01:00  INFO 98539 --- [lucid-customer] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T15:37:50.875+01:00  INFO 98539 --- [lucid-customer] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T15:37:50.919+01:00  INFO 98539 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T15:37:50.924+01:00  INFO 98539 --- [lucid-customer] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T15:37:50.935+01:00  INFO 98539 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T15:37:50.936+01:00  INFO 98539 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T15:37:50.936+01:00  INFO 98539 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T15:37:50.936+01:00  INFO 98539 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T15:37:50.936+01:00  INFO 98539 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T15:37:50.936+01:00  INFO 98539 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T15:37:50.936+01:00  INFO 98539 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T15:37:51.271+01:00  INFO 98539 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T15:37:51.273+01:00  INFO 98539 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T15:37:51.275+01:00  INFO 98539 --- [lucid-customer] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T15:37:51.277+01:00  INFO 98539 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751899071276 with initial instances count: 0
2025-07-07T15:37:51.285+01:00  INFO 98539 --- [lucid-customer] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CUSTOMER with eureka with status UP
2025-07-07T15:37:51.286+01:00  INFO 98539 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751899071286, current=UP, previous=STARTING]
2025-07-07T15:37:51.288+01:00  INFO 98539 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T15:37:51.303+01:00  INFO 98539 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8075 (http) with context path '/'
2025-07-07T15:37:51.304+01:00  INFO 98539 --- [lucid-customer] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8075
2025-07-07T15:37:51.328+01:00  INFO 98539 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T15:37:51.478+01:00  INFO 98539 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Started LucidCustomerServiceApplication in 25.559 seconds (process running for 31.709)
2025-07-07T15:38:21.280+01:00  INFO 98539 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T15:38:21.283+01:00  INFO 98539 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T15:38:21.283+01:00  INFO 98539 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T15:38:21.283+01:00  INFO 98539 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T15:38:21.283+01:00  INFO 98539 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T15:38:21.284+01:00  INFO 98539 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application version is -1: false
2025-07-07T15:38:21.284+01:00  INFO 98539 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T15:38:21.351+01:00  INFO 98539 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T15:39:01.554+01:00  INFO 98539 --- [lucid-customer] [http-nio-8075-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T15:39:01.557+01:00  INFO 98539 --- [lucid-customer] [http-nio-8075-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T15:39:01.575+01:00  INFO 98539 --- [lucid-customer] [http-nio-8075-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 17 ms
2025-07-07T15:42:50.945+01:00  INFO 98539 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T15:42:57.498+01:00  INFO 98539 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CUSTOMER with eureka with status DOWN
2025-07-07T15:42:57.499+01:00  INFO 98539 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751899377499, current=DOWN, previous=UP]
2025-07-07T15:42:57.499+01:00  INFO 98539 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T15:42:57.509+01:00  INFO 98539 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T15:42:57.510+01:00  INFO 98539 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T15:42:57.514+01:00  INFO 98539 --- [lucid-customer] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T15:42:57.596+01:00  INFO 98539 --- [lucid-customer] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:42:57.604+01:00  INFO 98539 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T15:42:57.615+01:00  INFO 98539 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T15:42:57.616+01:00  INFO 98539 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T15:43:00.623+01:00  INFO 98539 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T15:43:00.641+01:00  INFO 98539 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - deregister  status: 200
2025-07-07T15:43:00.641+01:00  INFO 98539 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T15:43:08.847+01:00  INFO 99768 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 99768 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T15:43:08.849+01:00  INFO 99768 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T15:43:08.872+01:00  INFO 99768 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:43:08.872+01:00  INFO 99768 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T15:43:08.872+01:00  INFO 99768 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:43:08.872+01:00  INFO 99768 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T15:43:09.574+01:00  INFO 99768 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:43:09.702+01:00  INFO 99768 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 122 ms. Found 7 JPA repository interfaces.
2025-07-07T15:43:09.706+01:00  INFO 99768 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:43:09.717+01:00  INFO 99768 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T15:43:09.717+01:00  INFO 99768 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:43:09.720+01:00  INFO 99768 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:43:09.720+01:00  INFO 99768 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:43:09.723+01:00  INFO 99768 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:43:09.751+01:00  INFO 99768 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:43:09.755+01:00  INFO 99768 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:43:09.755+01:00  INFO 99768 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:43:09.758+01:00  INFO 99768 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:43:10.312+01:00  INFO 99768 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=6563597d-f026-3131-90ac-ca3db3f7b442
2025-07-07T15:43:10.542+01:00  WARN 99768 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:43:10.545+01:00  WARN 99768 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:43:10.546+01:00  WARN 99768 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x00000003016867d8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:43:10.548+01:00  WARN 99768 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:43:10.553+01:00  WARN 99768 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:43:10.556+01:00  WARN 99768 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:43:10.559+01:00  WARN 99768 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T15:43:10.767+01:00  INFO 99768 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T15:43:10.777+01:00  INFO 99768 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T15:43:10.777+01:00  INFO 99768 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T15:43:10.829+01:00  INFO 99768 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T15:43:10.830+01:00  INFO 99768 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1956 ms
2025-07-07T15:43:11.242+01:00  INFO 99768 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T15:43:11.290+01:00  INFO 99768 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T15:43:11.314+01:00  INFO 99768 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T15:43:11.477+01:00  INFO 99768 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T15:43:11.503+01:00  INFO 99768 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T15:43:11.647+01:00  INFO 99768 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@77eb76f
2025-07-07T15:43:11.649+01:00  INFO 99768 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T15:43:11.726+01:00  INFO 99768 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T15:43:11.855+01:00  WARN 99768 --- [lucid-customer] [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Type com.digicore.lucid.customer.data.modules.branch.converter.BranchContactPersonConverter not present
2025-07-07T15:43:11.855+01:00  INFO 99768 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T15:43:11.869+01:00  INFO 99768 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T15:43:11.871+01:00  INFO 99768 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-07-07T15:43:11.885+01:00  INFO 99768 --- [lucid-customer] [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-07T15:43:11.897+01:00 ERROR 99768 --- [lucid-customer] [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Type com.digicore.lucid.customer.data.modules.branch.converter.BranchContactPersonConverter not present
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:970) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.2.jar:3.4.2]
	at com.digicore.LucidCustomerServiceApplication.main(LucidCustomerServiceApplication.java:27) ~[classes/:na]
Caused by: java.lang.TypeNotPresentException: Type com.digicore.lucid.customer.data.modules.branch.converter.BranchContactPersonConverter not present
	at java.base/sun.reflect.annotation.TypeNotPresentExceptionProxy.generateException(TypeNotPresentExceptionProxy.java:47) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationInvocationHandler.invoke(AnnotationInvocationHandler.java:89) ~[na:na]
	at jdk.proxy2/jdk.proxy2.$Proxy188.converter(Unknown Source) ~[na:na]
	at org.hibernate.boot.model.internal.AttributeConversionInfo.<init>(AttributeConversionInfo.java:44) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.ClassPropertyHolder.startingProperty(ClassPropertyHolder.java:152) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.makePropertyAndValue(PropertyBinder.java:264) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.makePropertyValueAndBind(PropertyBinder.java:311) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.createBasicBinder(PropertyBinder.java:1251) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.bindBasic(PropertyBinder.java:1141) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.bindProperty(PropertyBinder.java:913) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.buildProperty(PropertyBinder.java:811) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.processElementAnnotations(PropertyBinder.java:732) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.EntityBinder.processIdPropertiesIfNotAlready(EntityBinder.java:1088) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.EntityBinder.handleIdentifier(EntityBinder.java:419) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.EntityBinder.bindEntityClass(EntityBinder.java:251) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.AnnotationBinder.bindClass(AnnotationBinder.java:401) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.source.internal.annotations.AnnotationMetadataSourceProcessorImpl.processEntityHierarchies(AnnotationMetadataSourceProcessorImpl.java:257) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess$1.processEntityHierarchies(MetadataBuildingProcess.java:281) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:324) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1431) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1502) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:66) ~[spring-orm-6.2.2.jar:6.2.2]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390) ~[spring-orm-6.2.2.jar:6.2.2]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:419) ~[spring-orm-6.2.2.jar:6.2.2]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:400) ~[spring-orm-6.2.2.jar:6.2.2]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366) ~[spring-orm-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-6.2.2.jar:6.2.2]
	... 15 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.digicore.lucid.customer.data.modules.branch.converter.BranchContactPersonConverter
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[na:na]
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	at java.base/java.lang.Class.forName0(Native Method) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:534) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:513) ~[na:na]
	at java.base/sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:114) ~[na:na]
	at java.base/sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:125) ~[na:na]
	at java.base/sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationParser.parseSig(AnnotationParser.java:442) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationParser.parseClassValue(AnnotationParser.java:428) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:347) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:282) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:121) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:73) ~[na:na]
	at java.base/java.lang.reflect.Field.declaredAnnotations(Field.java:1281) ~[na:na]
	at java.base/java.lang.reflect.Field.declaredAnnotations(Field.java:1279) ~[na:na]
	at java.base/java.lang.reflect.Field.getAnnotation(Field.java:1246) ~[na:na]
	at java.base/java.lang.reflect.AnnotatedElement.isAnnotationPresent(AnnotatedElement.java:292) ~[na:na]
	at java.base/java.lang.reflect.AccessibleObject.isAnnotationPresent(AccessibleObject.java:558) ~[na:na]
	at org.hibernate.annotations.common.reflection.java.JavaAnnotationReader.isAnnotationPresent(JavaAnnotationReader.java:30) ~[hibernate-commons-annotations-7.0.3.Final.jar:7.0.3.Final]
	at org.hibernate.annotations.common.reflection.java.JavaXAnnotatedElement.isAnnotationPresent(JavaXAnnotatedElement.java:40) ~[hibernate-commons-annotations-7.0.3.Final.jar:7.0.3.Final]
	at org.hibernate.annotations.common.reflection.java.JavaXMember.isAnnotationPresent(JavaXMember.java:21) ~[hibernate-commons-annotations-7.0.3.Final.jar:7.0.3.Final]
	at org.hibernate.boot.model.internal.InheritanceState.determineDefaultAccessType(InheritanceState.java:276) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.InheritanceState.getElementsToProcess(InheritanceState.java:226) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.InheritanceState.postProcess(InheritanceState.java:162) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.EntityBinder.handleIdentifier(EntityBinder.java:410) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	... 29 common frames omitted

2025-07-07T15:44:54.468+01:00  INFO 197 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 197 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T15:44:54.469+01:00  INFO 197 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T15:44:54.494+01:00  INFO 197 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:44:54.494+01:00  INFO 197 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T15:44:54.494+01:00  INFO 197 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:44:54.494+01:00  INFO 197 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T15:44:55.207+01:00  INFO 197 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:44:55.336+01:00  INFO 197 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 122 ms. Found 7 JPA repository interfaces.
2025-07-07T15:44:55.340+01:00  INFO 197 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:44:55.352+01:00  INFO 197 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 4 JPA repository interfaces.
2025-07-07T15:44:55.353+01:00  INFO 197 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:44:55.355+01:00  INFO 197 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:44:55.356+01:00  INFO 197 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:44:55.358+01:00  INFO 197 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:44:55.386+01:00  INFO 197 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:44:55.389+01:00  INFO 197 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:44:55.390+01:00  INFO 197 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:44:55.393+01:00  INFO 197 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:44:55.961+01:00  INFO 197 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=6563597d-f026-3131-90ac-ca3db3f7b442
2025-07-07T15:44:56.185+01:00  WARN 197 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:44:56.188+01:00  WARN 197 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:44:56.190+01:00  WARN 197 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x0000008801686ec0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:44:56.192+01:00  WARN 197 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:44:56.198+01:00  WARN 197 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:44:56.204+01:00  WARN 197 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:44:56.206+01:00  WARN 197 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T15:44:56.421+01:00  INFO 197 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T15:44:56.431+01:00  INFO 197 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T15:44:56.431+01:00  INFO 197 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T15:44:56.483+01:00  INFO 197 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T15:44:56.483+01:00  INFO 197 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1987 ms
2025-07-07T15:44:56.776+01:00  INFO 197 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T15:44:56.828+01:00  INFO 197 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T15:44:56.851+01:00  INFO 197 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T15:44:57.011+01:00  INFO 197 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T15:44:57.035+01:00  INFO 197 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T15:44:57.182+01:00  INFO 197 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@303a484e
2025-07-07T15:44:57.183+01:00  INFO 197 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T15:44:57.253+01:00  INFO 197 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T15:44:57.381+01:00  WARN 197 --- [lucid-customer] [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Type com.digicore.lucid.customer.data.modules.branch.converter.BranchContactPersonConverter not present
2025-07-07T15:44:57.382+01:00  INFO 197 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T15:44:57.399+01:00  INFO 197 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T15:44:57.402+01:00  INFO 197 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-07-07T15:44:57.422+01:00  INFO 197 --- [lucid-customer] [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-07T15:44:57.435+01:00 ERROR 197 --- [lucid-customer] [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Type com.digicore.lucid.customer.data.modules.branch.converter.BranchContactPersonConverter not present
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:970) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.2.jar:3.4.2]
	at com.digicore.LucidCustomerServiceApplication.main(LucidCustomerServiceApplication.java:27) ~[classes/:na]
Caused by: java.lang.TypeNotPresentException: Type com.digicore.lucid.customer.data.modules.branch.converter.BranchContactPersonConverter not present
	at java.base/sun.reflect.annotation.TypeNotPresentExceptionProxy.generateException(TypeNotPresentExceptionProxy.java:47) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationInvocationHandler.invoke(AnnotationInvocationHandler.java:89) ~[na:na]
	at jdk.proxy2/jdk.proxy2.$Proxy188.converter(Unknown Source) ~[na:na]
	at org.hibernate.boot.model.internal.AttributeConversionInfo.<init>(AttributeConversionInfo.java:44) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.ClassPropertyHolder.startingProperty(ClassPropertyHolder.java:152) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.makePropertyAndValue(PropertyBinder.java:264) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.makePropertyValueAndBind(PropertyBinder.java:311) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.createBasicBinder(PropertyBinder.java:1251) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.bindBasic(PropertyBinder.java:1141) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.bindProperty(PropertyBinder.java:913) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.buildProperty(PropertyBinder.java:811) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.PropertyBinder.processElementAnnotations(PropertyBinder.java:732) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.EntityBinder.processIdPropertiesIfNotAlready(EntityBinder.java:1088) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.EntityBinder.handleIdentifier(EntityBinder.java:419) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.EntityBinder.bindEntityClass(EntityBinder.java:251) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.AnnotationBinder.bindClass(AnnotationBinder.java:401) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.source.internal.annotations.AnnotationMetadataSourceProcessorImpl.processEntityHierarchies(AnnotationMetadataSourceProcessorImpl.java:257) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess$1.processEntityHierarchies(MetadataBuildingProcess.java:281) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:324) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1431) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1502) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:66) ~[spring-orm-6.2.2.jar:6.2.2]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390) ~[spring-orm-6.2.2.jar:6.2.2]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:419) ~[spring-orm-6.2.2.jar:6.2.2]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:400) ~[spring-orm-6.2.2.jar:6.2.2]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366) ~[spring-orm-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-6.2.2.jar:6.2.2]
	... 15 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.digicore.lucid.customer.data.modules.branch.converter.BranchContactPersonConverter
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[na:na]
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	at java.base/java.lang.Class.forName0(Native Method) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:534) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:513) ~[na:na]
	at java.base/sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:114) ~[na:na]
	at java.base/sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:125) ~[na:na]
	at java.base/sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationParser.parseSig(AnnotationParser.java:442) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationParser.parseClassValue(AnnotationParser.java:428) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:347) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:282) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:121) ~[na:na]
	at java.base/sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:73) ~[na:na]
	at java.base/java.lang.reflect.Field.declaredAnnotations(Field.java:1281) ~[na:na]
	at java.base/java.lang.reflect.Field.declaredAnnotations(Field.java:1279) ~[na:na]
	at java.base/java.lang.reflect.Field.getAnnotation(Field.java:1246) ~[na:na]
	at java.base/java.lang.reflect.AnnotatedElement.isAnnotationPresent(AnnotatedElement.java:292) ~[na:na]
	at java.base/java.lang.reflect.AccessibleObject.isAnnotationPresent(AccessibleObject.java:558) ~[na:na]
	at org.hibernate.annotations.common.reflection.java.JavaAnnotationReader.isAnnotationPresent(JavaAnnotationReader.java:30) ~[hibernate-commons-annotations-7.0.3.Final.jar:7.0.3.Final]
	at org.hibernate.annotations.common.reflection.java.JavaXAnnotatedElement.isAnnotationPresent(JavaXAnnotatedElement.java:40) ~[hibernate-commons-annotations-7.0.3.Final.jar:7.0.3.Final]
	at org.hibernate.annotations.common.reflection.java.JavaXMember.isAnnotationPresent(JavaXMember.java:21) ~[hibernate-commons-annotations-7.0.3.Final.jar:7.0.3.Final]
	at org.hibernate.boot.model.internal.InheritanceState.determineDefaultAccessType(InheritanceState.java:276) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.InheritanceState.getElementsToProcess(InheritanceState.java:226) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.InheritanceState.postProcess(InheritanceState.java:162) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.boot.model.internal.EntityBinder.handleIdentifier(EntityBinder.java:410) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	... 29 common frames omitted

2025-07-07T15:46:51.856+01:00  INFO 1309 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 1309 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T15:46:51.857+01:00  INFO 1309 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T15:46:51.885+01:00  INFO 1309 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:46:51.885+01:00  INFO 1309 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T15:46:51.885+01:00  INFO 1309 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:46:51.885+01:00  INFO 1309 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T15:46:52.656+01:00  INFO 1309 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:46:52.815+01:00  INFO 1309 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 152 ms. Found 19 JPA repository interfaces.
2025-07-07T15:46:52.819+01:00  INFO 1309 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:46:52.829+01:00  INFO 1309 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 4 JPA repository interfaces.
2025-07-07T15:46:52.829+01:00  INFO 1309 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:46:52.833+01:00  INFO 1309 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:46:52.833+01:00  INFO 1309 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:46:52.836+01:00  INFO 1309 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:46:52.868+01:00  INFO 1309 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:46:52.872+01:00  INFO 1309 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-07T15:46:52.873+01:00  INFO 1309 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:46:52.878+01:00  INFO 1309 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-07-07T15:46:53.557+01:00  INFO 1309 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5dc5dd8c-b3fc-3d05-a8bd-9e8185a56d2b
2025-07-07T15:46:53.811+01:00  WARN 1309 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:46:53.814+01:00  WARN 1309 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:46:53.816+01:00  WARN 1309 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000030169e360] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:46:53.818+01:00  WARN 1309 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:46:53.824+01:00  WARN 1309 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:46:53.827+01:00  WARN 1309 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:46:53.829+01:00  WARN 1309 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T15:46:54.074+01:00  INFO 1309 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T15:46:54.087+01:00  INFO 1309 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T15:46:54.087+01:00  INFO 1309 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T15:46:54.137+01:00  INFO 1309 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T15:46:54.137+01:00  INFO 1309 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2250 ms
2025-07-07T15:46:54.480+01:00  INFO 1309 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T15:46:54.531+01:00  INFO 1309 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T15:46:54.556+01:00  INFO 1309 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T15:46:54.741+01:00  INFO 1309 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T15:46:54.777+01:00  INFO 1309 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T15:46:54.907+01:00  INFO 1309 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@21d30ba5
2025-07-07T15:46:54.908+01:00  INFO 1309 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T15:46:54.983+01:00  INFO 1309 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T15:46:56.079+01:00  INFO 1309 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T15:46:56.216+01:00  INFO 1309 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:46:56.514+01:00  INFO 1309 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T15:46:57.660+01:00  INFO 1309 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T15:46:57.940+01:00  INFO 1309 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T15:46:57.983+01:00  WARN 1309 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T15:46:58.093+01:00  INFO 1309 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:46:58.143+01:00  INFO 1309 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:46:59.478+01:00  WARN 1309 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T15:46:59.624+01:00  INFO 1309 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:46:59.877+01:00  INFO 1309 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:46:59.973+01:00  WARN 1309 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T15:46:59.989+01:00  WARN 1309 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T15:47:07.236+01:00  INFO 1309 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:47:07.850+01:00  WARN 1309 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T15:47:08.433+01:00  INFO 1309 --- [lucid-customer] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T15:47:09.066+01:00  WARN 1309 --- [lucid-customer] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T15:47:09.278+01:00  INFO 1309 --- [lucid-customer] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T15:47:09.448+01:00  INFO 1309 --- [lucid-customer] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T15:47:09.477+01:00  INFO 1309 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T15:47:09.481+01:00  INFO 1309 --- [lucid-customer] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T15:47:09.493+01:00  INFO 1309 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T15:47:09.493+01:00  INFO 1309 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T15:47:09.493+01:00  INFO 1309 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T15:47:09.494+01:00  INFO 1309 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T15:47:09.494+01:00  INFO 1309 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T15:47:09.494+01:00  INFO 1309 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T15:47:09.494+01:00  INFO 1309 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T15:47:09.847+01:00  INFO 1309 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T15:47:09.848+01:00  INFO 1309 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T15:47:09.850+01:00  INFO 1309 --- [lucid-customer] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T15:47:09.852+01:00  INFO 1309 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751899629851 with initial instances count: 0
2025-07-07T15:47:09.859+01:00  INFO 1309 --- [lucid-customer] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CUSTOMER with eureka with status UP
2025-07-07T15:47:09.859+01:00  INFO 1309 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751899629859, current=UP, previous=STARTING]
2025-07-07T15:47:09.860+01:00  INFO 1309 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T15:47:09.872+01:00  INFO 1309 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8075 (http) with context path '/'
2025-07-07T15:47:09.873+01:00  INFO 1309 --- [lucid-customer] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8075
2025-07-07T15:47:09.933+01:00  INFO 1309 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T15:47:10.031+01:00  INFO 1309 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Started LucidCustomerServiceApplication in 18.791 seconds (process running for 25.913)
2025-07-07T15:47:39.858+01:00  INFO 1309 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T15:47:39.860+01:00  INFO 1309 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T15:47:39.860+01:00  INFO 1309 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T15:47:39.860+01:00  INFO 1309 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T15:47:39.860+01:00  INFO 1309 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T15:47:39.861+01:00  INFO 1309 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application version is -1: false
2025-07-07T15:47:39.861+01:00  INFO 1309 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T15:47:39.928+01:00  INFO 1309 --- [lucid-customer] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T15:48:00.102+01:00  INFO 1309 --- [lucid-customer] [http-nio-8075-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T15:48:00.103+01:00  INFO 1309 --- [lucid-customer] [http-nio-8075-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T15:48:00.146+01:00  INFO 1309 --- [lucid-customer] [http-nio-8075-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 43 ms
2025-07-07T15:48:00.252+01:00  INFO 1309 --- [lucid-customer] [http-nio-8075-exec-1] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T15:48:06.059+01:00  INFO 1309 --- [lucid-customer] [ApprovalExecutor-1] c.d.l.c.l.n.s.NotificationDispatcher     : sending EMAIL notification to rabbitmq :: <{"firstName":"SI-NURENI AGBOOLA ABIOLA SI-NURENI","recipients":["<EMAIL>"],"notificationSubject":"Login successful","notificationRequestType":"SEND_LOGIN_SUCCESS_EMAIL","twoFaIsEnabled":false,"isHtml":false,"channel":"EMAIL","dateTime":"7::Jul::2025 15::48::05","logoLink":"","templateName":"login","bankName":"coronation","platformName":"retail","primaryColor":"#5026fb","subDomain":"coronation","supportMailLink":"","helpUrl":"","device":"Browser: Postman Runtime 0, Device: Unknown, OS: Unknown","expiryTimeInMinutes":0}>
2025-07-07T15:48:06.068+01:00  INFO 1309 --- [lucid-customer] [ApprovalExecutor-1] o.s.a.r.c.CachingConnectionFactory       : Attempting to connect to: [localhost:5672]
2025-07-07T15:48:06.296+01:00  INFO 1309 --- [lucid-customer] [ApprovalExecutor-1] o.s.a.r.c.CachingConnectionFactory       : Created new connection: rabbitConnectionFactory#5a68eeda:0/SimpleConnection@5e03a141 [delegate=amqp://guest@127.0.0.1:5672/, localPort=52981]
2025-07-07T15:48:06.362+01:00  INFO 1309 --- [lucid-customer] [ApprovalExecutor-1] o.s.amqp.rabbit.core.RabbitAdmin         : Auto-declaring a non-durable, auto-delete, or exclusive Queue (lucidNotification) durable:false, auto-delete:false, exclusive:false. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-07T15:50:50.439+01:00  INFO 1309 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CUSTOMER with eureka with status DOWN
2025-07-07T15:50:50.441+01:00  INFO 1309 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751899850441, current=DOWN, previous=UP]
2025-07-07T15:50:50.441+01:00  INFO 1309 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T15:50:50.471+01:00  INFO 1309 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T15:50:50.487+01:00  INFO 1309 --- [lucid-customer] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T15:50:50.495+01:00  INFO 1309 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T15:50:50.579+01:00  INFO 1309 --- [lucid-customer] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:50:50.593+01:00  INFO 1309 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T15:50:50.607+01:00  INFO 1309 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T15:50:50.609+01:00  INFO 1309 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T15:50:53.616+01:00  INFO 1309 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T15:50:53.639+01:00  INFO 1309 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - deregister  status: 200
2025-07-07T15:50:53.639+01:00  INFO 1309 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T15:51:01.882+01:00  INFO 2221 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 2221 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T15:51:01.884+01:00  INFO 2221 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T15:51:01.908+01:00  INFO 2221 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:51:01.908+01:00  INFO 2221 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T15:51:01.909+01:00  INFO 2221 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:51:01.909+01:00  INFO 2221 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T15:51:02.735+01:00  INFO 2221 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:51:02.887+01:00  INFO 2221 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 145 ms. Found 19 JPA repository interfaces.
2025-07-07T15:51:02.891+01:00  INFO 2221 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:51:02.903+01:00  INFO 2221 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T15:51:02.903+01:00  INFO 2221 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:51:02.906+01:00  INFO 2221 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:51:02.907+01:00  INFO 2221 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:51:02.909+01:00  INFO 2221 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T15:51:02.939+01:00  INFO 2221 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:51:02.943+01:00  INFO 2221 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-07-07T15:51:02.944+01:00  INFO 2221 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T15:51:02.948+01:00  INFO 2221 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-07-07T15:51:03.632+01:00  INFO 2221 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=23bc0fb5-637d-308d-8a9f-28ae5f7dca9b
2025-07-07T15:51:03.873+01:00  WARN 2221 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:51:03.876+01:00  WARN 2221 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:51:03.877+01:00  WARN 2221 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000030169e760] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:51:03.879+01:00  WARN 2221 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:51:03.885+01:00  WARN 2221 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:51:03.888+01:00  WARN 2221 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T15:51:03.890+01:00  WARN 2221 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T15:51:04.098+01:00  INFO 2221 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T15:51:04.110+01:00  INFO 2221 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T15:51:04.111+01:00  INFO 2221 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T15:51:04.169+01:00  INFO 2221 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T15:51:04.169+01:00  INFO 2221 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2259 ms
2025-07-07T15:51:04.482+01:00  INFO 2221 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T15:51:04.526+01:00  INFO 2221 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T15:51:04.548+01:00  INFO 2221 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T15:51:04.715+01:00  INFO 2221 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T15:51:04.748+01:00  INFO 2221 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T15:51:04.875+01:00  INFO 2221 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2f32fe68
2025-07-07T15:51:04.876+01:00  INFO 2221 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T15:51:04.949+01:00  INFO 2221 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T15:51:05.905+01:00  INFO 2221 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T15:51:06.033+01:00  INFO 2221 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T15:51:06.289+01:00  INFO 2221 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T15:51:07.390+01:00  INFO 2221 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T15:51:07.640+01:00  INFO 2221 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T15:51:07.680+01:00  WARN 2221 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T15:51:07.775+01:00  INFO 2221 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:51:07.836+01:00  INFO 2221 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:51:09.093+01:00  WARN 2221 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T15:51:09.230+01:00  INFO 2221 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:51:09.475+01:00  INFO 2221 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:51:09.566+01:00  WARN 2221 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T15:51:09.580+01:00  WARN 2221 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T15:51:15.727+01:00  INFO 2221 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T15:51:16.425+01:00  WARN 2221 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T15:51:16.982+01:00  INFO 2221 --- [lucid-customer] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T15:51:17.610+01:00  WARN 2221 --- [lucid-customer] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T15:51:17.826+01:00  INFO 2221 --- [lucid-customer] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T15:51:18.001+01:00  INFO 2221 --- [lucid-customer] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T15:51:18.040+01:00  INFO 2221 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T15:51:18.045+01:00  INFO 2221 --- [lucid-customer] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T15:51:18.057+01:00  INFO 2221 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T15:51:18.057+01:00  INFO 2221 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T15:51:18.057+01:00  INFO 2221 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T15:51:18.057+01:00  INFO 2221 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T15:51:18.057+01:00  INFO 2221 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T15:51:18.058+01:00  INFO 2221 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T15:51:18.058+01:00  INFO 2221 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T15:51:18.303+01:00  INFO 2221 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T15:51:18.306+01:00  INFO 2221 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T15:51:18.308+01:00  INFO 2221 --- [lucid-customer] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T15:51:18.310+01:00  INFO 2221 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751899878309 with initial instances count: 1
2025-07-07T15:51:18.318+01:00  INFO 2221 --- [lucid-customer] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CUSTOMER with eureka with status UP
2025-07-07T15:51:18.319+01:00  INFO 2221 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751899878319, current=UP, previous=STARTING]
2025-07-07T15:51:18.320+01:00  INFO 2221 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T15:51:18.336+01:00  INFO 2221 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8075 (http) with context path '/'
2025-07-07T15:51:18.337+01:00  INFO 2221 --- [lucid-customer] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8075
2025-07-07T15:51:18.372+01:00  INFO 2221 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T15:51:18.492+01:00  INFO 2221 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Started LucidCustomerServiceApplication in 17.211 seconds (process running for 23.744)
2025-07-07T15:53:06.059+01:00  INFO 2221 --- [lucid-customer] [http-nio-8075-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T15:53:06.060+01:00  INFO 2221 --- [lucid-customer] [http-nio-8075-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T15:53:06.080+01:00  INFO 2221 --- [lucid-customer] [http-nio-8075-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 19 ms
2025-07-07T15:56:18.068+01:00  INFO 2221 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:01:18.092+01:00  INFO 2221 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:01:35.777+01:00  INFO 2221 --- [lucid-customer] [http-nio-8075-exec-3] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T16:01:37.065+01:00 ERROR 2221 --- [lucid-customer] [http-nio-8075-exec-3] a.s.c.CoronationCustomerUserLoginService : Phishing Image Not Found <NAME_EMAIL>
2025-07-07T16:01:37.118+01:00 ERROR 2221 --- [lucid-customer] [http-nio-8075-exec-3] c.d.r.e.DefaultExceptionHandler          :  A custom exception was thrown, with cause of error to be : Invalid login credentials
2025-07-07T16:01:55.205+01:00  INFO 2221 --- [lucid-customer] [http-nio-8075-exec-4] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T16:02:24.943+01:00 ERROR 2221 --- [lucid-customer] [http-nio-8075-exec-4] a.s.c.CoronationCustomerUserLoginService : Phishing Image Not Found <NAME_EMAIL>
2025-07-07T16:02:25.087+01:00 ERROR 2221 --- [lucid-customer] [http-nio-8075-exec-4] c.d.r.e.DefaultExceptionHandler          :  A custom exception was thrown, with cause of error to be : N/A
2025-07-07T16:03:46.791+01:00  INFO 2221 --- [lucid-customer] [http-nio-8075-exec-5] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T16:04:15.658+01:00 ERROR 2221 --- [lucid-customer] [http-nio-8075-exec-5] a.s.c.CoronationCustomerUserLoginService : Phishing Image Mismatch <NAME_EMAIL>
2025-07-07T16:04:15.793+01:00 ERROR 2221 --- [lucid-customer] [http-nio-8075-exec-5] c.d.r.e.DefaultExceptionHandler          :  A custom exception was thrown, with cause of error to be : N/A
2025-07-07T16:04:24.620+01:00  INFO 2221 --- [lucid-customer] [http-nio-8075-exec-6] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T16:04:58.061+01:00 ERROR 2221 --- [lucid-customer] [http-nio-8075-exec-6] a.s.c.CoronationCustomerUserLoginService : Phishing Image Mismatch <NAME_EMAIL>
2025-07-07T16:04:56.301+01:00  WARN 2221 --- [lucid-customer] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.ProxyLeakTask     : Connection leak detection triggered for com.mysql.cj.jdbc.ConnectionImpl@2f32fe68 on thread http-nio-8075-exec-6, stack trace follows

java.lang.Exception: Apparent connection leak detected
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:127) ~[HikariCP-5.1.0.jar:na]
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:126) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.internal.NonContextualJdbcConnectionAccess.obtainConnection(NonContextualJdbcConnectionAccess.java:46) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.acquireConnectionIfNeeded(LogicalConnectionManagedImpl.java:126) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.getPhysicalConnection(LogicalConnectionManagedImpl.java:156) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.connection(StatementPreparerImpl.java:56) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.query.spi.AbstractSelectionQuery.getSingleResult(AbstractSelectionQuery.java:275) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$SingleEntityExecution.doExecute(JpaQueryExecution.java:224) ~[spring-data-jpa-3.4.2.jar:3.4.2]
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:93) ~[spring-data-jpa-3.4.2.jar:3.4.2]
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:152) ~[spring-data-jpa-3.4.2.jar:3.4.2]
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:140) ~[spring-data-jpa-3.4.2.jar:3.4.2]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170) ~[spring-data-commons-3.4.2.jar:3.4.2]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158) ~[spring-data-commons-3.4.2.jar:3.4.2]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:170) ~[spring-data-commons-3.4.2.jar:3.4.2]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149) ~[spring-data-commons-3.4.2.jar:3.4.2]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.2.jar:6.2.2]
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69) ~[spring-data-commons-3.4.2.jar:3.4.2]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.2.jar:6.2.2]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380) ~[spring-tx-6.2.2.jar:6.2.2]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-6.2.2.jar:6.2.2]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.2.jar:6.2.2]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138) ~[spring-tx-6.2.2.jar:6.2.2]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.2.jar:6.2.2]
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:136) ~[spring-data-jpa-3.4.2.jar:3.4.2]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.2.jar:6.2.2]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223) ~[spring-aop-6.2.2.jar:6.2.2]
	at jdk.proxy2/jdk.proxy2.$Proxy229.retrieveOrganizationIdAndBankOrganizationId(Unknown Source) ~[na:na]
	at com.digicore.lucid.customer.data.modules.profile.service.CustomerProfileService.setOrganizationIdAndCbaTokenAndProvider(CustomerProfileService.java:58) ~[classes/:na]
	at com.digicore.lucid.customer.service.modules.authentication.controller.coronation.CoronationCustomerUserAuthenticationController.setRequestContext(CoronationCustomerUserAuthenticationController.java:233) ~[classes/:na]
	at com.digicore.lucid.customer.service.modules.authentication.controller.coronation.CoronationCustomerUserAuthenticationController.login(CoronationCustomerUserAuthenticationController.java:73) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:257) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590) ~[tomcat-embed-core-10.1.34.jar:6.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658) ~[tomcat-embed-core-10.1.34.jar:6.0]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101) ~[spring-web-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.oauth2.server.resource.web.authentication.BearerTokenAuthenticationFilter.doFilterInternal(BearerTokenAuthenticationFilter.java:128) ~[spring-security-oauth2-resource-server-6.4.2.jar:6.4.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238) ~[spring-security-config-6.4.2.jar:6.4.2]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278) ~[spring-web-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T16:04:58.121+01:00 ERROR 2221 --- [lucid-customer] [http-nio-8075-exec-6] c.d.r.e.DefaultExceptionHandler          :  A custom exception was thrown, with cause of error to be : N/A
2025-07-07T16:04:58.125+01:00  INFO 2221 --- [lucid-customer] [http-nio-8075-exec-6] com.zaxxer.hikari.pool.ProxyLeakTask     : Previously reported leaked connection com.mysql.cj.jdbc.ConnectionImpl@2f32fe68 on thread http-nio-8075-exec-6 was returned to the pool (unleaked)
2025-07-07T16:06:18.108+01:00  INFO 2221 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:08:02.919+01:00  INFO 2221 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CUSTOMER with eureka with status DOWN
2025-07-07T16:08:02.926+01:00  INFO 2221 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751900882925, current=DOWN, previous=UP]
2025-07-07T16:08:02.931+01:00  INFO 2221 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T16:08:03.005+01:00  INFO 2221 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T16:08:03.013+01:00  INFO 2221 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T16:08:03.018+01:00  INFO 2221 --- [lucid-customer] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T16:08:03.158+01:00  INFO 2221 --- [lucid-customer] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T16:08:03.165+01:00  INFO 2221 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T16:08:03.170+01:00  INFO 2221 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T16:08:03.172+01:00  INFO 2221 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T16:08:06.179+01:00  INFO 2221 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T16:08:06.246+01:00  INFO 2221 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - deregister  status: 200
2025-07-07T16:08:06.247+01:00  INFO 2221 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T16:08:16.014+01:00  INFO 5803 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 5803 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T16:08:16.016+01:00  INFO 5803 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T16:08:16.041+01:00  INFO 5803 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T16:08:16.041+01:00  INFO 5803 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T16:08:16.041+01:00  INFO 5803 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T16:08:16.041+01:00  INFO 5803 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T16:08:16.863+01:00  INFO 5803 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:08:17.017+01:00  INFO 5803 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 148 ms. Found 19 JPA repository interfaces.
2025-07-07T16:08:17.021+01:00  INFO 5803 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:08:17.033+01:00  INFO 5803 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T16:08:17.033+01:00  INFO 5803 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:08:17.036+01:00  INFO 5803 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T16:08:17.036+01:00  INFO 5803 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:08:17.039+01:00  INFO 5803 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-07T16:08:17.076+01:00  INFO 5803 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:08:17.081+01:00  INFO 5803 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-07T16:08:17.082+01:00  INFO 5803 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:08:17.087+01:00  INFO 5803 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-07-07T16:08:17.699+01:00  INFO 5803 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=23bc0fb5-637d-308d-8a9f-28ae5f7dca9b
2025-07-07T16:08:17.926+01:00  WARN 5803 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:08:17.930+01:00  WARN 5803 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:08:17.931+01:00  WARN 5803 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000080169df70] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:08:17.933+01:00  WARN 5803 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:08:17.938+01:00  WARN 5803 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:08:17.941+01:00  WARN 5803 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:08:17.943+01:00  WARN 5803 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T16:08:18.156+01:00  INFO 5803 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T16:08:18.167+01:00  INFO 5803 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T16:08:18.167+01:00  INFO 5803 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T16:08:18.211+01:00  INFO 5803 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T16:08:18.212+01:00  INFO 5803 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2168 ms
2025-07-07T16:08:18.530+01:00  INFO 5803 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T16:08:18.574+01:00  INFO 5803 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T16:08:18.597+01:00  INFO 5803 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T16:08:18.769+01:00  INFO 5803 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T16:08:18.806+01:00  INFO 5803 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T16:08:18.927+01:00  INFO 5803 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1b68e626
2025-07-07T16:08:18.929+01:00  INFO 5803 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T16:08:19.000+01:00  INFO 5803 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T16:08:19.948+01:00  INFO 5803 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T16:08:20.082+01:00  INFO 5803 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T16:08:20.338+01:00  INFO 5803 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T16:08:21.431+01:00  INFO 5803 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T16:08:21.692+01:00  INFO 5803 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T16:08:21.734+01:00  WARN 5803 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T16:08:21.829+01:00  INFO 5803 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T16:08:21.881+01:00  INFO 5803 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T16:08:23.114+01:00  WARN 5803 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T16:08:23.225+01:00  INFO 5803 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:08:23.475+01:00  INFO 5803 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:08:23.564+01:00  WARN 5803 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T16:08:23.579+01:00  WARN 5803 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T16:08:29.936+01:00  INFO 5803 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:08:30.514+01:00  WARN 5803 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T16:08:31.027+01:00  INFO 5803 --- [lucid-customer] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T16:08:31.609+01:00  WARN 5803 --- [lucid-customer] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T16:08:31.820+01:00  INFO 5803 --- [lucid-customer] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T16:08:31.989+01:00  INFO 5803 --- [lucid-customer] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T16:08:32.025+01:00  INFO 5803 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T16:08:32.033+01:00  INFO 5803 --- [lucid-customer] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:08:32.046+01:00  INFO 5803 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T16:08:32.046+01:00  INFO 5803 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T16:08:32.046+01:00  INFO 5803 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T16:08:32.046+01:00  INFO 5803 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T16:08:32.046+01:00  INFO 5803 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T16:08:32.046+01:00  INFO 5803 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T16:08:32.046+01:00  INFO 5803 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T16:08:32.303+01:00  INFO 5803 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T16:08:32.305+01:00  INFO 5803 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T16:08:32.307+01:00  INFO 5803 --- [lucid-customer] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T16:08:32.309+01:00  INFO 5803 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751900912309 with initial instances count: 1
2025-07-07T16:08:32.317+01:00  INFO 5803 --- [lucid-customer] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CUSTOMER with eureka with status UP
2025-07-07T16:08:32.318+01:00  INFO 5803 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751900912318, current=UP, previous=STARTING]
2025-07-07T16:08:32.320+01:00  INFO 5803 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T16:08:32.335+01:00  INFO 5803 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8075 (http) with context path '/'
2025-07-07T16:08:32.336+01:00  INFO 5803 --- [lucid-customer] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8075
2025-07-07T16:08:32.358+01:00  INFO 5803 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T16:08:32.482+01:00  INFO 5803 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Started LucidCustomerServiceApplication in 17.253 seconds (process running for 23.846)
2025-07-07T16:10:45.179+01:00  INFO 5803 --- [lucid-customer] [http-nio-8075-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T16:10:45.179+01:00  INFO 5803 --- [lucid-customer] [http-nio-8075-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T16:10:45.187+01:00  INFO 5803 --- [lucid-customer] [http-nio-8075-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 7 ms
2025-07-07T16:10:45.288+01:00  INFO 5803 --- [lucid-customer] [http-nio-8075-exec-1] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T16:11:16.035+01:00  WARN 5803 --- [lucid-customer] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.ProxyLeakTask     : Connection leak detection triggered for com.mysql.cj.jdbc.ConnectionImpl@1b68e626 on thread http-nio-8075-exec-1, stack trace follows

java.lang.Exception: Apparent connection leak detected
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:127) ~[HikariCP-5.1.0.jar:na]
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:126) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.internal.NonContextualJdbcConnectionAccess.obtainConnection(NonContextualJdbcConnectionAccess.java:46) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.acquireConnectionIfNeeded(LogicalConnectionManagedImpl.java:126) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.getPhysicalConnection(LogicalConnectionManagedImpl.java:156) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.connection(StatementPreparerImpl.java:56) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.hibernate.query.spi.AbstractSelectionQuery.getSingleResult(AbstractSelectionQuery.java:275) ~[hibernate-core-6.6.5.Final.jar:6.6.5.Final]
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$SingleEntityExecution.doExecute(JpaQueryExecution.java:224) ~[spring-data-jpa-3.4.2.jar:3.4.2]
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:93) ~[spring-data-jpa-3.4.2.jar:3.4.2]
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:152) ~[spring-data-jpa-3.4.2.jar:3.4.2]
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:140) ~[spring-data-jpa-3.4.2.jar:3.4.2]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170) ~[spring-data-commons-3.4.2.jar:3.4.2]
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158) ~[spring-data-commons-3.4.2.jar:3.4.2]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:170) ~[spring-data-commons-3.4.2.jar:3.4.2]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149) ~[spring-data-commons-3.4.2.jar:3.4.2]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.2.jar:6.2.2]
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69) ~[spring-data-commons-3.4.2.jar:3.4.2]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.2.jar:6.2.2]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380) ~[spring-tx-6.2.2.jar:6.2.2]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-6.2.2.jar:6.2.2]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.2.jar:6.2.2]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138) ~[spring-tx-6.2.2.jar:6.2.2]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.2.jar:6.2.2]
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:136) ~[spring-data-jpa-3.4.2.jar:3.4.2]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.2.jar:6.2.2]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223) ~[spring-aop-6.2.2.jar:6.2.2]
	at jdk.proxy2/jdk.proxy2.$Proxy229.retrieveOrganizationIdAndBankOrganizationId(Unknown Source) ~[na:na]
	at com.digicore.lucid.customer.data.modules.profile.service.CustomerProfileService.setOrganizationIdAndCbaTokenAndProvider(CustomerProfileService.java:58) ~[classes/:na]
	at com.digicore.lucid.customer.service.modules.authentication.controller.coronation.CoronationCustomerUserAuthenticationController.setRequestContext(CoronationCustomerUserAuthenticationController.java:233) ~[classes/:na]
	at com.digicore.lucid.customer.service.modules.authentication.controller.coronation.CoronationCustomerUserAuthenticationController.login(CoronationCustomerUserAuthenticationController.java:73) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:257) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590) ~[tomcat-embed-core-10.1.34.jar:6.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658) ~[tomcat-embed-core-10.1.34.jar:6.0]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101) ~[spring-web-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.oauth2.server.resource.web.authentication.BearerTokenAuthenticationFilter.doFilterInternal(BearerTokenAuthenticationFilter.java:128) ~[spring-security-oauth2-resource-server-6.4.2.jar:6.4.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191) ~[spring-security-web-6.4.2.jar:6.4.2]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238) ~[spring-security-config-6.4.2.jar:6.4.2]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278) ~[spring-web-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.2.jar:6.2.2]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-10.1.34.jar:10.1.34]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T16:11:36.713+01:00  INFO 5803 --- [lucid-customer] [ApprovalExecutor-1] c.d.l.c.l.n.s.NotificationDispatcher     : sending EMAIL notification to rabbitmq :: <{"firstName":"SI-NURENI AGBOOLA ABIOLA SI-NURENI","recipients":["<EMAIL>"],"notificationSubject":"Login successful","notificationRequestType":"SEND_LOGIN_SUCCESS_EMAIL","twoFaIsEnabled":false,"isHtml":false,"channel":"EMAIL","dateTime":"7::Jul::2025 16::11::36","logoLink":"","templateName":"login","bankName":"coronation","platformName":"retail","primaryColor":"#5026fb","subDomain":"coronation","supportMailLink":"","helpUrl":"","device":"Browser: Postman Runtime 0, Device: Unknown, OS: Unknown","expiryTimeInMinutes":0}>
2025-07-07T16:11:36.722+01:00  INFO 5803 --- [lucid-customer] [ApprovalExecutor-1] o.s.a.r.c.CachingConnectionFactory       : Attempting to connect to: [localhost:5672]
2025-07-07T16:11:36.750+01:00  INFO 5803 --- [lucid-customer] [http-nio-8075-exec-1] com.zaxxer.hikari.pool.ProxyLeakTask     : Previously reported leaked connection com.mysql.cj.jdbc.ConnectionImpl@1b68e626 on thread http-nio-8075-exec-1 was returned to the pool (unleaked)
2025-07-07T16:11:36.839+01:00  INFO 5803 --- [lucid-customer] [ApprovalExecutor-1] o.s.a.r.c.CachingConnectionFactory       : Created new connection: rabbitConnectionFactory#1ccffb23:0/SimpleConnection@6875acda [delegate=amqp://guest@127.0.0.1:5672/, localPort=53563]
2025-07-07T16:11:36.852+01:00  INFO 5803 --- [lucid-customer] [ApprovalExecutor-1] o.s.amqp.rabbit.core.RabbitAdmin         : Auto-declaring a non-durable, auto-delete, or exclusive Queue (lucidNotification) durable:false, auto-delete:false, exclusive:false. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-07T16:13:32.054+01:00  INFO 5803 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:16:21.325+01:00  INFO 5803 --- [lucid-customer] [http-nio-8075-exec-2] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T16:16:26.704+01:00  INFO 5803 --- [lucid-customer] [ApprovalExecutor-2] c.d.l.c.l.n.s.NotificationDispatcher     : sending EMAIL notification to rabbitmq :: <{"firstName":"SI-NURENI AGBOOLA ABIOLA SI-NURENI","recipients":["<EMAIL>"],"notificationSubject":"Login successful","notificationRequestType":"SEND_LOGIN_SUCCESS_EMAIL","twoFaIsEnabled":false,"isHtml":false,"channel":"EMAIL","dateTime":"7::Jul::2025 16::16::26","logoLink":"","templateName":"login","bankName":"coronation","platformName":"retail","primaryColor":"#5026fb","subDomain":"coronation","supportMailLink":"","helpUrl":"","device":"Browser: Postman Runtime 0, Device: Unknown, OS: Unknown","expiryTimeInMinutes":0}>
2025-07-07T16:16:36.414+01:00  INFO 5803 --- [lucid-customer] [http-nio-8075-exec-3] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T16:16:36.926+01:00  INFO 5803 --- [lucid-customer] [ApprovalExecutor-3] c.d.l.c.l.n.s.NotificationDispatcher     : sending EMAIL notification to rabbitmq :: <{"firstName":"SI-NURENI AGBOOLA ABIOLA SI-NURENI","recipients":["<EMAIL>"],"notificationSubject":"Login successful","notificationRequestType":"SEND_LOGIN_SUCCESS_EMAIL","twoFaIsEnabled":false,"isHtml":false,"channel":"EMAIL","dateTime":"7::Jul::2025 16::16::36","logoLink":"","templateName":"login","bankName":"coronation","platformName":"retail","primaryColor":"#5026fb","subDomain":"coronation","supportMailLink":"","helpUrl":"","device":"Browser: Postman Runtime 0, Device: Unknown, OS: Unknown","expiryTimeInMinutes":0}>
2025-07-07T16:16:42.633+01:00  INFO 5803 --- [lucid-customer] [http-nio-8075-exec-4] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T16:16:42.864+01:00 ERROR 5803 --- [lucid-customer] [http-nio-8075-exec-4] a.s.c.CoronationCustomerUserLoginService : Phishing Image Mismatch <NAME_EMAIL>
2025-07-07T16:16:42.876+01:00 ERROR 5803 --- [lucid-customer] [http-nio-8075-exec-4] c.d.r.e.DefaultExceptionHandler          :  A custom exception was thrown, with cause of error to be : Invalid login credentials
2025-07-07T16:16:59.362+01:00  INFO 5803 --- [lucid-customer] [http-nio-8075-exec-5] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T16:16:59.623+01:00  INFO 5803 --- [lucid-customer] [ApprovalExecutor-4] c.d.l.c.l.n.s.NotificationDispatcher     : sending EMAIL notification to rabbitmq :: <{"firstName":"SI-NURENI AGBOOLA ABIOLA SI-NURENI","recipients":["<EMAIL>"],"notificationSubject":"Login successful","notificationRequestType":"SEND_LOGIN_SUCCESS_EMAIL","twoFaIsEnabled":false,"isHtml":false,"channel":"EMAIL","dateTime":"7::Jul::2025 16::16::59","logoLink":"","templateName":"login","bankName":"coronation","platformName":"retail","primaryColor":"#5026fb","subDomain":"coronation","supportMailLink":"","helpUrl":"","device":"Browser: Postman Runtime 0, Device: Unknown, OS: Unknown","expiryTimeInMinutes":0}>
2025-07-07T16:17:17.617+01:00  INFO 5803 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CUSTOMER with eureka with status DOWN
2025-07-07T16:17:17.618+01:00  INFO 5803 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=*************, current=DOWN, previous=UP]
2025-07-07T16:17:17.619+01:00  INFO 5803 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T16:17:17.626+01:00  INFO 5803 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T16:17:17.629+01:00  INFO 5803 --- [lucid-customer] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T16:17:17.629+01:00  INFO 5803 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T16:17:18.784+01:00  INFO 5803 --- [lucid-customer] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T16:17:18.803+01:00  INFO 5803 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T16:17:18.817+01:00  INFO 5803 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T16:17:18.819+01:00  INFO 5803 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T16:17:21.824+01:00  INFO 5803 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T16:17:21.845+01:00  INFO 5803 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - deregister  status: 200
2025-07-07T16:17:21.845+01:00  INFO 5803 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T16:17:31.185+01:00  INFO 7673 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 7673 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T16:17:31.186+01:00  INFO 7673 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T16:17:31.212+01:00  INFO 7673 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T16:17:31.212+01:00  INFO 7673 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T16:17:31.212+01:00  INFO 7673 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T16:17:31.212+01:00  INFO 7673 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T16:17:32.037+01:00  INFO 7673 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:17:32.200+01:00  INFO 7673 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 153 ms. Found 19 JPA repository interfaces.
2025-07-07T16:17:32.204+01:00  INFO 7673 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:17:32.216+01:00  INFO 7673 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T16:17:32.216+01:00  INFO 7673 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:17:32.220+01:00  INFO 7673 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-07-07T16:17:32.220+01:00  INFO 7673 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:17:32.223+01:00  INFO 7673 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T16:17:32.255+01:00  INFO 7673 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:17:32.261+01:00  INFO 7673 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-07-07T16:17:32.262+01:00  INFO 7673 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:17:32.267+01:00  INFO 7673 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-07-07T16:17:32.896+01:00  INFO 7673 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=23bc0fb5-637d-308d-8a9f-28ae5f7dca9b
2025-07-07T16:17:33.213+01:00  WARN 7673 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:17:33.227+01:00  WARN 7673 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:17:33.229+01:00  WARN 7673 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000700169b5c8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:17:33.232+01:00  WARN 7673 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:17:33.239+01:00  WARN 7673 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:17:33.242+01:00  WARN 7673 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:17:33.244+01:00  WARN 7673 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T16:17:33.514+01:00  INFO 7673 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T16:17:33.544+01:00  INFO 7673 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T16:17:33.545+01:00  INFO 7673 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T16:17:33.597+01:00  INFO 7673 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T16:17:33.598+01:00  INFO 7673 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2384 ms
2025-07-07T16:17:33.917+01:00  INFO 7673 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T16:17:33.961+01:00  INFO 7673 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T16:17:33.984+01:00  INFO 7673 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T16:17:34.146+01:00  INFO 7673 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T16:17:34.179+01:00  INFO 7673 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T16:17:34.283+01:00  INFO 7673 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3a2b1f24
2025-07-07T16:17:34.284+01:00  INFO 7673 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T16:17:34.351+01:00  INFO 7673 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T16:17:35.353+01:00  INFO 7673 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T16:17:35.505+01:00  INFO 7673 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T16:17:35.768+01:00  INFO 7673 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T16:17:36.840+01:00  INFO 7673 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T16:17:37.087+01:00  INFO 7673 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T16:17:37.126+01:00  WARN 7673 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T16:17:37.234+01:00  INFO 7673 --- [lucid-customer] [redisson-netty-1-6] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T16:17:37.307+01:00  INFO 7673 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T16:17:38.587+01:00  WARN 7673 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T16:17:38.694+01:00  INFO 7673 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:17:38.938+01:00  INFO 7673 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:17:39.034+01:00  WARN 7673 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T16:17:39.048+01:00  WARN 7673 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T16:17:45.359+01:00  INFO 7673 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:17:45.781+01:00  WARN 7673 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T16:17:46.172+01:00  INFO 7673 --- [lucid-customer] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T16:17:46.658+01:00  WARN 7673 --- [lucid-customer] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T16:17:46.804+01:00  INFO 7673 --- [lucid-customer] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T16:17:46.917+01:00  INFO 7673 --- [lucid-customer] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T16:17:46.934+01:00  INFO 7673 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T16:17:46.936+01:00  INFO 7673 --- [lucid-customer] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:17:46.942+01:00  INFO 7673 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T16:17:46.942+01:00  INFO 7673 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T16:17:46.942+01:00  INFO 7673 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T16:17:46.942+01:00  INFO 7673 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T16:17:46.942+01:00  INFO 7673 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T16:17:46.942+01:00  INFO 7673 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T16:17:46.942+01:00  INFO 7673 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T16:17:47.069+01:00  INFO 7673 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T16:17:47.070+01:00  INFO 7673 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T16:17:47.071+01:00  INFO 7673 --- [lucid-customer] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T16:17:47.072+01:00  INFO 7673 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751901467072 with initial instances count: 1
2025-07-07T16:17:47.078+01:00  INFO 7673 --- [lucid-customer] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CUSTOMER with eureka with status UP
2025-07-07T16:17:47.078+01:00  INFO 7673 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751901467078, current=UP, previous=STARTING]
2025-07-07T16:17:47.079+01:00  INFO 7673 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T16:17:47.087+01:00  INFO 7673 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8075 (http) with context path '/'
2025-07-07T16:17:47.087+01:00  INFO 7673 --- [lucid-customer] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8075
2025-07-07T16:17:47.103+01:00  INFO 7673 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T16:17:47.193+01:00  INFO 7673 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Started LucidCustomerServiceApplication in 16.777 seconds (process running for 23.68)
2025-07-07T16:22:46.949+01:00  INFO 7673 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:26:59.415+01:00  INFO 7673 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CUSTOMER with eureka with status DOWN
2025-07-07T16:26:59.419+01:00  INFO 7673 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751902019419, current=DOWN, previous=UP]
2025-07-07T16:26:59.420+01:00  INFO 7673 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T16:26:59.485+01:00  INFO 7673 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T16:26:59.520+01:00  INFO 7673 --- [lucid-customer] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T16:26:59.520+01:00  INFO 7673 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T16:26:59.647+01:00  INFO 7673 --- [lucid-customer] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T16:26:59.664+01:00  INFO 7673 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T16:26:59.687+01:00  INFO 7673 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T16:26:59.693+01:00  INFO 7673 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T16:27:02.723+01:00  INFO 7673 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T16:27:02.798+01:00  INFO 7673 --- [lucid-customer] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/} exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CUSTOMER/*************:lucid-customer:8075": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CUSTOMER/*************:lucid-customer:8075": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:91)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 37 more

2025-07-07T16:27:02.799+01:00  WARN 7673 --- [lucid-customer] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CUSTOMER/*************:lucid-customer:8075": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T16:27:02.836+01:00  INFO 7673 --- [lucid-customer] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CUSTOMER/*************:lucid-customer:8075": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CUSTOMER/*************:lucid-customer:8075": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 38 more

2025-07-07T16:27:02.836+01:00  WARN 7673 --- [lucid-customer] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CUSTOMER/*************:lucid-customer:8075": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T16:27:02.836+01:00 ERROR 7673 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147) ~[spring-boot-3.4.2.jar:3.4.2]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116) ~[spring-boot-3.4.2.jar:3.4.2]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T16:27:02.848+01:00  INFO 7673 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T16:35:11.026+01:00  INFO 11835 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 11835 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T16:35:11.027+01:00  INFO 11835 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T16:35:11.050+01:00  INFO 11835 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T16:35:11.050+01:00  INFO 11835 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T16:35:11.050+01:00  INFO 11835 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T16:35:11.051+01:00  INFO 11835 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T16:35:11.889+01:00  INFO 11835 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:35:12.049+01:00  INFO 11835 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 154 ms. Found 19 JPA repository interfaces.
2025-07-07T16:35:12.053+01:00  INFO 11835 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:35:12.064+01:00  INFO 11835 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T16:35:12.065+01:00  INFO 11835 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:35:12.067+01:00  INFO 11835 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T16:35:12.067+01:00  INFO 11835 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:35:12.070+01:00  INFO 11835 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-07T16:35:12.097+01:00  INFO 11835 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:35:12.101+01:00  INFO 11835 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T16:35:12.101+01:00  INFO 11835 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:35:12.105+01:00  INFO 11835 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T16:35:12.672+01:00  INFO 11835 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=2491e3bc-2786-3d2a-8f3e-140b5552247a
2025-07-07T16:35:12.877+01:00  WARN 11835 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:35:12.880+01:00  WARN 11835 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:35:12.881+01:00  WARN 11835 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000050169d0d0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:35:12.883+01:00  WARN 11835 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:35:12.889+01:00  WARN 11835 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:35:12.892+01:00  WARN 11835 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:35:12.895+01:00  WARN 11835 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T16:35:13.094+01:00  INFO 11835 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T16:35:13.103+01:00  INFO 11835 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T16:35:13.103+01:00  INFO 11835 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T16:35:13.165+01:00  INFO 11835 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T16:35:13.165+01:00  INFO 11835 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2113 ms
2025-07-07T16:35:13.463+01:00  INFO 11835 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T16:35:13.504+01:00  INFO 11835 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T16:35:13.525+01:00  INFO 11835 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T16:35:13.671+01:00  INFO 11835 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T16:35:13.694+01:00  INFO 11835 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T16:35:13.824+01:00  INFO 11835 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@76ea1cb5
2025-07-07T16:35:13.825+01:00  INFO 11835 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T16:35:13.897+01:00  INFO 11835 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T16:35:14.767+01:00  INFO 11835 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T16:35:14.908+01:00  INFO 11835 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T16:35:15.130+01:00  INFO 11835 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T16:35:16.232+01:00  INFO 11835 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T16:35:16.473+01:00  INFO 11835 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T16:35:16.509+01:00  WARN 11835 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T16:35:16.597+01:00  INFO 11835 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T16:35:16.651+01:00  INFO 11835 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T16:35:17.871+01:00  WARN 11835 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T16:35:17.976+01:00  INFO 11835 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:35:18.223+01:00  INFO 11835 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:35:18.310+01:00  WARN 11835 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T16:35:18.323+01:00  WARN 11835 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T16:35:24.442+01:00  INFO 11835 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:35:24.898+01:00  WARN 11835 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T16:35:25.003+01:00  WARN 11835 --- [lucid-customer] [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration': Injection of autowired dependencies failed
2025-07-07T16:35:25.006+01:00  WARN 11835 --- [lucid-customer] [main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'meterRegistryCloser': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:275) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:267) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:223) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:460) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:114) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:640) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.2.jar:3.4.2]
	at com.digicore.LucidCustomerServiceApplication.main(LucidCustomerServiceApplication.java:27) ~[classes/:na]

2025-07-07T16:35:25.008+01:00  WARN 11835 --- [lucid-customer] [main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'meterRegistryCloser': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:275) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:267) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:223) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:460) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:114) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:640) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.2.jar:3.4.2]
	at com.digicore.LucidCustomerServiceApplication.main(LucidCustomerServiceApplication.java:27) ~[classes/:na]

2025-07-07T16:35:25.009+01:00  WARN 11835 --- [lucid-customer] [main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'meterRegistryCloser': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:275) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:267) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:223) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:460) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1163) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:114) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:640) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.2.jar:3.4.2]
	at com.digicore.LucidCustomerServiceApplication.main(LucidCustomerServiceApplication.java:27) ~[classes/:na]

2025-07-07T16:35:25.036+01:00  INFO 11835 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T16:35:25.037+01:00  INFO 11835 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T16:35:25.041+01:00  INFO 11835 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T16:35:25.045+01:00  INFO 11835 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-07-07T16:35:25.057+01:00  INFO 11835 --- [lucid-customer] [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-07T16:35:25.070+01:00 ERROR 11835 --- [lucid-customer] [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration': Injection of autowired dependencies failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:515) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1445) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1122) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1093) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1030) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.2.jar:3.4.2]
	at com.digicore.LucidCustomerServiceApplication.main(LucidCustomerServiceApplication.java:27) ~[classes/:na]
Caused by: java.lang.RuntimeException: Could not postProcess org.springframework.security.config.annotation.web.builders.WebSecurity@40183ded of type class org.springframework.security.config.annotation.web.builders.WebSecurity
	at org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor.postProcess(AutowireBeanFactoryObjectPostProcessor.java:71) ~[spring-security-config-6.4.2.jar:6.4.2]
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration.setFilterChainProxySecurityConfigurer(WebSecurityConfiguration.java:145) ~[spring-security-config-6.4.2.jar:6.4.2]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.inject(AutowiredAnnotationBeanPostProcessor.java:854) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509) ~[spring-beans-6.2.2.jar:6.2.2]
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mvcHandlerMappingIntrospectorRequestTransformer': Cannot resolve reference to bean 'mvcHandlerMappingIntrospector' while setting constructor argument
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:377) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveValueIfNecessary(BeanDefinitionValueResolver.java:135) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveConstructorArguments(ConstructorResolver.java:691) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:206) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:224) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1489) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1450) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:516) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$1.getIfUnique(DefaultListableBeanFactory.java:468) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.security.config.annotation.web.builders.WebSecurity.setApplicationContext(WebSecurity.java:428) ~[spring-security-config-6.4.2.jar:6.4.2]
	at org.springframework.context.support.ApplicationContextAwareProcessor.invokeAwareInterfaces(ApplicationContextAwareProcessor.java:110) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.ApplicationContextAwareProcessor.postProcessBeforeInitialization(ApplicationContextAwareProcessor.java:85) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:423) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:413) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor.initializeBeanIfNeeded(AutowireBeanFactoryObjectPostProcessor.java:98) ~[spring-security-config-6.4.2.jar:6.4.2]
	at org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor.postProcess(AutowireBeanFactoryObjectPostProcessor.java:67) ~[spring-security-config-6.4.2.jar:6.4.2]
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'coronationCustomerUserAuthenticationController' method 
com.digicore.lucid.customer.service.modules.authentication.controller.coronation.CoronationCustomerUserAuthenticationController#retrievePreferences(HttpServletRequest)
to {GET [/api/v1/authentication/process/preference/retrieve]}: There is already 'customerUserAuthenticationController' bean method
com.digicore.lucid.customer.service.modules.authentication.controller.CustomerUserAuthenticationController#retrievePreferences(HttpServletRequest) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:365) ~[spring-beans-6.2.2.jar:6.2.2]
	... 48 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'coronationCustomerUserAuthenticationController' method 
com.digicore.lucid.customer.service.modules.authentication.controller.coronation.CoronationCustomerUserAuthenticationController#retrievePreferences(HttpServletRequest)
to {GET [/api/v1/authentication/process/preference/retrieve]}: There is already 'customerUserAuthenticationController' bean method
com.digicore.lucid.customer.service.modules.authentication.controller.CustomerUserAuthenticationController#retrievePreferences(HttpServletRequest) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:307) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:695) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1426) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:368) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.initHandlerMappings(HandlerMappingIntrospector.java:135) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.afterPropertiesSet(HandlerMappingIntrospector.java:123) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-6.2.2.jar:6.2.2]
	... 55 common frames omitted
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'coronationCustomerUserAuthenticationController' method 
com.digicore.lucid.customer.service.modules.authentication.controller.coronation.CoronationCustomerUserAuthenticationController#retrievePreferences(HttpServletRequest)
to {GET [/api/v1/authentication/process/preference/retrieve]}: There is already 'customerUserAuthenticationController' bean method
com.digicore.lucid.customer.service.modules.authentication.controller.CustomerUserAuthenticationController#retrievePreferences(HttpServletRequest) mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:675) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:637) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:331) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:509) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:84) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$2(AbstractHandlerMethodMapping.java:298) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at java.base/java.util.LinkedHashMap.forEach(LinkedHashMap.java:986) ~[na:na]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:296) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:265) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:224) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:212) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:239) ~[spring-webmvc-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-6.2.2.jar:6.2.2]
	... 68 common frames omitted

2025-07-07T16:57:06.393+01:00  INFO 18114 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 18114 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T16:57:06.395+01:00  INFO 18114 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T16:57:06.423+01:00  INFO 18114 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T16:57:06.423+01:00  INFO 18114 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T16:57:06.423+01:00  INFO 18114 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T16:57:06.424+01:00  INFO 18114 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T16:57:07.191+01:00  INFO 18114 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:57:07.334+01:00  INFO 18114 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 137 ms. Found 19 JPA repository interfaces.
2025-07-07T16:57:07.337+01:00  INFO 18114 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:57:07.347+01:00  INFO 18114 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 4 JPA repository interfaces.
2025-07-07T16:57:07.348+01:00  INFO 18114 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:57:07.351+01:00  INFO 18114 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T16:57:07.351+01:00  INFO 18114 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:57:07.353+01:00  INFO 18114 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-07T16:57:07.379+01:00  INFO 18114 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:57:07.383+01:00  INFO 18114 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T16:57:07.383+01:00  INFO 18114 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:57:07.386+01:00  INFO 18114 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T16:57:07.933+01:00  INFO 18114 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5dc5dd8c-b3fc-3d05-a8bd-9e8185a56d2b
2025-07-07T16:57:08.147+01:00  WARN 18114 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:57:08.150+01:00  WARN 18114 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:57:08.151+01:00  WARN 18114 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000f8016a6570] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:57:08.153+01:00  WARN 18114 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:57:08.158+01:00  WARN 18114 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:57:08.161+01:00  WARN 18114 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:57:08.163+01:00  WARN 18114 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T16:57:08.361+01:00  INFO 18114 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T16:57:08.368+01:00  INFO 18114 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T16:57:08.369+01:00  INFO 18114 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T16:57:08.403+01:00  INFO 18114 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T16:57:08.403+01:00  INFO 18114 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1978 ms
2025-07-07T16:57:08.674+01:00  INFO 18114 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T16:57:08.714+01:00  INFO 18114 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T16:57:08.736+01:00  INFO 18114 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T16:57:08.880+01:00  INFO 18114 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T16:57:08.900+01:00  INFO 18114 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T16:57:09.035+01:00  INFO 18114 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5e352e3a
2025-07-07T16:57:09.037+01:00  INFO 18114 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T16:57:09.138+01:00  INFO 18114 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T16:57:10.418+01:00  INFO 18114 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T16:57:10.664+01:00  INFO 18114 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T16:57:11.206+01:00  INFO 18114 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T16:57:12.637+01:00  INFO 18114 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T16:57:12.910+01:00  INFO 18114 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T16:57:12.968+01:00  WARN 18114 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T16:57:13.074+01:00  INFO 18114 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T16:57:13.126+01:00  INFO 18114 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T16:57:14.506+01:00  WARN 18114 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T16:57:14.751+01:00  INFO 18114 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:57:15.492+01:00  INFO 18114 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:57:15.773+01:00  WARN 18114 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T16:57:15.799+01:00  WARN 18114 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T16:57:23.367+01:00  INFO 18114 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:57:23.825+01:00  WARN 18114 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T16:57:24.226+01:00  INFO 18114 --- [lucid-customer] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T16:57:24.723+01:00  WARN 18114 --- [lucid-customer] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T16:57:24.982+01:00  INFO 18114 --- [lucid-customer] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T16:57:25.143+01:00  INFO 18114 --- [lucid-customer] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T16:57:25.157+01:00  INFO 18114 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T16:57:25.159+01:00  INFO 18114 --- [lucid-customer] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:57:25.163+01:00  INFO 18114 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T16:57:25.163+01:00  INFO 18114 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T16:57:25.163+01:00  INFO 18114 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T16:57:25.163+01:00  INFO 18114 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T16:57:25.163+01:00  INFO 18114 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T16:57:25.163+01:00  INFO 18114 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T16:57:25.163+01:00  INFO 18114 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T16:57:25.303+01:00  INFO 18114 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T16:57:25.304+01:00  INFO 18114 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T16:57:25.305+01:00  INFO 18114 --- [lucid-customer] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T16:57:25.306+01:00  INFO 18114 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751903845305 with initial instances count: 0
2025-07-07T16:57:25.316+01:00  INFO 18114 --- [lucid-customer] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CUSTOMER with eureka with status UP
2025-07-07T16:57:25.317+01:00  INFO 18114 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751903845317, current=UP, previous=STARTING]
2025-07-07T16:57:25.318+01:00  INFO 18114 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T16:57:25.325+01:00  INFO 18114 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8075 (http) with context path '/'
2025-07-07T16:57:25.326+01:00  INFO 18114 --- [lucid-customer] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8075
2025-07-07T16:57:25.344+01:00  INFO 18114 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T16:57:25.439+01:00  INFO 18114 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Started LucidCustomerServiceApplication in 20.24 seconds (process running for 25.848)
2025-07-07T16:57:25.843+01:00  INFO 18114 --- [lucid-customer] [RMI TCP Connection(4)-*************] o.s.a.r.c.CachingConnectionFactory       : Attempting to connect to: [localhost:5672]
2025-07-07T16:57:25.853+01:00  INFO 18114 --- [lucid-customer] [RMI TCP Connection(3)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T16:57:25.853+01:00  INFO 18114 --- [lucid-customer] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T16:57:25.856+01:00  INFO 18114 --- [lucid-customer] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-07T16:57:26.396+01:00  INFO 18114 --- [lucid-customer] [RMI TCP Connection(4)-*************] o.s.a.r.c.CachingConnectionFactory       : Created new connection: rabbitConnectionFactory#143a14d6:0/SimpleConnection@782115f7 [delegate=amqp://guest@127.0.0.1:5672/, localPort=54508]
2025-07-07T16:57:26.408+01:00  INFO 18114 --- [lucid-customer] [RMI TCP Connection(4)-*************] o.s.amqp.rabbit.core.RabbitAdmin         : Auto-declaring a non-durable, auto-delete, or exclusive Queue (lucidNotification) durable:false, auto-delete:false, exclusive:false. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-07T16:57:28.440+01:00  INFO 18114 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CUSTOMER with eureka with status DOWN
2025-07-07T16:57:28.441+01:00  INFO 18114 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751903848441, current=DOWN, previous=UP]
2025-07-07T16:57:28.441+01:00  INFO 18114 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T16:57:28.447+01:00  INFO 18114 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T16:57:28.450+01:00  INFO 18114 --- [lucid-customer] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T16:57:28.453+01:00  INFO 18114 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T16:57:28.513+01:00  INFO 18114 --- [lucid-customer] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T16:57:28.517+01:00  INFO 18114 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T16:57:28.522+01:00  INFO 18114 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T16:57:28.524+01:00  INFO 18114 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T16:57:31.526+01:00  INFO 18114 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T16:57:31.574+01:00  INFO 18114 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - deregister  status: 200
2025-07-07T16:57:31.574+01:00  INFO 18114 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T16:57:38.870+01:00  INFO 18233 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 18233 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T16:57:38.871+01:00  INFO 18233 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T16:57:38.892+01:00  INFO 18233 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T16:57:38.892+01:00  INFO 18233 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T16:57:38.892+01:00  INFO 18233 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T16:57:38.892+01:00  INFO 18233 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T16:57:39.628+01:00  INFO 18233 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:57:39.766+01:00  INFO 18233 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 133 ms. Found 19 JPA repository interfaces.
2025-07-07T16:57:39.770+01:00  INFO 18233 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:57:39.782+01:00  INFO 18233 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T16:57:39.782+01:00  INFO 18233 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:57:39.785+01:00  INFO 18233 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T16:57:39.785+01:00  INFO 18233 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:57:39.788+01:00  INFO 18233 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-07T16:57:39.814+01:00  INFO 18233 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:57:39.817+01:00  INFO 18233 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T16:57:39.818+01:00  INFO 18233 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T16:57:39.820+01:00  INFO 18233 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T16:57:40.360+01:00  INFO 18233 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5dc5dd8c-b3fc-3d05-a8bd-9e8185a56d2b
2025-07-07T16:57:40.565+01:00  WARN 18233 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:57:40.568+01:00  WARN 18233 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:57:40.570+01:00  WARN 18233 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x0000000401694b28] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:57:40.572+01:00  WARN 18233 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:57:40.578+01:00  WARN 18233 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:57:40.582+01:00  WARN 18233 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T16:57:40.584+01:00  WARN 18233 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T16:57:40.790+01:00  INFO 18233 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T16:57:40.799+01:00  INFO 18233 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T16:57:40.799+01:00  INFO 18233 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T16:57:40.840+01:00  INFO 18233 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T16:57:40.841+01:00  INFO 18233 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1948 ms
2025-07-07T16:57:41.125+01:00  INFO 18233 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T16:57:41.164+01:00  INFO 18233 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T16:57:41.182+01:00  INFO 18233 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T16:57:41.307+01:00  INFO 18233 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T16:57:41.325+01:00  INFO 18233 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T16:57:41.441+01:00  INFO 18233 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@70720b78
2025-07-07T16:57:41.443+01:00  INFO 18233 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T16:57:41.495+01:00  INFO 18233 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T16:57:42.386+01:00  INFO 18233 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T16:57:42.517+01:00  INFO 18233 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T16:57:42.743+01:00  INFO 18233 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T16:57:43.828+01:00  INFO 18233 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T16:57:44.085+01:00  INFO 18233 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T16:57:44.123+01:00  WARN 18233 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T16:57:44.213+01:00  INFO 18233 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T16:57:44.264+01:00  INFO 18233 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T16:57:45.488+01:00  WARN 18233 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T16:57:46.567+01:00  INFO 18233 --- [lucid-customer] [main] o.s.cloud.commons.util.InetUtils         : Cannot determine local hostname
2025-07-07T16:57:46.625+01:00  INFO 18233 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:57:46.878+01:00  INFO 18233 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:57:46.974+01:00  WARN 18233 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T16:57:47.013+01:00  WARN 18233 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T16:57:57.154+01:00  INFO 18233 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T16:57:57.638+01:00  WARN 18233 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T16:57:58.128+01:00  INFO 18233 --- [lucid-customer] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T16:57:58.636+01:00  WARN 18233 --- [lucid-customer] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T16:57:58.811+01:00  INFO 18233 --- [lucid-customer] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T16:57:58.933+01:00  INFO 18233 --- [lucid-customer] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T16:57:58.947+01:00  INFO 18233 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T16:57:58.949+01:00  INFO 18233 --- [lucid-customer] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:57:58.953+01:00  INFO 18233 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T16:57:58.953+01:00  INFO 18233 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T16:57:58.953+01:00  INFO 18233 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T16:57:58.953+01:00  INFO 18233 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T16:57:58.953+01:00  INFO 18233 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T16:57:58.953+01:00  INFO 18233 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T16:57:58.953+01:00  INFO 18233 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T16:57:59.119+01:00  INFO 18233 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T16:57:59.120+01:00  INFO 18233 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T16:57:59.121+01:00  INFO 18233 --- [lucid-customer] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T16:57:59.122+01:00  INFO 18233 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751903879121 with initial instances count: 2
2025-07-07T16:57:59.128+01:00  INFO 18233 --- [lucid-customer] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CUSTOMER with eureka with status UP
2025-07-07T16:57:59.129+01:00  INFO 18233 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751903879129, current=UP, previous=STARTING]
2025-07-07T16:57:59.129+01:00  INFO 18233 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T16:57:59.137+01:00  INFO 18233 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8075 (http) with context path '/'
2025-07-07T16:57:59.137+01:00  INFO 18233 --- [lucid-customer] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8075
2025-07-07T16:57:59.160+01:00  INFO 18233 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T16:57:59.305+01:00  INFO 18233 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Started LucidCustomerServiceApplication in 20.863 seconds (process running for 26.514)
2025-07-07T16:57:59.746+01:00  INFO 18233 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.a.r.c.CachingConnectionFactory       : Attempting to connect to: [localhost:5672]
2025-07-07T16:57:59.780+01:00  INFO 18233 --- [lucid-customer] [RMI TCP Connection(3)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T16:57:59.780+01:00  INFO 18233 --- [lucid-customer] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T16:57:59.782+01:00  INFO 18233 --- [lucid-customer] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-07T16:57:59.853+01:00  INFO 18233 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.a.r.c.CachingConnectionFactory       : Created new connection: rabbitConnectionFactory#99a5440:0/SimpleConnection@1e07d1ae [delegate=amqp://guest@127.0.0.1:5672/, localPort=54579]
2025-07-07T16:57:59.860+01:00  INFO 18233 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.amqp.rabbit.core.RabbitAdmin         : Auto-declaring a non-durable, auto-delete, or exclusive Queue (lucidNotification) durable:false, auto-delete:false, exclusive:false. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-07T17:02:58.968+01:00  INFO 18233 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:03:50.546+01:00  INFO 18233 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CUSTOMER with eureka with status DOWN
2025-07-07T17:03:50.548+01:00  INFO 18233 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751904230548, current=DOWN, previous=UP]
2025-07-07T17:03:50.550+01:00  INFO 18233 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T17:03:50.625+01:00  INFO 18233 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T17:03:50.766+01:00  INFO 18233 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T17:03:50.780+01:00  INFO 18233 --- [lucid-customer] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T17:03:50.980+01:00  INFO 18233 --- [lucid-customer] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T17:03:51.000+01:00  INFO 18233 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T17:03:51.029+01:00  INFO 18233 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T17:03:51.043+01:00  INFO 18233 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T17:03:54.054+01:00  INFO 18233 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T17:03:54.115+01:00  INFO 18233 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - deregister  status: 200
2025-07-07T17:03:54.116+01:00  INFO 18233 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T17:04:01.887+01:00  INFO 19724 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 19724 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T17:04:01.888+01:00  INFO 19724 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T17:04:01.911+01:00  INFO 19724 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T17:04:01.911+01:00  INFO 19724 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T17:04:01.911+01:00  INFO 19724 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T17:04:01.911+01:00  INFO 19724 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T17:04:02.708+01:00  INFO 19724 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:04:02.855+01:00  INFO 19724 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 141 ms. Found 19 JPA repository interfaces.
2025-07-07T17:04:02.859+01:00  INFO 19724 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:04:02.870+01:00  INFO 19724 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T17:04:02.871+01:00  INFO 19724 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:04:02.874+01:00  INFO 19724 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:04:02.874+01:00  INFO 19724 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:04:02.876+01:00  INFO 19724 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-07T17:04:02.903+01:00  INFO 19724 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:04:02.906+01:00  INFO 19724 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:04:02.907+01:00  INFO 19724 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:04:02.909+01:00  INFO 19724 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:04:03.555+01:00  INFO 19724 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5dc5dd8c-b3fc-3d05-a8bd-9e8185a56d2b
2025-07-07T17:04:03.765+01:00  WARN 19724 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:04:03.769+01:00  WARN 19724 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:04:03.770+01:00  WARN 19724 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x0000007001697dc0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:04:03.773+01:00  WARN 19724 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:04:03.778+01:00  WARN 19724 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:04:03.781+01:00  WARN 19724 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:04:03.783+01:00  WARN 19724 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T17:04:03.973+01:00  INFO 19724 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T17:04:03.982+01:00  INFO 19724 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T17:04:03.982+01:00  INFO 19724 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T17:04:04.040+01:00  INFO 19724 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T17:04:04.041+01:00  INFO 19724 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2129 ms
2025-07-07T17:04:04.334+01:00  INFO 19724 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T17:04:04.377+01:00  INFO 19724 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T17:04:04.398+01:00  INFO 19724 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T17:04:04.544+01:00  INFO 19724 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T17:04:04.567+01:00  INFO 19724 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T17:04:04.697+01:00  INFO 19724 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5be783a
2025-07-07T17:04:04.699+01:00  INFO 19724 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T17:04:04.782+01:00  INFO 19724 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T17:04:05.644+01:00  INFO 19724 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T17:04:05.794+01:00  INFO 19724 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T17:04:06.002+01:00  INFO 19724 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T17:04:07.106+01:00  INFO 19724 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T17:04:07.347+01:00  INFO 19724 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T17:04:07.382+01:00  WARN 19724 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T17:04:07.467+01:00  INFO 19724 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T17:04:07.533+01:00  INFO 19724 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T17:04:08.768+01:00  WARN 19724 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T17:04:08.870+01:00  INFO 19724 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:04:09.117+01:00  INFO 19724 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:04:09.225+01:00  WARN 19724 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T17:04:09.239+01:00  WARN 19724 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T17:04:15.887+01:00  INFO 19724 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:04:16.297+01:00  WARN 19724 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T17:04:16.720+01:00  INFO 19724 --- [lucid-customer] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T17:04:17.178+01:00  WARN 19724 --- [lucid-customer] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T17:04:17.322+01:00  INFO 19724 --- [lucid-customer] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T17:04:17.440+01:00  INFO 19724 --- [lucid-customer] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T17:04:17.454+01:00  INFO 19724 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T17:04:17.456+01:00  INFO 19724 --- [lucid-customer] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:04:17.459+01:00  INFO 19724 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T17:04:17.459+01:00  INFO 19724 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T17:04:17.459+01:00  INFO 19724 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T17:04:17.459+01:00  INFO 19724 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T17:04:17.460+01:00  INFO 19724 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T17:04:17.460+01:00  INFO 19724 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T17:04:17.460+01:00  INFO 19724 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T17:04:17.594+01:00  INFO 19724 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T17:04:17.595+01:00  INFO 19724 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T17:04:17.596+01:00  INFO 19724 --- [lucid-customer] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T17:04:17.596+01:00  INFO 19724 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751904257596 with initial instances count: 1
2025-07-07T17:04:17.603+01:00  INFO 19724 --- [lucid-customer] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CUSTOMER with eureka with status UP
2025-07-07T17:04:17.604+01:00  INFO 19724 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751904257604, current=UP, previous=STARTING]
2025-07-07T17:04:17.604+01:00  INFO 19724 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T17:04:17.612+01:00  INFO 19724 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8075 (http) with context path '/'
2025-07-07T17:04:17.612+01:00  INFO 19724 --- [lucid-customer] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8075
2025-07-07T17:04:17.630+01:00  INFO 19724 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T17:04:17.746+01:00  INFO 19724 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Started LucidCustomerServiceApplication in 16.487 seconds (process running for 22.472)
2025-07-07T17:04:18.025+01:00  INFO 19724 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T17:04:18.025+01:00  INFO 19724 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T17:04:18.030+01:00  INFO 19724 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 5 ms
2025-07-07T17:04:18.035+01:00  INFO 19724 --- [lucid-customer] [RMI TCP Connection(3)-*************] o.s.a.r.c.CachingConnectionFactory       : Attempting to connect to: [localhost:5672]
2025-07-07T17:04:18.290+01:00  INFO 19724 --- [lucid-customer] [RMI TCP Connection(3)-*************] o.s.a.r.c.CachingConnectionFactory       : Created new connection: rabbitConnectionFactory#296ecb77:0/SimpleConnection@b22178e [delegate=amqp://guest@127.0.0.1:5672/, localPort=55893]
2025-07-07T17:04:18.299+01:00  INFO 19724 --- [lucid-customer] [RMI TCP Connection(3)-*************] o.s.amqp.rabbit.core.RabbitAdmin         : Auto-declaring a non-durable, auto-delete, or exclusive Queue (lucidNotification) durable:false, auto-delete:false, exclusive:false. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-07T17:04:45.751+01:00  INFO 19724 --- [lucid-customer] [http-nio-8075-exec-1] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T17:04:46.399+01:00  INFO 19724 --- [lucid-customer] [ApprovalExecutor-1] c.d.l.c.l.n.s.NotificationDispatcher     : sending EMAIL notification to rabbitmq :: <{"firstName":"SI-NURENI AGBOOLA ABIOLA SI-NURENI","recipients":["<EMAIL>"],"notificationSubject":"Login successful","notificationRequestType":"SEND_LOGIN_SUCCESS_EMAIL","twoFaIsEnabled":false,"isHtml":false,"channel":"EMAIL","dateTime":"7::Jul::2025 17::04::46","logoLink":"","templateName":"login","bankName":"coronation","platformName":"retail","primaryColor":"#5026fb","subDomain":"coronation","supportMailLink":"","helpUrl":"","device":"Browser: Postman Runtime 0, Device: Unknown, OS: Unknown","expiryTimeInMinutes":0}>
2025-07-07T17:06:58.176+01:00  INFO 19724 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CUSTOMER with eureka with status DOWN
2025-07-07T17:06:58.178+01:00  INFO 19724 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=*************, current=DOWN, previous=UP]
2025-07-07T17:06:58.178+01:00  INFO 19724 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T17:06:58.190+01:00  INFO 19724 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T17:06:58.198+01:00  INFO 19724 --- [lucid-customer] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T17:06:58.226+01:00  INFO 19724 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T17:06:58.304+01:00  INFO 19724 --- [lucid-customer] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T17:06:58.307+01:00  INFO 19724 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T17:06:58.313+01:00  INFO 19724 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T17:06:58.314+01:00  INFO 19724 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T17:07:01.316+01:00  INFO 19724 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T17:07:01.329+01:00  INFO 19724 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - deregister  status: 200
2025-07-07T17:07:01.330+01:00  INFO 19724 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T17:07:09.358+01:00  INFO 20502 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 20502 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T17:07:09.359+01:00  INFO 20502 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T17:07:09.380+01:00  INFO 20502 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T17:07:09.380+01:00  INFO 20502 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T17:07:09.381+01:00  INFO 20502 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T17:07:09.381+01:00  INFO 20502 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T17:07:10.150+01:00  INFO 20502 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:07:10.298+01:00  INFO 20502 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 142 ms. Found 19 JPA repository interfaces.
2025-07-07T17:07:10.302+01:00  INFO 20502 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:07:10.313+01:00  INFO 20502 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T17:07:10.313+01:00  INFO 20502 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:07:10.316+01:00  INFO 20502 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:07:10.316+01:00  INFO 20502 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:07:10.319+01:00  INFO 20502 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-07T17:07:10.345+01:00  INFO 20502 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:07:10.348+01:00  INFO 20502 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:07:10.349+01:00  INFO 20502 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:07:10.351+01:00  INFO 20502 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:07:10.909+01:00  INFO 20502 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=23bc0fb5-637d-308d-8a9f-28ae5f7dca9b
2025-07-07T17:07:11.127+01:00  WARN 20502 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:07:11.130+01:00  WARN 20502 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:07:11.131+01:00  WARN 20502 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000040169d570] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:07:11.133+01:00  WARN 20502 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:07:11.138+01:00  WARN 20502 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:07:11.141+01:00  WARN 20502 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:07:11.143+01:00  WARN 20502 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T17:07:11.349+01:00  INFO 20502 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T17:07:11.358+01:00  INFO 20502 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T17:07:11.358+01:00  INFO 20502 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T17:07:11.414+01:00  INFO 20502 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T17:07:11.415+01:00  INFO 20502 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2033 ms
2025-07-07T17:07:11.706+01:00  INFO 20502 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T17:07:11.746+01:00  INFO 20502 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T17:07:11.767+01:00  INFO 20502 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T17:07:11.902+01:00  INFO 20502 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T17:07:11.922+01:00  INFO 20502 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T17:07:12.029+01:00  INFO 20502 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@b89cbf9
2025-07-07T17:07:12.030+01:00  INFO 20502 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T17:07:12.089+01:00  INFO 20502 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T17:07:13.001+01:00  INFO 20502 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T17:07:13.129+01:00  INFO 20502 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T17:07:13.401+01:00  INFO 20502 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T17:07:14.491+01:00  INFO 20502 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T17:07:14.742+01:00  INFO 20502 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T17:07:14.776+01:00  WARN 20502 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T17:07:14.881+01:00  INFO 20502 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T17:07:14.937+01:00  INFO 20502 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T17:07:16.266+01:00  WARN 20502 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T17:07:16.369+01:00  INFO 20502 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:07:16.613+01:00  INFO 20502 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:07:16.714+01:00  WARN 20502 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T17:07:16.731+01:00  WARN 20502 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T17:07:30.063+01:00  INFO 20502 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:07:30.514+01:00  WARN 20502 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T17:07:30.900+01:00  INFO 20502 --- [lucid-customer] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T17:07:31.382+01:00  WARN 20502 --- [lucid-customer] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T17:07:31.529+01:00  INFO 20502 --- [lucid-customer] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T17:07:31.693+01:00  INFO 20502 --- [lucid-customer] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T17:07:31.713+01:00  INFO 20502 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T17:07:31.715+01:00  INFO 20502 --- [lucid-customer] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:07:31.720+01:00  INFO 20502 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T17:07:31.720+01:00  INFO 20502 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T17:07:31.720+01:00  INFO 20502 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T17:07:31.720+01:00  INFO 20502 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T17:07:31.720+01:00  INFO 20502 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T17:07:31.720+01:00  INFO 20502 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T17:07:31.720+01:00  INFO 20502 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T17:07:31.862+01:00  INFO 20502 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T17:07:31.863+01:00  INFO 20502 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T17:07:31.864+01:00  INFO 20502 --- [lucid-customer] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T17:07:31.865+01:00  INFO 20502 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751904451865 with initial instances count: 1
2025-07-07T17:07:31.873+01:00  INFO 20502 --- [lucid-customer] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CUSTOMER with eureka with status UP
2025-07-07T17:07:31.873+01:00  INFO 20502 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751904451873, current=UP, previous=STARTING]
2025-07-07T17:07:31.874+01:00  INFO 20502 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T17:07:31.884+01:00  INFO 20502 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8075 (http) with context path '/'
2025-07-07T17:07:31.884+01:00  INFO 20502 --- [lucid-customer] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8075
2025-07-07T17:07:31.907+01:00  INFO 20502 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T17:07:31.995+01:00  INFO 20502 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Started LucidCustomerServiceApplication in 23.412 seconds (process running for 29.475)
2025-07-07T17:07:32.316+01:00  INFO 20502 --- [lucid-customer] [RMI TCP Connection(1)-*************] o.s.a.r.c.CachingConnectionFactory       : Attempting to connect to: [localhost:5672]
2025-07-07T17:07:32.364+01:00  INFO 20502 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T17:07:32.365+01:00  INFO 20502 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T17:07:32.368+01:00  INFO 20502 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-07T17:07:32.729+01:00  INFO 20502 --- [lucid-customer] [RMI TCP Connection(1)-*************] o.s.a.r.c.CachingConnectionFactory       : Created new connection: rabbitConnectionFactory#798d6d05:0/SimpleConnection@4f0f0c4c [delegate=amqp://guest@127.0.0.1:5672/, localPort=56878]
2025-07-07T17:07:32.737+01:00  INFO 20502 --- [lucid-customer] [RMI TCP Connection(1)-*************] o.s.amqp.rabbit.core.RabbitAdmin         : Auto-declaring a non-durable, auto-delete, or exclusive Queue (lucidNotification) durable:false, auto-delete:false, exclusive:false. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-07T17:08:00.046+01:00  INFO 20502 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CUSTOMER with eureka with status DOWN
2025-07-07T17:08:00.048+01:00  INFO 20502 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751904480048, current=DOWN, previous=UP]
2025-07-07T17:08:00.049+01:00  INFO 20502 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T17:08:00.086+01:00  INFO 20502 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T17:08:00.096+01:00  INFO 20502 --- [lucid-customer] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T17:08:00.108+01:00  INFO 20502 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T17:08:00.169+01:00  INFO 20502 --- [lucid-customer] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T17:08:00.173+01:00  INFO 20502 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T17:08:00.184+01:00  INFO 20502 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T17:08:00.186+01:00  INFO 20502 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T17:08:03.192+01:00  INFO 20502 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T17:08:03.193+01:00  WARN 20502 --- [lucid-customer] [DiscoveryClient-%d] c.netflix.discovery.TimedSupervisorTask  : task supervisor shutting down, can't accept the task
2025-07-07T17:08:03.210+01:00  INFO 20502 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - deregister  status: 200
2025-07-07T17:08:03.210+01:00  INFO 20502 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T17:08:10.800+01:00  INFO 21080 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Starting LucidCustomerServiceApplication using Java 21.0.1 with PID 21080 (/Users/<USER>/Desktop/LUCID/lucid-customer-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T17:08:10.801+01:00  INFO 21080 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : The following 8 profiles are active: "dev", "messages", "mails", "verification-flow", "security-question", "enterprise", "coronation", "coronation-provider"
2025-07-07T17:08:10.823+01:00  INFO 21080 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T17:08:10.824+01:00  INFO 21080 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[default], label=null, version=null, state=null
2025-07-07T17:08:10.824+01:00  INFO 21080 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T17:08:10.824+01:00  INFO 21080 --- [lucid-customer] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-customer, profiles=[dev,messages,mails,verification-flow,security-question,enterprise,coronation,coronation-provider], label=null, version=null, state=null
2025-07-07T17:08:11.582+01:00  INFO 21080 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:08:11.728+01:00  INFO 21080 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 141 ms. Found 19 JPA repository interfaces.
2025-07-07T17:08:11.732+01:00  INFO 21080 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:08:11.743+01:00  INFO 21080 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-07-07T17:08:11.743+01:00  INFO 21080 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:08:11.746+01:00  INFO 21080 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:08:11.746+01:00  INFO 21080 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:08:11.749+01:00  INFO 21080 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-07T17:08:11.775+01:00  INFO 21080 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:08:11.778+01:00  INFO 21080 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:08:11.779+01:00  INFO 21080 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07T17:08:11.782+01:00  INFO 21080 --- [lucid-customer] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-07T17:08:12.343+01:00  INFO 21080 --- [lucid-customer] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=23bc0fb5-637d-308d-8a9f-28ae5f7dca9b
2025-07-07T17:08:12.551+01:00  WARN 21080 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:08:12.555+01:00  WARN 21080 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:08:12.556+01:00  WARN 21080 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000700169d770] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:08:12.558+01:00  WARN 21080 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:08:12.565+01:00  WARN 21080 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'soteriaKeeperPropertyConfig' of type [com.digicore.lucid.common.lib.properties.SoteriaKeeperPropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:08:12.568+01:00  WARN 21080 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'filePropertyConfig' of type [com.digicore.lucid.common.lib.properties.FilePropertyConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [requestHandlerPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07T17:08:12.570+01:00  WARN 21080 --- [lucid-customer] [main] trationDelegate$BeanPostProcessorChecker : Bean 'beanConfig' of type [com.digicore.lucid.common.lib.bean.BeanConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [requestHandlerPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-07T17:08:12.771+01:00  INFO 21080 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8075 (http)
2025-07-07T17:08:12.781+01:00  INFO 21080 --- [lucid-customer] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T17:08:12.781+01:00  INFO 21080 --- [lucid-customer] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T17:08:12.829+01:00  INFO 21080 --- [lucid-customer] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T17:08:12.829+01:00  INFO 21080 --- [lucid-customer] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2004 ms
2025-07-07T17:08:13.122+01:00  INFO 21080 --- [lucid-customer] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07T17:08:13.162+01:00  INFO 21080 --- [lucid-customer] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.5.Final
2025-07-07T17:08:13.183+01:00  INFO 21080 --- [lucid-customer] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-07T17:08:13.325+01:00  INFO 21080 --- [lucid-customer] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07T17:08:13.348+01:00  INFO 21080 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-07T17:08:13.454+01:00  INFO 21080 --- [lucid-customer] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6178ac9d
2025-07-07T17:08:13.455+01:00  INFO 21080 --- [lucid-customer] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-07T17:08:13.507+01:00  INFO 21080 --- [lucid-customer] [main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-07T17:08:14.389+01:00  INFO 21080 --- [lucid-customer] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07T17:08:14.518+01:00  INFO 21080 --- [lucid-customer] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T17:08:14.734+01:00  INFO 21080 --- [lucid-customer] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07T17:08:15.911+01:00  INFO 21080 --- [lucid-customer] [main] c.d.l.common.lib.util.DefaultFileUtil    : <<< Instantiating DefaultFileUtil >>>
2025-07-07T17:08:16.175+01:00  INFO 21080 --- [lucid-customer] [main] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T17:08:16.211+01:00  WARN 21080 --- [lucid-customer] [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-07T17:08:16.294+01:00  INFO 21080 --- [lucid-customer] [redisson-netty-1-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T17:08:16.353+01:00  INFO 21080 --- [lucid-customer] [redisson-netty-1-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T17:08:17.558+01:00  WARN 21080 --- [lucid-customer] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07T17:08:17.662+01:00  INFO 21080 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-cba' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:08:17.944+01:00  INFO 21080 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-backoffice' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:08:18.029+01:00  WARN 21080 --- [lucid-customer] [main] ion$DefaultTemplateResolverConfiguration : Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-07-07T17:08:18.043+01:00  WARN 21080 --- [lucid-customer] [main] o.thymeleaf.templatemode.TemplateMode    : [THYMELEAF][main] Unknown Template Mode 'HTML5'. Must be one of: 'HTML', 'XML', 'TEXT', 'JAVASCRIPT', 'CSS', 'RAW'. Using default Template Mode 'HTML'.
2025-07-07T17:08:23.998+01:00  INFO 21080 --- [lucid-customer] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'lucid-transfer' URL not provided. Will try picking an instance via load-balancing.
2025-07-07T17:08:24.432+01:00  WARN 21080 --- [lucid-customer] [main] r$InitializeUserDetailsManagerConfigurer : Found 2 UserDetailsService beans, with names [customerUserLoginService, coronationCustomerUserLoginService]. Global Authentication Manager will not use a UserDetailsService for username/password login. Consider publishing a single UserDetailsService bean.
2025-07-07T17:08:24.831+01:00  INFO 21080 --- [lucid-customer] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T17:08:25.292+01:00  WARN 21080 --- [lucid-customer] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T17:08:25.435+01:00  INFO 21080 --- [lucid-customer] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T17:08:25.559+01:00  INFO 21080 --- [lucid-customer] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T17:08:25.572+01:00  INFO 21080 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T17:08:25.574+01:00  INFO 21080 --- [lucid-customer] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:08:25.578+01:00  INFO 21080 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T17:08:25.578+01:00  INFO 21080 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T17:08:25.578+01:00  INFO 21080 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T17:08:25.578+01:00  INFO 21080 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T17:08:25.578+01:00  INFO 21080 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T17:08:25.578+01:00  INFO 21080 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T17:08:25.578+01:00  INFO 21080 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T17:08:25.702+01:00  INFO 21080 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T17:08:25.703+01:00  INFO 21080 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T17:08:25.703+01:00  INFO 21080 --- [lucid-customer] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T17:08:25.704+01:00  INFO 21080 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751904505704 with initial instances count: 2
2025-07-07T17:08:25.711+01:00  INFO 21080 --- [lucid-customer] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CUSTOMER with eureka with status UP
2025-07-07T17:08:25.711+01:00  INFO 21080 --- [lucid-customer] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751904505711, current=UP, previous=STARTING]
2025-07-07T17:08:25.711+01:00  INFO 21080 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T17:08:25.717+01:00  INFO 21080 --- [lucid-customer] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8075 (http) with context path '/'
2025-07-07T17:08:25.718+01:00  INFO 21080 --- [lucid-customer] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8075
2025-07-07T17:08:25.733+01:00  INFO 21080 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T17:08:25.829+01:00  INFO 21080 --- [lucid-customer] [main] c.d.LucidCustomerServiceApplication      : Started LucidCustomerServiceApplication in 15.565 seconds (process running for 21.35)
2025-07-07T17:08:26.229+01:00  INFO 21080 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T17:08:26.229+01:00  INFO 21080 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T17:08:26.232+01:00  INFO 21080 --- [lucid-customer] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-07T17:08:26.234+01:00  INFO 21080 --- [lucid-customer] [RMI TCP Connection(1)-*************] o.s.a.r.c.CachingConnectionFactory       : Attempting to connect to: [localhost:5672]
2025-07-07T17:08:26.293+01:00  INFO 21080 --- [lucid-customer] [RMI TCP Connection(1)-*************] o.s.a.r.c.CachingConnectionFactory       : Created new connection: rabbitConnectionFactory#410dcb8b:0/SimpleConnection@849eed5 [delegate=amqp://guest@127.0.0.1:5672/, localPort=57130]
2025-07-07T17:08:26.299+01:00  INFO 21080 --- [lucid-customer] [RMI TCP Connection(1)-*************] o.s.amqp.rabbit.core.RabbitAdmin         : Auto-declaring a non-durable, auto-delete, or exclusive Queue (lucidNotification) durable:false, auto-delete:false, exclusive:false. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-07T17:09:14.413+01:00  INFO 21080 --- [lucid-customer] [http-nio-8075-exec-1] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T17:09:15.023+01:00  INFO 21080 --- [lucid-customer] [ApprovalExecutor-1] c.d.l.c.l.n.s.NotificationDispatcher     : sending EMAIL notification to rabbitmq :: <{"firstName":"SI-NURENI AGBOOLA ABIOLA SI-NURENI","recipients":["<EMAIL>"],"notificationSubject":"Login successful","notificationRequestType":"SEND_LOGIN_SUCCESS_EMAIL","twoFaIsEnabled":false,"isHtml":false,"channel":"EMAIL","dateTime":"7::Jul::2025 17::09::15","logoLink":"","templateName":"login","bankName":"coronation","platformName":"retail","primaryColor":"#5026fb","subDomain":"coronation","supportMailLink":"","helpUrl":"","device":"Browser: Postman Runtime 0, Device: Unknown, OS: Unknown","expiryTimeInMinutes":0}>
2025-07-07T17:09:28.036+01:00  INFO 21080 --- [lucid-customer] [http-nio-8075-exec-2] c.d.lucid.common.lib.util.ClientUtil     : <<< extracting subdomain/applicationId >>>
2025-07-07T17:09:28.368+01:00  INFO 21080 --- [lucid-customer] [ApprovalExecutor-2] c.d.l.c.l.n.s.NotificationDispatcher     : sending EMAIL notification to rabbitmq :: <{"firstName":"SI-NURENI AGBOOLA ABIOLA SI-NURENI","recipients":["<EMAIL>"],"notificationSubject":"Login successful","notificationRequestType":"SEND_LOGIN_SUCCESS_EMAIL","twoFaIsEnabled":false,"isHtml":false,"channel":"EMAIL","dateTime":"7::Jul::2025 17::09::28","logoLink":"","templateName":"login","bankName":"coronation","platformName":"retail","primaryColor":"#5026fb","subDomain":"coronation","supportMailLink":"","helpUrl":"","device":"Browser: Postman Runtime 0, Device: Unknown, OS: Unknown","expiryTimeInMinutes":0}>
2025-07-07T17:13:25.581+01:00  INFO 21080 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:18:25.544+01:00  INFO 21080 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:23:25.551+01:00  INFO 21080 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:28:25.561+01:00  INFO 21080 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:33:25.728+01:00  INFO 21080 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:38:25.742+01:00  INFO 21080 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:43:25.748+01:00  INFO 21080 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:48:25.761+01:00  INFO 21080 --- [lucid-customer] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:48:50.903+01:00  INFO 21080 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CUSTOMER with eureka with status DOWN
2025-07-07T17:48:50.906+01:00  INFO 21080 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751906930905, current=DOWN, previous=UP]
2025-07-07T17:48:50.907+01:00  INFO 21080 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075: registering service...
2025-07-07T17:48:50.979+01:00  INFO 21080 --- [lucid-customer] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - registration status: 204
2025-07-07T17:48:51.059+01:00  INFO 21080 --- [lucid-customer] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T17:48:51.075+01:00  INFO 21080 --- [lucid-customer] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T17:48:51.248+01:00  INFO 21080 --- [lucid-customer] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07T17:48:51.324+01:00  INFO 21080 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-07T17:48:51.326+01:00  INFO 21080 --- [lucid-customer] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-07T17:48:51.328+01:00  INFO 21080 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T17:48:54.331+01:00  INFO 21080 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T17:48:54.347+01:00  INFO 21080 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CUSTOMER/*************:lucid-customer:8075 - deregister  status: 200
2025-07-07T17:48:54.348+01:00  INFO 21080 --- [lucid-customer] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
