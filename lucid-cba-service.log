2025-07-07T17:57:13.300+01:00  INFO 31783 --- [lucid-cba] [main] c.digicore.LucidCbaServiceApplication    : Starting LucidCbaServiceApplication using Java 21.0.1 with PID 31783 (/Users/<USER>/Desktop/LUCID/lucid-cba-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T17:57:13.301+01:00  INFO 31783 --- [lucid-cba] [main] c.digicore.LucidCbaServiceApplication    : The following 5 profiles are active: "dev", "messages", "enterprise", "coronation-provider", "coronation"
2025-07-07T17:57:13.326+01:00  INFO 31783 --- [lucid-cba] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T17:57:13.326+01:00  INFO 31783 --- [lucid-cba] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-cba, profiles=[default], label=null, version=null, state=null
2025-07-07T17:57:13.326+01:00  INFO 31783 --- [lucid-cba] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T17:57:13.326+01:00  INFO 31783 --- [lucid-cba] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-cba, profiles=[dev,messages,enterprise,coronation-provider,coronation], label=null, version=null, state=null
2025-07-07T17:57:13.997+01:00  INFO 31783 --- [lucid-cba] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=a599e8bf-0c1b-3f85-8fed-af668f5b5cd7
2025-07-07T17:57:14.174+01:00  INFO 31783 --- [lucid-cba] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8076 (http)
2025-07-07T17:57:14.180+01:00  INFO 31783 --- [lucid-cba] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T17:57:14.180+01:00  INFO 31783 --- [lucid-cba] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T17:57:14.205+01:00  INFO 31783 --- [lucid-cba] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T17:57:14.206+01:00  INFO 31783 --- [lucid-cba] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 879 ms
2025-07-07T17:57:14.858+01:00  INFO 31783 --- [lucid-cba] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T17:57:15.073+01:00  WARN 31783 --- [lucid-cba] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T17:57:15.132+01:00  INFO 31783 --- [lucid-cba] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T17:57:15.180+01:00  INFO 31783 --- [lucid-cba] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T17:57:15.191+01:00  INFO 31783 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T17:57:15.192+01:00  INFO 31783 --- [lucid-cba] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:57:15.196+01:00  INFO 31783 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T17:57:15.196+01:00  INFO 31783 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T17:57:15.196+01:00  INFO 31783 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T17:57:15.196+01:00  INFO 31783 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T17:57:15.196+01:00  INFO 31783 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T17:57:15.196+01:00  INFO 31783 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T17:57:15.196+01:00  INFO 31783 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T17:57:15.340+01:00  INFO 31783 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T17:57:15.341+01:00  INFO 31783 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T17:57:15.342+01:00  INFO 31783 --- [lucid-cba] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T17:57:15.343+01:00  INFO 31783 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751907435343 with initial instances count: 3
2025-07-07T17:57:15.346+01:00  INFO 31783 --- [lucid-cba] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CBA with eureka with status UP
2025-07-07T17:57:15.347+01:00  INFO 31783 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751907435347, current=UP, previous=STARTING]
2025-07-07T17:57:15.347+01:00  INFO 31783 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076: registering service...
2025-07-07T17:57:15.358+01:00  INFO 31783 --- [lucid-cba] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8076 (http) with context path '/'
2025-07-07T17:57:15.359+01:00  INFO 31783 --- [lucid-cba] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8076
2025-07-07T17:57:15.380+01:00  INFO 31783 --- [lucid-cba] [main] c.digicore.LucidCbaServiceApplication    : Started LucidCbaServiceApplication in 2.551 seconds (process running for 8.174)
2025-07-07T17:57:15.386+01:00  INFO 31783 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076 - registration status: 204
2025-07-07T17:57:16.055+01:00  INFO 31783 --- [lucid-cba] [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T17:57:16.055+01:00  INFO 31783 --- [lucid-cba] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T17:57:16.056+01:00  INFO 31783 --- [lucid-cba] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-07T17:59:07.770+01:00  INFO 31783 --- [lucid-cba] [http-nio-8076-exec-1] d.l.c.s.m.p.a.c.AccountServiceController : the request is : {responseStatus=null, narration=null, provider=null, serviceRequired=null, accountNumber=**********, customerId=null, fetchAllAccounts=false}
2025-07-07T17:59:07.771+01:00  INFO 31783 --- [lucid-cba] [http-nio-8076-exec-1] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< request : {responseStatus=null, narration=null, provider=null, serviceRequired=null, accountNumber=**********, customerId=null, fetchAllAccounts=false} >>>
2025-07-07T17:59:07.771+01:00  INFO 31783 --- [lucid-cba] [http-nio-8076-exec-1] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< provider : coronation >>>
2025-07-07T17:59:07.771+01:00  INFO 31783 --- [lucid-cba] [http-nio-8076-exec-1] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< serviceRequired : OrganizationAccountDetail >>>
2025-07-07T17:59:08.418+01:00  INFO 31783 --- [lucid-cba] [http-nio-8076-exec-1] .c.s.m.p.c.s.a.s.CoronationTokenProvider : <<< auth response : {"flag":true,"code":"00","message":"Authentication Successful","data":{"accessToken":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************************************************.aov5NvJP1aJ1ZuhfQPjO0X1e1AEq7ShA2K_kpM79KDlN6MtRFL_CflS6U3VBHy5PlOda4OZXlhkfmsI5IlRckQ","duration":3600}} >>>
2025-07-07T17:59:08.675+01:00  INFO 31783 --- [lucid-cba] [http-nio-8076-exec-1] nOrganizationFetchCustomerAccountService : <<< coronation account response : {"flag":true,"code":"00","message":"SUCCESS","data":{"responseText":"SUCCESS","responseCode":"00","accountNumber":"**********","accountCurrency":"EUR","accountName":"POLYSMART PACKAGING LTD","status":"A","balance":1000000,"restriction":"N","cifId":"C000947","accountSchemeCode":"204","misCode":"CB35C","effectiveBalance":1000000,"phoneNumber":"+234(803)6203846","email":"<EMAIL>","tin":"***********","rcNumber":"*********","bvn":null,"address":"NO.7,POLYSMART AVENUE SANGO OTTA INDUSTRY EST","branchCode":"999","bankId":"01","schemeType":"CAA","customerDateOfBirth":"2012-06-27 00:00:00.0","salutation":"M/S","tier":"TIER2"}} >>>
2025-07-07T17:59:23.172+01:00  INFO 31783 --- [lucid-cba] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CBA with eureka with status DOWN
2025-07-07T17:59:23.174+01:00  INFO 31783 --- [lucid-cba] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=*************, current=DOWN, previous=UP]
2025-07-07T17:59:23.175+01:00  INFO 31783 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076: registering service...
2025-07-07T17:59:23.193+01:00  INFO 31783 --- [lucid-cba] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T17:59:23.216+01:00  INFO 31783 --- [lucid-cba] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T17:59:23.287+01:00  INFO 31783 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076 - registration status: 204
2025-07-07T17:59:23.298+01:00  INFO 31783 --- [lucid-cba] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T17:59:26.309+01:00  INFO 31783 --- [lucid-cba] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T17:59:26.378+01:00  INFO 31783 --- [lucid-cba] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/} exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CBA/*************:lucid-cba:8076": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CBA/*************:lucid-cba:8076": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:91)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 37 more

2025-07-07T17:59:26.378+01:00  WARN 31783 --- [lucid-cba] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CBA/*************:lucid-cba:8076": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T17:59:26.390+01:00  INFO 31783 --- [lucid-cba] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CBA/*************:lucid-cba:8076": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CBA/*************:lucid-cba:8076": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 38 more

2025-07-07T17:59:26.390+01:00  WARN 31783 --- [lucid-cba] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CBA/*************:lucid-cba:8076": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T17:59:26.391+01:00 ERROR 31783 --- [lucid-cba] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147) ~[spring-boot-3.4.2.jar:3.4.2]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116) ~[spring-boot-3.4.2.jar:3.4.2]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T17:59:26.397+01:00  INFO 31783 --- [lucid-cba] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T19:23:21.030+01:00  INFO 49671 --- [lucid-cba] [main] c.digicore.LucidCbaServiceApplication    : Starting LucidCbaServiceApplication using Java 21.0.1 with PID 49671 (/Users/<USER>/Desktop/LUCID/lucid-cba-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T19:23:21.031+01:00  INFO 49671 --- [lucid-cba] [main] c.digicore.LucidCbaServiceApplication    : The following 5 profiles are active: "dev", "messages", "enterprise", "coronation-provider", "coronation"
2025-07-07T19:23:21.051+01:00  INFO 49671 --- [lucid-cba] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T19:23:21.051+01:00  INFO 49671 --- [lucid-cba] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-cba, profiles=[default], label=null, version=null, state=null
2025-07-07T19:23:21.051+01:00  INFO 49671 --- [lucid-cba] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T19:23:21.051+01:00  INFO 49671 --- [lucid-cba] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-cba, profiles=[dev,messages,enterprise,coronation-provider,coronation], label=null, version=null, state=null
2025-07-07T19:23:22.076+01:00  INFO 49671 --- [lucid-cba] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=a599e8bf-0c1b-3f85-8fed-af668f5b5cd7
2025-07-07T19:23:22.359+01:00  INFO 49671 --- [lucid-cba] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8076 (http)
2025-07-07T19:23:22.367+01:00  INFO 49671 --- [lucid-cba] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T19:23:22.368+01:00  INFO 49671 --- [lucid-cba] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T19:23:22.398+01:00  INFO 49671 --- [lucid-cba] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T19:23:22.398+01:00  INFO 49671 --- [lucid-cba] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1346 ms
2025-07-07T19:23:23.519+01:00  INFO 49671 --- [lucid-cba] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T19:23:23.793+01:00  WARN 49671 --- [lucid-cba] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T19:23:23.853+01:00  INFO 49671 --- [lucid-cba] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T19:23:23.905+01:00  INFO 49671 --- [lucid-cba] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T19:23:23.919+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T19:23:23.921+01:00  INFO 49671 --- [lucid-cba] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T19:23:23.926+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T19:23:23.926+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T19:23:23.926+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T19:23:23.926+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T19:23:23.926+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T19:23:23.926+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T19:23:23.926+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T19:23:24.062+01:00  INFO 49671 --- [lucid-cba] [main] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on GET request for "http://localhost:8070/eureka/apps/": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:8070/eureka/apps/": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.getApplicationsInternal(RestTemplateEurekaHttpClient.java:174)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.getApplications(RestTemplateEurekaHttpClient.java:162)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$6.execute(EurekaHttpClientDecorator.java:137)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getApplications(EurekaHttpClientDecorator.java:134)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$6.execute(EurekaHttpClientDecorator.java:137)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getApplications(EurekaHttpClientDecorator.java:134)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$6.execute(EurekaHttpClientDecorator.java:137)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getApplications(EurekaHttpClientDecorator.java:134)
	at com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry(DiscoveryClient.java:1046)
	at com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:961)
	at com.netflix.discovery.DiscoveryClient.<init>(DiscoveryClient.java:410)
	at com.netflix.discovery.DiscoveryClient.<init>(DiscoveryClient.java:245)
	at com.netflix.discovery.DiscoveryClient.<init>(DiscoveryClient.java:240)
	at org.springframework.cloud.netflix.eureka.CloudEurekaClient.<init>(CloudEurekaClient.java:68)
	at org.springframework.cloud.netflix.eureka.EurekaClientAutoConfiguration$RefreshableEurekaClientConfiguration.eurekaClient(EurekaClientAutoConfiguration.java:324)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$1(AbstractBeanFactory.java:375)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.getBean(GenericScope.java:375)
	at org.springframework.cloud.context.scope.GenericScope.get(GenericScope.java:179)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:372)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.aop.target.SimpleBeanTargetSource.getTarget(SimpleBeanTargetSource.java:35)
	at org.springframework.cloud.netflix.eureka.serviceregistry.EurekaRegistration.getTargetObject(EurekaRegistration.java:128)
	at org.springframework.cloud.netflix.eureka.serviceregistry.EurekaRegistration.getEurekaClient(EurekaRegistration.java:116)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at org.springframework.cloud.context.scope.GenericScope$LockedScopedProxyFactoryBean.invoke(GenericScope.java:482)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at org.springframework.cloud.netflix.eureka.serviceregistry.EurekaRegistration$$SpringCGLIB$$0.getEurekaClient(<generated>)
	at org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry.maybeInitializeClient(EurekaServiceRegistry.java:83)
	at org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry.register(EurekaServiceRegistry.java:66)
	at org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration.start(EurekaAutoServiceRegistration.java:89)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:323)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:510)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:295)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:240)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:1006)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:630)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.digicore.LucidCbaServiceApplication.main(LucidCbaServiceApplication.java:15)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 64 more

2025-07-07T19:23:24.062+01:00  WARN 49671 --- [lucid-cba] [main] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on GET request for "http://localhost:8070/eureka/apps/": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T19:23:24.062+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076 - was unable to refresh its cache! This periodic background refresh will be retried in 30 seconds. status = Cannot execute request on any known server stacktrace = com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getApplications(EurekaHttpClientDecorator.java:134)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$6.execute(EurekaHttpClientDecorator.java:137)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getApplications(EurekaHttpClientDecorator.java:134)
	at com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry(DiscoveryClient.java:1046)
	at com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:961)
	at com.netflix.discovery.DiscoveryClient.<init>(DiscoveryClient.java:410)
	at com.netflix.discovery.DiscoveryClient.<init>(DiscoveryClient.java:245)
	at com.netflix.discovery.DiscoveryClient.<init>(DiscoveryClient.java:240)
	at org.springframework.cloud.netflix.eureka.CloudEurekaClient.<init>(CloudEurekaClient.java:68)
	at org.springframework.cloud.netflix.eureka.EurekaClientAutoConfiguration$RefreshableEurekaClientConfiguration.eurekaClient(EurekaClientAutoConfiguration.java:324)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$1(AbstractBeanFactory.java:375)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.getBean(GenericScope.java:375)
	at org.springframework.cloud.context.scope.GenericScope.get(GenericScope.java:179)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:372)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.aop.target.SimpleBeanTargetSource.getTarget(SimpleBeanTargetSource.java:35)
	at org.springframework.cloud.netflix.eureka.serviceregistry.EurekaRegistration.getTargetObject(EurekaRegistration.java:128)
	at org.springframework.cloud.netflix.eureka.serviceregistry.EurekaRegistration.getEurekaClient(EurekaRegistration.java:116)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at org.springframework.cloud.context.scope.GenericScope$LockedScopedProxyFactoryBean.invoke(GenericScope.java:482)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at org.springframework.cloud.netflix.eureka.serviceregistry.EurekaRegistration$$SpringCGLIB$$0.getEurekaClient(<generated>)
	at org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry.maybeInitializeClient(EurekaServiceRegistry.java:83)
	at org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry.register(EurekaServiceRegistry.java:66)
	at org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration.start(EurekaAutoServiceRegistration.java:89)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:323)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:510)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:295)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:240)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:1006)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:630)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.digicore.LucidCbaServiceApplication.main(LucidCbaServiceApplication.java:15)

2025-07-07T19:23:24.062+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Initial registry fetch from primary servers failed
2025-07-07T19:23:24.062+01:00  WARN 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Using default backup registry implementation which does not do anything.
2025-07-07T19:23:24.062+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Initial registry fetch from backup servers failed
2025-07-07T19:23:24.063+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T19:23:24.063+01:00  INFO 49671 --- [lucid-cba] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T19:23:24.064+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751912604064 with initial instances count: 0
2025-07-07T19:23:24.068+01:00  INFO 49671 --- [lucid-cba] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CBA with eureka with status UP
2025-07-07T19:23:24.068+01:00  INFO 49671 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751912604068, current=UP, previous=STARTING]
2025-07-07T19:23:24.069+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076: registering service...
2025-07-07T19:23:24.082+01:00  INFO 49671 --- [lucid-cba] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8076 (http) with context path '/'
2025-07-07T19:23:24.082+01:00  INFO 49671 --- [lucid-cba] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8076
2025-07-07T19:23:24.088+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on POST request for "http://localhost:8070/eureka/apps/LUCID-CBA": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:8070/eureka/apps/LUCID-CBA": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.register(RestTemplateEurekaHttpClient.java:87)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:828)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:125)
	at com.netflix.discovery.InstanceInfoReplicator$2.run(InstanceInfoReplicator.java:105)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 22 more

2025-07-07T19:23:24.088+01:00  WARN 49671 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on POST request for "http://localhost:8070/eureka/apps/LUCID-CBA": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T19:23:24.088+01:00  WARN 49671 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076 - registration failed Cannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:828) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:125) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.InstanceInfoReplicator$2.run(InstanceInfoReplicator.java:105) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T19:23:24.090+01:00  WARN 49671 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] c.n.discovery.InstanceInfoReplicator     : There was a problem with the instance info replicator

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:828) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:125) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.InstanceInfoReplicator$2.run(InstanceInfoReplicator.java:105) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T19:23:24.094+01:00  INFO 49671 --- [lucid-cba] [main] c.digicore.LucidCbaServiceApplication    : Started LucidCbaServiceApplication in 3.586 seconds (process running for 9.257)
2025-07-07T19:23:24.631+01:00  INFO 49671 --- [lucid-cba] [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T19:23:24.631+01:00  INFO 49671 --- [lucid-cba] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T19:23:24.632+01:00  INFO 49671 --- [lucid-cba] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-07T19:23:54.092+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076: registering service...
2025-07-07T19:23:54.083+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T19:23:54.096+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T19:23:54.096+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T19:23:54.096+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T19:23:54.097+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T19:23:54.097+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T19:23:54.097+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T19:23:55.097+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076 - registration status: 204
2025-07-07T19:23:55.099+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T19:23:55.239+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076 - Re-registering apps/LUCID-CBA
2025-07-07T19:23:55.239+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076: registering service...
2025-07-07T19:23:55.337+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076 - registration status: 204
2025-07-07T19:24:25.114+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T19:24:25.132+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T19:24:25.133+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T19:24:25.133+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T19:24:25.133+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T19:24:25.133+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application version is -1: false
2025-07-07T19:24:25.136+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T19:24:25.558+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T19:28:23.944+01:00  INFO 49671 --- [lucid-cba] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T19:29:13.440+01:00  INFO 49671 --- [lucid-cba] [http-nio-8076-exec-1] d.l.c.s.m.p.a.c.AccountServiceController : the request is : {responseStatus=null, narration=null, provider=null, serviceRequired=null, accountNumber=**********, pageNo=0, pageSize=0, startDate=2025-06-27, endDate=2025-07-07}
2025-07-07T19:29:13.441+01:00  INFO 49671 --- [lucid-cba] [http-nio-8076-exec-1] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< request : {responseStatus=null, narration=null, provider=null, serviceRequired=null, accountNumber=**********, pageNo=0, pageSize=0, startDate=2025-06-27, endDate=2025-07-07} >>>
2025-07-07T19:29:13.441+01:00  INFO 49671 --- [lucid-cba] [http-nio-8076-exec-1] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< provider : coronation >>>
2025-07-07T19:29:13.441+01:00  INFO 49671 --- [lucid-cba] [http-nio-8076-exec-1] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< serviceRequired : OrganizationAccountTransactions >>>
2025-07-07T19:29:23.719+01:00 ERROR 49671 --- [lucid-cba] [http-nio-8076-exec-1] .c.s.m.p.c.s.a.s.CoronationTokenProvider : auth error : Connect timed out executing POST https://api-fundstransfer-test.coronationmb.com:8080/auth
2025-07-07T19:30:34.758+01:00  INFO 49671 --- [lucid-cba] [http-nio-8076-exec-2] d.l.c.s.m.p.a.c.AccountServiceController : the request is : {responseStatus=null, narration=null, provider=null, serviceRequired=null, accountNumber=**********, pageNo=0, pageSize=0, startDate=2025-06-27, endDate=2025-07-07}
2025-07-07T19:30:34.760+01:00  INFO 49671 --- [lucid-cba] [http-nio-8076-exec-2] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< request : {responseStatus=null, narration=null, provider=null, serviceRequired=null, accountNumber=**********, pageNo=0, pageSize=0, startDate=2025-06-27, endDate=2025-07-07} >>>
2025-07-07T19:30:34.760+01:00  INFO 49671 --- [lucid-cba] [http-nio-8076-exec-2] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< provider : coronation >>>
2025-07-07T19:30:34.760+01:00  INFO 49671 --- [lucid-cba] [http-nio-8076-exec-2] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< serviceRequired : OrganizationAccountTransactions >>>
2025-07-07T19:30:35.664+01:00  INFO 49671 --- [lucid-cba] [http-nio-8076-exec-2] .c.s.m.p.c.s.a.s.CoronationTokenProvider : <<< auth response : {"flag":true,"code":"00","message":"Authentication Successful","data":{"accessToken":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************************************************.8ZLg81wwy6dHt1BM56nUpFFFEWQSkGmj_LpqKw1h6I6NAGaubxVnFCfv44KcnjEh0slAsZxkepJaUQd0f6QSBw","duration":3600}} >>>
2025-07-07T19:30:36.018+01:00  INFO 49671 --- [lucid-cba] [http-nio-8076-exec-2] onOrganizationAccountTransactionsService : <<< coronation account statement response : {"flag":true,"code":"00","message":"Account Statement Retrieved","data":[{"tranId":"011252","tranRemarks":"EUR TO NAIRA CONVERSION","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"CR","narration":"LOCAL TRANSFER","traBal":"3,520.00","traAmt":"3,520.00","pstdDate":"05-07-2025"},{"tranId":"011262","tranRemarks":"MTN_DATA_1/LIFJJHCPBOCF/199001","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"LOCAL TRANSFER","traBal":"2,020.00","traAmt":"1,500.00","pstdDate":"05-07-2025"},{"tranId":"011263","tranRemarks":"MTN_DATA_1/HHDUJKLDJHDKLDKJL/1","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"LOCAL TRANSFER","traBal":"520.00","traAmt":"1,500.00","pstdDate":"05-07-2025"},{"tranId":"011266","tranRemarks":"9MOBILE_AIRTIME_1/HHDUJKLDJHDG","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"LOCAL TRANSFER","traBal":"-1,480.00","traAmt":"2,000.00","pstdDate":"05-07-2025"},{"tranId":"011267","tranRemarks":"9MOBILE_AIRTIME_1/HHDUJKLDJHDG","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"LOCAL TRANSFER","traBal":"-3,480.00","traAmt":"2,000.00","pstdDate":"05-07-2025"},{"tranId":"011269","tranRemarks":"MTN_DATA_1/6LXUTHBHRJKE/199001","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/6LXUTHBHRJKE/**********","traBal":"-4,980.00","traAmt":"1,500.00","pstdDate":"05-07-2025"},{"tranId":"011280","tranRemarks":"AIRTEL_DATA_1/HHDUJKLDJHDGHHDD","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"AIRTEL_DATA_1/HHDUJKLDJHDGHHDDKLD;DL;DDJDKLDJHKDKLdkjl/**********","traBal":"-5,180.00","traAmt":"200.00","pstdDate":"05-07-2025"},{"tranId":"011281","tranRemarks":"GLO_AIRTIME_1/RLTOM2IMK2HD/199","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"GLO_AIRTIME_1/RLTOM2IMK2HD/**********","traBal":"-7,180.00","traAmt":"2,000.00","pstdDate":"05-07-2025"},{"tranId":"011283","tranRemarks":"MTN_DATA_1/DO4FONQULF9O/199001","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/DO4FONQULF9O/**********","traBal":"-8,680.00","traAmt":"1,500.00","pstdDate":"05-07-2025"},{"tranId":"011284","tranRemarks":"MTN_DATA_1/CL9T5HYW7NYF/199001","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/CL9T5HYW7NYF/**********","traBal":"-10,180.00","traAmt":"1,500.00","pstdDate":"05-07-2025"},{"tranId":"011285","tranRemarks":"MTN_DATA_1/ZENHQZ1YTDCZ/199001","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/ZENHQZ1YTDCZ/**********","traBal":"-11,680.00","traAmt":"1,500.00","pstdDate":"05-07-2025"},{"tranId":"011286","tranRemarks":"MTN_AIRTIME_1/X6IF4VTDP3EA/199","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"MTN_AIRTIME_1/X6IF4VTDP3EA/**********","traBal":"-13,680.00","traAmt":"2,000.00","pstdDate":"05-07-2025"},{"tranId":"011287","tranRemarks":"MTN_AIRTIME_1/MSAANXBQWUQS/199","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"MTN_AIRTIME_1/MSAANXBQWUQS/**********","traBal":"-13,700.00","traAmt":"20.00","pstdDate":"05-07-2025"},{"tranId":"011288","tranRemarks":"MTN_DATA_1/RCGXYXFWI3H8/199001","traDate":"05-07-2025","valDate":"05-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/RCGXYXFWI3H8/**********","traBal":"-15,200.00","traAmt":"1,500.00","pstdDate":"05-07-2025"},{"tranId":"011251","tranRemarks":"MTN_DATA_1/DCHK737I8HIE/199001","traDate":"06-07-2025","valDate":"06-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/DCHK737I8HIE/**********","traBal":"-16,700.00","traAmt":"1,500.00","pstdDate":"06-07-2025"},{"tranId":"011252","tranRemarks":"MTN_DATA_1/WIGWJ7BSU1OG/199001","traDate":"06-07-2025","valDate":"06-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/WIGWJ7BSU1OG/**********","traBal":"-18,200.00","traAmt":"1,500.00","pstdDate":"06-07-2025"},{"tranId":"011253","tranRemarks":"MTN_DATA_1/7CVZKSFVY6MN/199001","traDate":"06-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/7CVZKSFVY6MN/**********","traBal":"-19,700.00","traAmt":"1,500.00","pstdDate":"07-07-2025"},{"tranId":"011254","tranRemarks":"MTN_DATA_1/NM9OLKSNEVZE/199001","traDate":"06-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/NM9OLKSNEVZE/**********","traBal":"-21,200.00","traAmt":"1,500.00","pstdDate":"07-07-2025"},{"tranId":"011255","tranRemarks":"MTN_DATA_1/CMEZ85FWNKK8/199001","traDate":"06-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/CMEZ85FWNKK8/**********","traBal":"-22,700.00","traAmt":"1,500.00","pstdDate":"07-07-2025"},{"tranId":"011256","tranRemarks":"MTN_DATA_1/ZDGTI1XDIXH4/199001","traDate":"06-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/ZDGTI1XDIXH4/**********","traBal":"-24,200.00","traAmt":"1,500.00","pstdDate":"07-07-2025"},{"tranId":"011257","tranRemarks":"MTN_DATA_1/QJYYRUSRZSAQ/199001","traDate":"06-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/QJYYRUSRZSAQ/**********","traBal":"-25,700.00","traAmt":"1,500.00","pstdDate":"07-07-2025"},{"tranId":"011258","tranRemarks":"MTN_DATA_1/HAYULMR3RIBN/199001","traDate":"06-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/HAYULMR3RIBN/**********","traBal":"-27,200.00","traAmt":"1,500.00","pstdDate":"07-07-2025"},{"tranId":"011259","tranRemarks":"MTN_DATA_1/UPJNIPXDHTQG/199001","traDate":"06-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/UPJNIPXDHTQG/**********","traBal":"-28,700.00","traAmt":"1,500.00","pstdDate":"07-07-2025"},{"tranId":"011260","tranRemarks":"1800 NAIRA","traDate":"06-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"LOCAL TRANSFER","traBal":"-30,500.00","traAmt":"1,800.00","pstdDate":"07-07-2025"},{"tranId":"011261","tranRemarks":"2K NAIRA","traDate":"06-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"LOCAL TRANSFER","traBal":"-32,500.00","traAmt":"2,000.00","pstdDate":"07-07-2025"},{"tranId":"011249","tranRemarks":"2K NAIRA","traDate":"07-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"LOCAL TRANSFER","traBal":"-34,500.00","traAmt":"2,000.00","pstdDate":"07-07-2025"},{"tranId":"011250","tranRemarks":"9K8H","traDate":"07-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"LOCAL TRANSFER","traBal":"-44,300.00","traAmt":"9,800.00","pstdDate":"07-07-2025"},{"tranId":"011253","tranRemarks":"EUR TO NAIRA CONVERSION","traDate":"07-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"CR","narration":"LOCAL TRANSFER","traBal":"-40,780.00","traAmt":"3,520.00","pstdDate":"07-07-2025"},{"tranId":"011258","tranRemarks":"USD TO NAIRA CONVERSION","traDate":"07-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"CR","narration":"LOCAL TRANSFER","traBal":"-31,510.00","traAmt":"9,270.00","pstdDate":"07-07-2025"},{"tranId":"011261","tranRemarks":"XOA03XREI16F","traDate":"07-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/XOA03XREI16F/**********","traBal":"-33,010.00","traAmt":"1,500.00","pstdDate":"07-07-2025"},{"tranId":"011262","tranRemarks":"E75WMYIZPGGT","traDate":"07-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/E75WMYIZPGGT/**********","traBal":"-34,510.00","traAmt":"1,500.00","pstdDate":"07-07-2025"},{"tranId":"011263","tranRemarks":"VJSTYDYCDCEJ","traDate":"07-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_DATA_1/VJSTYDYCDCEJ/**********","traBal":"-36,010.00","traAmt":"1,500.00","pstdDate":"07-07-2025"},{"tranId":"011264","tranRemarks":"3QJVK19THSPJ","traDate":"07-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_AIRTIME_1/3QJVK19THSPJ/**********","traBal":"-36,300.00","traAmt":"290.00","pstdDate":"07-07-2025"},{"tranId":"011268","tranRemarks":"VIQWVUPSZVPL","traDate":"07-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_AIRTIME_1/VIQWVUPSZVPL/**********","traBal":"-37,300.00","traAmt":"1,000.00","pstdDate":"07-07-2025"},{"tranId":"011269","tranRemarks":"ZJVDEUYCEER2","traDate":"07-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_AIRTIME_1/ZJVDEUYCEER2/**********","traBal":"-38,300.00","traAmt":"1,000.00","pstdDate":"07-07-2025"},{"tranId":"011270","tranRemarks":"CJ6BERCHA0K5","traDate":"07-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"DR","narration":"MTN_AIRTIME_1/CJ6BERCHA0K5/**********","traBal":"-38,500.00","traAmt":"200.00","pstdDate":"07-07-2025"},{"tranId":"011280","tranRemarks":"USD TO NAIRA CONVERSION","traDate":"07-07-2025","valDate":"07-07-2025","currency":"NGN","traType":"CR","narration":"LOCAL TRANSFER","traBal":"-32,320.00","traAmt":"6,180.00","pstdDate":"07-07-2025"}],"currentPage":0,"size":37,"totalElements":37,"totalPages":1,"last":false} >>>
2025-07-07T19:36:40.952+01:00  INFO 49671 --- [lucid-cba] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T21:49:17.074+01:00  INFO 49671 --- [lucid-cba] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-CBA with eureka with status DOWN
2025-07-07T21:49:17.116+01:00  INFO 49671 --- [lucid-cba] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751921357115, current=DOWN, previous=UP]
2025-07-07T21:49:17.124+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076: registering service...
2025-07-07T21:49:17.582+01:00  INFO 49671 --- [lucid-cba] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T21:49:17.593+01:00  INFO 49671 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076 - registration status: 204
2025-07-07T21:49:17.661+01:00  INFO 49671 --- [lucid-cba] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-07T21:49:17.867+01:00  INFO 49671 --- [lucid-cba] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T21:49:20.876+01:00  INFO 49671 --- [lucid-cba] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T21:49:20.900+01:00  INFO 49671 --- [lucid-cba] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/} exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CBA/*************:lucid-cba:8076": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CBA/*************:lucid-cba:8076": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:91)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 37 more

2025-07-07T21:49:20.901+01:00  WARN 49671 --- [lucid-cba] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CBA/*************:lucid-cba:8076": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T21:49:20.939+01:00  INFO 49671 --- [lucid-cba] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CBA/*************:lucid-cba:8076": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CBA/*************:lucid-cba:8076": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 38 more

2025-07-07T21:49:20.940+01:00  WARN 49671 --- [lucid-cba] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-CBA/*************:lucid-cba:8076": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T21:49:20.940+01:00 ERROR 49671 --- [lucid-cba] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:686) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:643) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:602) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-3.4.2.jar:3.4.2]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.2.jar:6.2.2]
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147) ~[spring-boot-3.4.2.jar:3.4.2]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116) ~[spring-boot-3.4.2.jar:3.4.2]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T21:49:20.943+01:00  INFO 49671 --- [lucid-cba] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T21:59:04.755+01:00  INFO 54781 --- [lucid-cba] [main] c.digicore.LucidCbaServiceApplication    : Starting LucidCbaServiceApplication using Java 21.0.1 with PID 54781 (/Users/<USER>/Desktop/LUCID/lucid-cba-service/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T21:59:04.756+01:00  INFO 54781 --- [lucid-cba] [main] c.digicore.LucidCbaServiceApplication    : The following 5 profiles are active: "dev", "messages", "enterprise", "coronation-provider", "coronation"
2025-07-07T21:59:04.776+01:00  INFO 54781 --- [lucid-cba] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T21:59:04.776+01:00  INFO 54781 --- [lucid-cba] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-cba, profiles=[default], label=null, version=null, state=null
2025-07-07T21:59:04.776+01:00  INFO 54781 --- [lucid-cba] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T21:59:04.776+01:00  INFO 54781 --- [lucid-cba] [main] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-cba, profiles=[dev,messages,enterprise,coronation-provider,coronation], label=null, version=null, state=null
2025-07-07T21:59:05.523+01:00  INFO 54781 --- [lucid-cba] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=a599e8bf-0c1b-3f85-8fed-af668f5b5cd7
2025-07-07T21:59:05.718+01:00  INFO 54781 --- [lucid-cba] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8076 (http)
2025-07-07T21:59:05.724+01:00  INFO 54781 --- [lucid-cba] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-07T21:59:05.725+01:00  INFO 54781 --- [lucid-cba] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-07-07T21:59:05.761+01:00  INFO 54781 --- [lucid-cba] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-07T21:59:05.762+01:00  INFO 54781 --- [lucid-cba] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 984 ms
2025-07-07T21:59:06.455+01:00  INFO 54781 --- [lucid-cba] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T21:59:06.706+01:00  WARN 54781 --- [lucid-cba] [main] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T21:59:06.782+01:00  INFO 54781 --- [lucid-cba] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T21:59:06.843+01:00  INFO 54781 --- [lucid-cba] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T21:59:06.853+01:00  INFO 54781 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T21:59:06.854+01:00  INFO 54781 --- [lucid-cba] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T21:59:06.858+01:00  INFO 54781 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T21:59:06.858+01:00  INFO 54781 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T21:59:06.858+01:00  INFO 54781 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T21:59:06.858+01:00  INFO 54781 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T21:59:06.858+01:00  INFO 54781 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T21:59:06.858+01:00  INFO 54781 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T21:59:06.858+01:00  INFO 54781 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T21:59:06.998+01:00  INFO 54781 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T21:59:06.999+01:00  INFO 54781 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T21:59:07.000+01:00  INFO 54781 --- [lucid-cba] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T21:59:07.000+01:00  INFO 54781 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751921947000 with initial instances count: 0
2025-07-07T21:59:07.004+01:00  INFO 54781 --- [lucid-cba] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-CBA with eureka with status UP
2025-07-07T21:59:07.005+01:00  INFO 54781 --- [lucid-cba] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751921947005, current=UP, previous=STARTING]
2025-07-07T21:59:07.005+01:00  INFO 54781 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076: registering service...
2025-07-07T21:59:07.017+01:00  INFO 54781 --- [lucid-cba] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8076 (http) with context path '/'
2025-07-07T21:59:07.018+01:00  INFO 54781 --- [lucid-cba] [main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8076
2025-07-07T21:59:07.027+01:00  INFO 54781 --- [lucid-cba] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-CBA/*************:lucid-cba:8076 - registration status: 204
2025-07-07T21:59:07.028+01:00  INFO 54781 --- [lucid-cba] [main] c.digicore.LucidCbaServiceApplication    : Started LucidCbaServiceApplication in 2.809 seconds (process running for 8.81)
2025-07-07T21:59:07.402+01:00  INFO 54781 --- [lucid-cba] [RMI TCP Connection(3)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07T21:59:07.403+01:00  INFO 54781 --- [lucid-cba] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-07T21:59:07.404+01:00  INFO 54781 --- [lucid-cba] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-07T21:59:37.005+01:00  INFO 54781 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T21:59:37.006+01:00  INFO 54781 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T21:59:37.006+01:00  INFO 54781 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T21:59:37.006+01:00  INFO 54781 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T21:59:37.006+01:00  INFO 54781 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T21:59:37.006+01:00  INFO 54781 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application version is -1: false
2025-07-07T21:59:37.006+01:00  INFO 54781 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T21:59:37.037+01:00  INFO 54781 --- [lucid-cba] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T22:03:20.647+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-2] d.l.c.s.m.p.a.c.AccountServiceController : the request is : {responseStatus=null, narration=null, provider=null, serviceRequired=null, accountNumber=**********, customerId=null, fetchAllAccounts=false}
2025-07-07T22:03:20.650+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-2] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< request : {responseStatus=null, narration=null, provider=null, serviceRequired=null, accountNumber=**********, customerId=null, fetchAllAccounts=false} >>>
2025-07-07T22:03:20.650+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-2] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< provider : coronation >>>
2025-07-07T22:03:20.650+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-2] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< serviceRequired : OrganizationAccountDetail >>>
2025-07-07T22:03:21.803+01:00 ERROR 54781 --- [lucid-cba] [http-nio-8076-exec-2] .c.s.m.p.c.s.a.s.CoronationTokenProvider : auth error : api-fundstransfer-test.coronationmb.com executing POST https://api-fundstransfer-test.coronationmb.com:8080/auth
2025-07-07T22:04:06.854+01:00  INFO 54781 --- [lucid-cba] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T22:05:40.027+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-1] d.l.c.s.m.p.a.c.AccountServiceController : the request is : {responseStatus=null, narration=null, provider=null, serviceRequired=null, accountNumber=**********, customerId=null, fetchAllAccounts=false}
2025-07-07T22:05:40.031+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-1] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< request : {responseStatus=null, narration=null, provider=null, serviceRequired=null, accountNumber=**********, customerId=null, fetchAllAccounts=false} >>>
2025-07-07T22:05:40.031+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-1] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< provider : coronation >>>
2025-07-07T22:05:40.031+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-1] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< serviceRequired : OrganizationAccountDetail >>>
2025-07-07T22:05:40.900+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-1] .c.s.m.p.c.s.a.s.CoronationTokenProvider : <<< auth response : {"flag":true,"code":"00","message":"Authentication Successful","data":{"accessToken":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************************************************.K9vH7cOe3m4rkn5uukcEJGvUcp0Vlg8sgFFQasJXNNcK5fT1SBavTUqHxa0iapWjnvdAdq6eRiB-E8ID9hnMCw","duration":3600}} >>>
2025-07-07T22:05:41.505+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-1] nOrganizationFetchCustomerAccountService : <<< coronation account response : {"flag":true,"code":"00","message":"SUCCESS","data":{"responseText":"SUCCESS","responseCode":"00","accountNumber":"**********","accountCurrency":"NGN","accountName":"OLORUNFEMI J OMONAIYE","status":"A","balance":1007020,"restriction":"N","cifId":"R006554","accountSchemeCode":"101","misCode":"GB31A","effectiveBalance":1007020,"phoneNumber":"+234(817)7150125","email":"<EMAIL>","tin":"***********","rcNumber":"RC3752738","bvn":null,"address":"7 MORENIKEJI STREET ALADE IKEJA LAGOS","branchCode":"999","bankId":"01","schemeType":"SBA","customerDateOfBirth":"1984-09-14 00:00:00.0","salutation":"MR","tier":"TER2"}} >>>
2025-07-07T22:08:07.894+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-4] d.l.c.s.m.p.a.c.AccountServiceController : the request is : {responseStatus=null, narration=null, provider=null, serviceRequired=null, accountNumber=**********, customerId=null, fetchAllAccounts=false}
2025-07-07T22:08:07.897+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-4] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< request : {responseStatus=null, narration=null, provider=null, serviceRequired=null, accountNumber=**********, customerId=null, fetchAllAccounts=false} >>>
2025-07-07T22:08:07.898+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-4] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< provider : coronation >>>
2025-07-07T22:08:07.898+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-4] c.d.l.c.s.m.p.a.r.AccountServiceRouter   : <<< serviceRequired : OrganizationCustomerDetail >>>
2025-07-07T22:09:06.808+01:00  INFO 54781 --- [lucid-cba] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T22:09:07.977+01:00  INFO 54781 --- [lucid-cba] [http-nio-8076-exec-4] ronationOrganizationFetchCustomerService : coronation account error : Read timed out executing GET https://api-fundstransfer-test.coronationmb.com:8080/accounts/**********
