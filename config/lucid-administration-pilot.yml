lucid:
  database:
    url: "*********************************************************************************"
    username: "<PERSON><PERSON><PERSON>"
    password: "<PERSON>gicore_123"
    driver: "com.mysql.cj.jdbc.Driver"
  security:
    jwtKeyStorePath: "/home//ubuntu//lucid//services//config-server//config//keys//keystore.jks"
    jwtKeyStorePassword: "password"
    jwtKeyAlias: "jwtsigning"
    jwtPrivateKeyPassphrase: "password"
    systemDefinedPermissions: "/home//ubuntu//lucid//services//config-server//config//permissions//administration//systemPermissions.json"
    systemDefinedLimits: "/home//ubuntu//lucid//services//config-server//config//limits//systemLimits.json"
    corsAllowedOrigins: "*"
    corsAllowedMethods:
      - POST
      - GET
      - PUT
      - DELETE
      - PATCH
      - HEAD
    corsAllowedHeaders:
      - Content-Type
      - Access-Control-Allow-Headers
      - Access-Control-Allow-Origin
      - Access-Control-Expose-Headers
      - Content-Disposition
      - Authorization
      - Access-Control-Allow-Methods
      - X-Requested-With
    corsAllowedExposedHeaders:
      - Content-Disposition
    allowedUrls:
      - /lucid-administration/documentation/**
      - /actuator/**
      - /api/v1/authentication/process/**
    platform: "ADMIN"
    filteredUrls:
      - /actuator/**
      - /lucid-administration/documentation/**
    allowedClientUrls:
      - unity.lucid.local:8073
      - lucid.local:8073
    redisHost: localhost
    redisPort: 6379
  swagger:
    deployedServerUrl: "https://lucid-gateway-service.digicoreltds.com/lucid-administration/"
  file:
    fileUploadDirectory: "/home//ubuntu//files"
    minFileUploadSize: 1000
    maxFileUploadSize: 5000000
    s3BucketName: lucid-staging-bucket
    s3AccessKey: test
    s3SecretKey: test
    s3Region: us-east-2

