<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Transaction Receipt</title>
    <style>

      html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        font-family: 'Verdana', 'Arial', Helvetica, sans-serif;
        background: #fff;
        color: #121212;
      }

      * {
        box-sizing: border-box;
      }

      @page {
        size: 21.59cm 27.94cm; /* US Letter Portrait */
        margin: 1cm;
      }

      @media print {
        html, body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
      }

      .receipt-wrapper {
        max-width: 800px;
        margin: auto;
        padding: 2rem;
        background: #ffffff;
        border-radius: 12px;
      }

      .receipt-header {
        text-align: center;
        margin-bottom: 1.5rem;
      }

      .receipt-header img {
        height: 50px;
        margin-bottom: 0.5rem;
      }

      .receipt-header h3 {
        font-size: 24px;
        font-weight: 600;
        margin: 0;
      }

      .amount-section {
        text-align: center;
        margin-bottom: 2rem;
      }

      .amount-section .amount {
        color: #888;
      }

      .amount-section h1 {
        font-size: 42px;
        font-weight: bold;
        margin: 0.25rem 0;
      }

      .amount-section .status {
        display: inline-block;
        padding: 8px 18px;
        border-radius: 24px;
        font-size: 14px;
        font-weight: 500;
        text-transform: capitalize;
        background-color: #e8f5e9;
        color: #2e7d32;
      }

      .receipt-table {
        width: 100%;
        border-collapse: collapse;
        background: #f9f9f9;
        border-radius: 8px;
        overflow: hidden;
        font-size: 16px;
        margin-bottom: 40px;
      }

      .receipt-table td {
        padding: 24px 20px;
        border-bottom: 1px solid #e0e0e0;
      }

      .receipt-table tr:last-child td {
        border-bottom: none;
      }

      .receipt-table td:first-child {
        font-weight: 500;
        font-size: 14px;
        color: #888;
      }

      .receipt-table td:last-child {
        font-weight: 600;
      }

      .footer-note {
        margin-top: 2rem;
        text-align: left;
        font-size: 12px;
        color: #888;
      }
    </style>
  </head>
  <body>
    <div class="receipt-wrapper">
      <div class="receipt-header">
        <img th:src="${logoUrl}" alt="Company Logo" />
        <h3>Transaction Receipt</h3>
      </div>

      <div class="amount-section">
        <div class="amount">Amount</div>
        <h1 th:text="${amount}">₦1,000,000</h1>
        <div
          class="status"
          th:text="${status}"
          th:if="${status != 'SUCCESS'}"
          style="background-color: #fdecea; color: #c62828;"
        >
          Failed
        </div>
        <div
          class="status"
          th:text="${status}"
          th:if="${status == 'SUCCESS'}"
          style="background-color: #e8f5e9; color: #2e7d32;"
        >
          Successful
        </div>
      </div>

      <table class="receipt-table">
        <tr>
          <td>Sender:</td>
          <td th:text="${senderName}">Oloto Stephen</td>
        </tr>
        <tr>
          <td>Sender Account Number:</td>
          <td th:text="${senderAccountNumber}">Oloto Stephen</td>
        </tr>
        <tr>
          <td>Receiving Bank:</td>
          <td th:text="${beneficiaryBankName}">Access Bank</td>
        </tr>
        <tr>
          <td>Beneficiary Name:</td>
          <td th:text="${beneficiaryName}">John Doe</td>
        </tr>
        <tr>
          <td>Beneficiary Account Number:</td>
          <td th:text="${beneficiaryAccountNumber}">932*****792</td>
        </tr>
        <tr>
          <td>Transaction Ref:</td>
          <td th:text="${transactionRef}">QWERTYUIOPASD</td>
        </tr>
        <tr>
          <td>Transaction Date:</td>
          <td th:text="${date}">May 7, 2025, 15:26:10</td>
        </tr>
        <tr>
          <td>Narration:</td>
          <td th:text="${narration}">Payment for goods</td>
        </tr>
      </table>

      <div style="border-top: 1px solid#e0e0e0;"></div>

      <div class="footer-note">
        Your transfer has been successful and the beneficiary’s account will be credited. However, this does not serve as confirmation of credit into the beneficiary’s account. Due to the nature of the internet, transactions may be subject to interruption, transmission blackout, delayed transmission and incorrect data transmission. The Bank is not liable for malfunctions in communications facilities not within its control that may affect the accuracy or timeliness of messages and transactions you send. All transactions are subject to verification and our normal fraud checks.
        <br/>
        This is a system-generated receipt and does not require a signature.
      </div>
    </div>
  </body>
</html>
