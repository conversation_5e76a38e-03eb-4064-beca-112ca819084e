[{"limitType": "INTERBANK_TRANSFER_LIMIT", "currency": "NGN", "minorMobileSingleCap": "200000", "minorWebSingleCap": "500000", "minorMobileCumulativeCap": "1000000", "minorWebCumulativeCap": "5000000", "minorMobileSingleCappedMessage": "Max single transaction limit reached for mobile (NGN)", "minorWebSingleCappedMessage": "Max single transaction limit reached for web (NGN)", "minorMobileCumulativeCappedMessage": "Max daily limit reached for mobile (NGN)", "minorWebCumulativeCappedMessage": "Max daily limit reached for web (NGN)", "defaultLimit": false}, {"limitType": "INTERBANK_TRANSFER_LIMIT", "currency": "NGN", "minorMobileSingleCap": "100000", "minorWebSingleCap": "300000", "minorMobileCumulativeCap": "500000", "minorWebCumulativeCap": "1000000", "minorMobileSingleCappedMessage": "Max single transaction limit reached for mobile (NGN)", "minorWebSingleCappedMessage": "Max single transaction limit reached for web (NGN)", "minorMobileCumulativeCappedMessage": "Max daily limit reached for mobile (NGN)", "minorWebCumulativeCappedMessage": "Max daily limit reached for web (NGN)", "defaultLimit": true}, {"limitType": "INTRABANK_TRANSFER_LIMIT", "currency": "NGN", "minorMobileSingleCap": "250000", "minorWebSingleCap": "600000", "minorMobileCumulativeCap": "1200000", "minorWebCumulativeCap": "6000000", "minorMobileSingleCappedMessage": "Max single transaction limit reached for mobile (NGN)", "minorWebSingleCappedMessage": "Max single transaction limit reached for web (NGN)", "minorMobileCumulativeCappedMessage": "Max daily limit reached for mobile (NGN)", "minorWebCumulativeCappedMessage": "Max daily limit reached for web (NGN)", "defaultLimit": true}, {"limitType": "INTRABANK_TRANSFER_LIMIT", "currency": "NGN", "minorMobileSingleCap": "300000", "minorWebSingleCap": "700000", "minorMobileCumulativeCap": "1200000", "minorWebCumulativeCap": "********", "minorMobileSingleCappedMessage": "Max single transaction limit reached for mobile (NGN)", "minorWebSingleCappedMessage": "Max single transaction limit reached for web (NGN)", "minorMobileCumulativeCappedMessage": "Max daily limit reached for mobile (NGN)", "minorWebCumulativeCappedMessage": "Max daily limit reached for web (NGN)", "defaultLimit": false}, {"limitType": "BACKOFFICE_ONBOARDING_LIMIT", "currency": "NGN", "minorMobileSingleCap": "300000", "minorWebSingleCap": "700000", "minorMobileCumulativeCap": "1200000", "minorWebCumulativeCap": "********", "minorMobileSingleCappedMessage": "Max single transaction limit reached for mobile (NGN)", "minorWebSingleCappedMessage": "Max single transaction limit reached for web (NGN)", "minorMobileCumulativeCappedMessage": "Max daily limit reached for mobile (NGN)", "minorWebCumulativeCappedMessage": "Max daily limit reached for web (NGN)", "defaultLimit": true}, {"limitType": "BULK_TRANSFER_LIMIT", "currency": "NGN", "minorMobileSingleCap": "300000", "minorWebSingleCap": "700000", "minorMobileCumulativeCap": "1200000", "minorWebCumulativeCap": "********", "minorMobileSingleCappedMessage": "Max single transaction limit reached for mobile (NGN)", "minorWebSingleCappedMessage": "Max single transaction limit reached for web (NGN)", "minorMobileCumulativeCappedMessage": "Max daily limit reached for mobile (NGN)", "minorWebCumulativeCappedMessage": "Max daily limit reached for web (NGN)", "defaultLimit": true}]