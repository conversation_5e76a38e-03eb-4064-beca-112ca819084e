lucid:
  enterprise:
    organizationName: "coronation"
    organizationEmail: "<EMAIL>"
    organizationPhoneNumber: "01101010101"
    subDomain: "coronation"
    subDomainName: "coronation"
    cbaProvider: "coronation"
    makerUsername: "<EMAIL>"
    makerEmail: "<EMAIL>"
    makerPhoneNumber: "01101010101"
    checkerUsername: "<EMAIL>"
    checkerEmail: "<EMAIL>"
    checkerPhoneNumber: "01101010102"
    enterpriseMode: true
    enterpriseModeSetupCompleted: true
    productCodes: "test, test"
    platform: "retail"
  database:
    url: "************************************************************************************"
    username: "Digicore"
    password: "Digicore_123"
    driver: "com.mysql.cj.jdbc.Driver"
  security:
    jwtKeyStorePath: "/Users//ikechi//Desktop//LUCID//lucid-config-server//config//keys//keystore.jks"
    jwtKeyStorePassword: "password"
    jwtKeyAlias: "jwtsigning"
    jwtPrivateKeyPassphrase: "password"
    systemKey: "LD8EH2OF34WL9I8A"
    systemDefinedPermissions: "/Users//ikechi//Desktop//LUCID//lucid-config-server//config//permissions//customer//systemPermissions.json"
    corsAllowedOrigins: "*"
    corsAllowedMethods:
      - POST
      - GET
      - PUT
      - DELETE
      - PATCH
      - HEAD
    corsAllowedHeaders:
      - Content-Type
      - Access-Control-Allow-Headers
      - Access-Control-Allow-Origin
      - Access-Control-Expose-Headers
      - Content-Disposition
      - Authorization
      - Access-Control-Allow-Methods
      - X-Requested-With
    corsAllowedExposedHeaders:
      - Content-Disposition
    allowedUrls:
      - /lucid-customer/documentation/**
      - /actuator/**
      - /api/v1/authentication/process/**
      - /api/v1/onboarding/process/**
      - /api/v1/activation/process/send-bvn-verification-code
      - /api/v1/activation/process/validate-bvn
      - /api/v1/activation/process/signatory/retrieve
      - /api/v1/activation/process/signatory/update
      - /api/v1/location/process/retrieve-all
    platform: "CUSTOMER"
    filteredUrls:
      - /actuator/**
      - /lucid-customer/documentation/**
      - /api/v1/onboarding/process/**
      - /api/v1/activation/process/send-bvn-verification-code
      - /api/v1/activation/process/validate-bvn
      - /api/v1/activation/process/signatory/retrieve
      - /api/v1/activation/process/signatory/update
      - /api/v1/activation/process/preference/retrieve
      - /api/v1/customer/**
      - /api/v1/authentication/process/**
    allowedClientUrls:
      - unity.lucid.local:8073
      - lucid.local:8073
    redisHost: localhost
    redisPort: 6379
    skipBvnOtpValidation: true
    skipSmsValidation: true
  feature:
    modules:
      - name: ROLE
        activities:
          - CREATE
          - EDIT
          - DELETE
          - ENABLE
          - DISABLE
      - name: USER
        activities:
          - CREATE
          - EDIT
          - DELETE
          - ENABLE
          - DISABLE
          - ONBOARD
      - name: APPROVAL_RULE
        activities:
          - CREATE
          - EDIT
    logo: ""
    primaryColor: "#5026fb"
    secondaryColor: "#eceeff"
  swagger:
    developmentServerUrl: "http://unity-corporate.local:8073/lucid-customer/"
  file:
    fileUploadDirectory: "/Users//ikechi//Desktop//LUCID//"
    minFileUploadSize: 1000
    maxFileUploadSize: 5000000
    s3BucketName: lucid-staging-bucket
    s3AccessKey: test
    s3SecretKey: test
    s3Url: http://localhost:4566
  template:
    account:
      account_template_path: "/Users//ikechi//Desktop//LUCID//lucid-config-server//config//templates//customer//"
      account_statement: "accountStatement"
      receipt: "accountReceipt"
  misc:
    locationsPath: "/Users//ikechi//Desktop//LUCID//lucid-config-server//config//locations//nigeria-states-and-cities.json"
    onboardingPath: "/auth/onboarding/signatory-account?organizationId=%s&email=%s"


