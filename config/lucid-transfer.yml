spring:
  #  application:
  #    name: "{cipher}af01efab3155ed3873be63dece96cd79cd73a189fe9ff335a14a7d57ed750f4d6e0532c286abcdf2dc55e973e13acf6f"
  jpa:
    hibernate:
      ddl-auto: "update"
    show-sql: "false"
    generate-ddl: "true"


eureka:
  instance:
    preferIpAddress: true
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: "http://localhost:8070/eureka/"

info:
  app:
    name: "lucid-customer-transfer-service"
    description: "Customer Transfer Service"
    version: "1.0.0-SNAPSHOT"

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    shutdown:
      enabled: true
  info:
    env:
      enabled: true

endpoints:
  shutdown:
    enabled: true

springdoc:
  swagger-ui:
    path: "/lucid-transfer/documentation/doc-ui.html"
    url: "/lucid-transfer/documentation/v3/api-docs"
    disable-swagger-default-url: true
    configUrl: "/lucid-transfer/documentation/v3/api-docs/swagger-config"
    operationsSorter: "alpha"
    tagsSorter: "alpha"
  api-docs:
    path: "/lucid-transfer/documentation/v3/api-docs"

lucid:
  swagger:
    projectTitle: "Lucid Customer Transfer Service"
    projectVersion: "${info.app.version}"
  #    projectDescription: "Lucid Administration Service"
  processors:
    enabled: true
logging:
  file:
    name: lucid-customer-transfer-service.log