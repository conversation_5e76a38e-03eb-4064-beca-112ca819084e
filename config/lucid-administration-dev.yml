lucid:
  database:
    url: "*********************************************************************************"
    username: "<PERSON><PERSON><PERSON>"
    password: "Digicore_123"
    driver: "com.mysql.cj.jdbc.Driver"
  security:
    jwtKeyStorePath: "/Users//togunwuyi//Documents//digicore-projects//lucid-projects//configuration//lucid-config-server//config//keys//keystore.jks"
    jwtKeyStorePassword: "password"
    jwtKeyAlias: "jwtsigning"
    jwtPrivateKeyPassphrase: "password"
    systemKey: "LD8EH2OF34WL9I8A"
    systemDefinedPermissions: "/Users//togunwuyi//Documents//digicore-projects//lucid-projects//configuration//lucid-config-server//config//permissions//administration//systemPermissions.json"
    systemDefinedLimits: "/Users//togunwuyi//Documents//digicore-projects//lucid-projects//configuration//lucid-config-server//config//limits//systemLimits.json"
    corsAllowedOrigins: "*"
    corsAllowedMethods:
      - POST
      - GET
      - PUT
      - DELETE
      - PATCH
      - HEAD
    corsAllowedHeaders:
      - Content-Type
      - Access-Control-Allow-Headers
      - Access-Control-Allow-Origin
      - Access-Control-Expose-Headers
      - Content-Disposition
      - Authorization
      - Access-Control-Allow-Methods
      - X-Requested-With
    corsAllowedExposedHeaders:
      - Content-Disposition
    allowedUrls:
      - /lucid-administration/documentation/**
      - /actuator/**
      - /api/v1/authentication/process/**
    platform: "ADMIN"
    filteredUrls:
      - /actuator/**
      - /lucid-administration/documentation/**
    allowedClientUrls:
      - unity.lucid.local:8073
      - lucid.local:8073
    redisHost: localhost
    redisPort: 6379
  swagger:
    developmentServerUrl: "http://unity-admin.local:8073/lucid-administration/"
  file:
    fileUploadDirectory: "/Users//togunwuyi//Documents//digicore-projects//"
    minFileUploadSize: 1
    maxFileUploadSize: 5000000
    s3BucketName: lucid-staging-bucket
    s3AccessKey: test
    s3SecretKey: test
    s3Url: http://localhost:4566

