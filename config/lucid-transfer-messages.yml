lucid:
 message:
  user:
   not_found: "{email} does not exist"
   created: "User created successfully"
   duplicate: "{email} already exists"
  role:
    not_found: "{roleName} does not exist"
    created: "Role created successfully"
    duplicate: "{roleN<PERSON>} already exists"
    conflict: "You can't assign a checker and a maker permission under one role"
    invalid: "Invalid role supplied"
    invalid_permissions: "The following permissions are invalid: {permissions}"
  login:
    login_successful: "{user} logged in successfully"
    login_failed: "Invalid login credentials"
    login_denied: "We detected multiple failed attempt on login, check back in {time} minutes or contact support for help."
    login_denied_description: "{user} login attempt failed {time} times"
    inactive: "Account is inactive"
    password_already_used: "Password has already been previously used"
    old_password_wrong: "The old password provided is wrong"
    system_default_password_cant_be_updated: "Default system users password can't be updated"
    password_updated: "{user} reset password successfully"
  audit:
    create: "A create {module} request has been initiated by {user}"
    onboard: "An onboarding {module} request has been initiated by {user}"
    edit: "An edit {module} request has been initiated by {user}"
    delete: "A delete {module} request has been initiated by {user}"
    approve: "A {action} {module} request has been approved by {user}"
    decline: "A {action} {module} request has been declined by {user}"
    enable: "An enable {module} request has been initiated by {user}"
    disable: "A disable {module} request has been initiated by {user}"
    password_updated: "Password updated successfully by {user}"
    single: "A single transfer request was initiated by {user} and completed by {user}"
  email:
    login_successful_subject: "Login successful"
    onboarding_subject: "Onboarding"
    password_reset_successful_subject: "Password Reset Successful"
    password_reset_request_subject: "Password Reset Request"
  client:
    not_found: "Invalid client"
    duplicate: "{name} already exists"
  approval:
    not_authorized: "You are not authorized to perform this action"
    not_found: "Invalid activity type supplied"
    not_configured: "Workflow not configured"
    level_not_configured: "Approval leg not configured"
    invalid_type: "Invalid approval type"
    already_treated: "User has already approved this request at a previous stage."
  activation:
    not_found: "Invalid organization id supplied"
    required_fields_missing: "Required fields are missing"
  account:
    not_found: "Account not found for this user"
    inactive: "Account is inactive"
    no_access: "User does not have access to this account."
  transfer:
    not_found: "No transaction found"
    same_account: "Sender account number and receiver account number can't be same"
    not_access: "You does not have access to this account."
    transaction_code_subject: "Authenticate transaction"
    name_enquiry_failed: "Name enquiry failed"
    identifier_required: "The below account numbers requires an identifier"
  limit:
    no_profile_limit: "No profile limit found"
    limit_violation: "You have violated limit rules."

