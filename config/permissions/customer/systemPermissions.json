[{"name": "view-dashboard", "description": "This allows you to view dashboard modules and metrics", "permissionType": "DASHBOARD"}, {"name": "view-transactions", "description": "This allows you to view transactions", "permissionType": "TRANSACTION"}, {"name": "view-transaction-details", "description": "This allows you to view a transaction details", "permissionType": "TRANSACTION"}, {"name": "export-transactions", "description": "This allows you to export  transactions", "permissionType": "TRANSACTION"}, {"name": "resend-invite-email", "description": "This allows you to resend an invite email", "permissionType": "USER"}, {"name": "create-customer-role", "description": "This allows you to create new customer roles", "permissionType": "ROLE"}, {"name": "approve-create-customer-role", "description": "This allows you to approve create new customer roles requests", "permissionType": "ROLE"}, {"name": "view-customer-permissions", "description": "This allows you to view customer available permissions", "permissionType": "ROLE"}, {"name": "view-customer-roles", "description": "This allows you to view customer available roles", "permissionType": "ROLE"}, {"name": "delete-customer-role", "description": "This allows you to delete a customer role", "permissionType": "ROLE"}, {"name": "approve-delete-customer-role", "description": "This allows you to approve a request to delete a customer role", "permissionType": "ROLE"}, {"name": "edit-customer-role", "description": "This allows you to edit a customer role", "permissionType": "ROLE"}, {"name": "approve-edit-customer-role", "description": "This allows you to approve a request to edit a customer role", "permissionType": "ROLE"}, {"name": "treat-requests", "description": "This allows you to treat a request you have been assigned to", "permissionType": "ROLE"}, {"name": "disable-customer-role", "description": "This allows you to disable a customer role", "permissionType": "ROLE"}, {"name": "approve-disable-customer-role", "description": "This allows you to approve a request to disable a customer role", "permissionType": "ROLE"}, {"name": "enable-customer-role", "description": "This allows you to enable a customer role", "permissionType": "ROLE"}, {"name": "approve-enable-customer-role", "description": "This allows you to approve a request to enable a customer role", "permissionType": "ROLE"}, {"name": "view-customer-role-details", "description": "This allows you to view a customer role detail", "permissionType": "ROLE"}, {"name": "view-self-audit-trails", "description": "This allows you to view only your audit trails", "permissionType": "AUDIT_TRAIL"}, {"name": "view-all-audit-trails", "description": "This allows you to view all audit trails", "permissionType": "AUDIT_TRAIL"}, {"name": "view-self-user-details", "description": "This allows you to view your user details", "permissionType": "USER"}, {"name": "approve-create-approval-rule", "description": "This allows you to approve create approval rule request", "permissionType": "APPROVAL_RULE"}, {"name": "create-approval-rule", "description": "This allows you to create approval rule", "permissionType": "APPROVAL_RULE"}, {"name": "approve-edit-approval-rule", "description": "This allows you to approve edit approval rule request", "permissionType": "APPROVAL_RULE"}, {"name": "edit-approval-rule", "description": "This allows you to edit approval rule", "permissionType": "APPROVAL_RULE"}, {"name": "view-activity-types", "description": "This allows you to view activity types", "permissionType": "APPROVAL_RULE"}, {"name": "view-approval-rule", "description": "This allows you to view approval rules", "permissionType": "APPROVAL_RULE"}, {"name": "view-customer-limit", "description": "This allows you to view customer limit configured", "permissionType": "LIMIT"}, {"name": "edit-customer-limit", "description": "This allows you to edit customer limit configured", "permissionType": "LIMIT"}, {"name": "approve-edit-customer-limit", "description": "This allows you to approve edit customer limit request configured", "permissionType": "LIMIT"}, {"name": "view-customer-account", "description": "This allows you to view customer accounts", "permissionType": "ACCOUNT"}, {"name": "single-transfer", "description": "This allows you to initiate single transfer request", "permissionType": "TRANSFER"}, {"name": "approve-single-transfer", "description": "This allows you to approve single  transfer request", "permissionType": "TRANSFER"}, {"name": "bulk-transfer", "description": "This allows you to initiate bulk transfer request", "permissionType": "TRANSFER"}, {"name": "approve-bulk-transfer", "description": "This allows you to approve bulk transfer request", "permissionType": "TRANSFER"}, {"name": "view-customer-users", "description": "This allows you to view a customer users", "permissionType": "USER"}, {"name": "view-customer-user-detail", "description": "This allows you to view a customer user details", "permissionType": "USER"}, {"name": "edit-customer-user-detail", "description": "This allows you to edit a customer user detail", "permissionType": "USER"}, {"name": "approve-edit-customer-user-detail", "description": "This allows you to approve edit customer user request", "permissionType": "USER"}, {"name": "enable-customer-user", "description": "This allows you to enable a customer user", "permissionType": "USER"}, {"name": "approve-enable-customer-user", "description": "This allows you to approve enable customer user request", "permissionType": "USER"}, {"name": "disable-customer-user", "description": "This allows you to disable a customer user", "permissionType": "USER"}, {"name": "approve-disable-customer-user", "description": "This allows you to approve disable customer user request", "permissionType": "USER"}, {"name": "create-customer-beneficiary", "description": "This allows you to create a customer beneficiary", "permissionType": "BENEFICIARY"}, {"name": "approve-create-customer-beneficiary", "description": "This allows you to approve create customer beneficiary request", "permissionType": "BENEFICIARY"}, {"name": "edit-customer-beneficiary", "description": "This allows you to edit a customer beneficiary", "permissionType": "BENEFICIARY"}, {"name": "approve-edit-customer-beneficiary", "description": "This allows you to approve edit customer beneficiary request", "permissionType": "BENEFICIARY"}, {"name": "delete-customer-beneficiary", "description": "This allows you to delete a customer beneficiary", "permissionType": "BENEFICIARY"}, {"name": "approve-delete-customer-beneficiary", "description": "This allows you to approve delete customer beneficiary request", "permissionType": "BENEFICIARY"}, {"name": "view-customer-profile", "description": "This allows you to view customer profile", "permissionType": "PROFILE"}, {"name": "invite-customer-user", "description": "This allows you to invite a new customer user", "permissionType": "USER"}, {"name": "approve-invite-customer-user", "description": "This allows you to approve invite a new customer user request", "permissionType": "USER"}, {"name": "view-backoffice-faqs", "description": "This allows you to view all backoffice frequently asked questions", "permissionType": "FAQ"}, {"name": "view-backoffice-faq-details", "description": "This allows you to view the details of a backoffice frequently asked question", "permissionType": "FAQ"}, {"name": "create-customer-branch", "description": "This allows you to create a customer branch", "permissionType": "BRANCH"}, {"name": "approve-create-customer-branch", "description": "This allows you to approve create customer branch request", "permissionType": "BRANCH"}, {"name": "view-customer-branches", "description": "This allows you to view all customer branches", "permissionType": "BRANCH"}, {"name": "view-customer-branch-details", "description": "This allows you to view customer branch details", "permissionType": "BRANCH"}, {"name": "edit-customer-branch", "description": "This allows you to edit a customer branch", "permissionType": "BRANCH"}, {"name": "approve-edit-customer-branch", "description": "This allows you to approve edit customer branch request", "permissionType": "BRANCH"}, {"name": "delete-customer-branch", "description": "This allows you to delete a customer branch", "permissionType": "BRANCH"}, {"name": "approve-delete-customer-branch", "description": "This allows you to approve delete customer branch request", "permissionType": "BRANCH"}, {"name": "export-customer-branches", "description": "This allows you to export customer branches", "permissionType": "BRANCH"}, {"name": "view-bank-branch-location", "description": "This allows you to view bank branch location", "permissionType": "BRANCH"}, {"name": "view-atm-location", "description": "This allows you to view ATM location", "permissionType": "ATM-LOCATION"}, {"name": "create-customer-vendor", "description": "This allows you to create a customer vendor", "permissionType": "VENDOR"}, {"name": "approve-create-customer-vendor", "description": "This allows you to approve create customer vendor request", "permissionType": "VENDOR"}, {"name": "edit-customer-vendor", "description": "This allows you to edit a customer vendor", "permissionType": "VENDOR"}, {"name": "approve-edit-customer-vendor", "description": "This allows you to approve edit customer vendor request", "permissionType": "VENDOR"}, {"name": "delete-customer-vendor", "description": "This allows you to delete a customer vendor", "permissionType": "VENDOR"}, {"name": "approve-delete-customer-vendor", "description": "This allows you to approve delete customer vendor request", "permissionType": "VENDOR"}, {"name": "view-customer-vendor", "description": "This allows you to view customer vendors", "permissionType": "VENDOR"}, {"name": "view-customer-employee", "description": "This allows you to view customer employees", "permissionType": "EMPLOYEE"}, {"name": "create-customer-employee", "description": "This allows you to create a customer employee", "permissionType": "EMPLOYEE"}, {"name": "approve-create-customer-employee", "description": "This allows you to approve create customer employee request", "permissionType": "EMPLOYEE"}, {"name": "edit-customer-employee", "description": "This allows you to edit a customer employee", "permissionType": "EMPLOYEE"}, {"name": "approve-edit-customer-employee", "description": "This allows you to approve edit customer employee request", "permissionType": "EMPLOYEE"}, {"name": "delete-customer-employee", "description": "This allows you to delete a customer employee", "permissionType": "EMPLOYEE"}, {"name": "approve-delete-customer-employee", "description": "This allows you to approve delete customer employee request", "permissionType": "EMPLOYEE"}, {"name": "view-spend-analysis", "description": "This allows you to view spend analysis", "permissionType": "SPEND-ANALYSIS"}, {"name": "view-biller-information", "description": "This allows you to view biller information", "permissionType": "BILLS_PAYMENT"}, {"name": "view-backoffice-contents", "description": "This allows you to view all in-app content", "permissionType": "CONTENT"}, {"name": "view-backoffice-content-detail", "description": "This allows you to view in-app content details", "permissionType": "CONTENT"}, {"name": "approve-biller-information", "description": "This allows you to approve biller information", "permissionType": "BILLS_PAYMENT"}]