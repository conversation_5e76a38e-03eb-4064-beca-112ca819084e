[{"name": "view-dashboard", "description": "This allows you to view dashboard modules and metrics", "permissionType": "DASHBOARD"}, {"name": "view-bank", "description": "This allows you to view bank module", "permissionType": "BANK"}, {"name": "view-bank-details", "description": "This allows you to view a bank details", "permissionType": "BANK"}, {"name": "edit-bank", "description": "This allows you to edit a bank details", "permissionType": "BANK"}, {"name": "approve-edit-bank", "description": "This allows you to approve the edit a bank details request", "permissionType": "BANK"}, {"name": "disable-bank", "description": "This allows you to disable a bank", "permissionType": "BANK"}, {"name": "approve-disable-bank", "description": "This allows you to approve the disable a bank request", "permissionType": "BANK"}, {"name": "enable-bank", "description": "This allows you to enable a bank", "permissionType": "BANK"}, {"name": "approve-enable-bank", "description": "TThis allows you to approve the enable a bank request", "permissionType": "BANK"}, {"name": "export-bank", "description": "This allows you to export banks to a csv file", "permissionType": "BANK"}, {"name": "view-bank-transactions", "description": "This allows you to view a bank transactions", "permissionType": "TRANSACTION"}, {"name": "view-bank-transaction-details", "description": "This allows you to view a bank transaction details", "permissionType": "TRANSACTION"}, {"name": "export-bank-transactions", "description": "This allows you to export a bank transactions", "permissionType": "TRANSACTION"}, {"name": "view-bank-users", "description": "This allows you to view a bank users", "permissionType": "USER"}, {"name": "view-bank-user-details", "description": "This allows you to view a bank user details", "permissionType": "USER"}, {"name": "export-bank-users", "description": "This allows you to export a bank users", "permissionType": "USER"}, {"name": "enable-bank-user", "description": "This allows you to enable a bank user", "permissionType": "USER"}, {"name": "approve-enable-bank-user", "description": "This allows you to approve enable a bank user request", "permissionType": "USER"}, {"name": "disable-bank-user", "description": "This allows you to disable a bank user", "permissionType": "USER"}, {"name": "approve-disable-bank-user", "description": "This allows you to approve disable a bank user request", "permissionType": "USER"}, {"name": "view-admin-users", "description": "This allows you to view all admin users", "permissionType": "USER"}, {"name": "view-admin-user-details", "description": "This allows you to view a admin user details", "permissionType": "USER"}, {"name": "export-admin-users", "description": "This allows you to export admin users", "permissionType": "USER"}, {"name": "invite-admin-user", "description": "This allows you to invite a new admin user", "permissionType": "USER"}, {"name": "approve-invite-admin-user", "description": "This allows you to approve invite a new admin user request", "permissionType": "USER"}, {"name": "edit-admin-user-details", "description": "This allows you to edit a  admin user details", "permissionType": "USER"}, {"name": "resend-invite-email", "description": "This allows you to resend an invite email", "permissionType": "USER"}, {"name": "create-admin-roles", "description": "This allows you to create new admin roles", "permissionType": "ROLE"}, {"name": "approve-create-admin-roles", "description": "This allows you to approve create new admin roles requests", "permissionType": "ROLE"}, {"name": "view-admin-permissions", "description": "This allows you to view admin available permissions", "permissionType": "ROLE"}, {"name": "view-admin-roles", "description": "This allows you to view admin available roles", "permissionType": "ROLE"}, {"name": "delete-admin-role", "description": "This allows you to delete an admin role", "permissionType": "ROLE"}, {"name": "approve-delete-admin-role", "description": "This allows you to approve a request to delete an admin role", "permissionType": "ROLE"}, {"name": "edit-admin-role", "description": "This allows you to edit an admin role", "permissionType": "ROLE"}, {"name": "approve-edit-admin-role", "description": "This allows you to approve a request to edit an admin role", "permissionType": "ROLE"}, {"name": "treat-requests", "description": "This allows you to treat a request you have been assigned to", "permissionType": "ROLE"}, {"name": "disable-admin-role", "description": "This allows you to disable an admin role", "permissionType": "ROLE"}, {"name": "approve-disable-admin-role", "description": "This allows you to approve a request to disable an admin role", "permissionType": "ROLE"}, {"name": "enable-admin-role", "description": "This allows you to enable an admin role", "permissionType": "ROLE"}, {"name": "approve-enable-admin-role", "description": "This allows you to approve a request to enable an admin role", "permissionType": "ROLE"}, {"name": "view-admin-role-details", "description": "This allows you to view an admin role detail", "permissionType": "ROLE"}, {"name": "view-self-audit-trails", "description": "This allows you to view only your audit trails", "permissionType": "AUDIT_TRAIL"}, {"name": "view-all-audit-trails", "description": "This allows you to view all audit trails", "permissionType": "AUDIT_TRAIL"}, {"name": "approve-edit-admin-user-details", "description": "This allows you to approve edit request of a admin user", "permissionType": "USER"}, {"name": "view-self-user-details", "description": "This allows you to view your user details", "permissionType": "USER"}, {"name": "create-bank", "description": "This allows you to create new banks", "permissionType": "BANK"}, {"name": "approve-create-bank", "description": "This allows you to approve create new bank requests", "permissionType": "BANK"}, {"name": "view-admin-limit", "description": "This allows you to view limit configured for the system", "permissionType": "LIMIT"}, {"name": "edit-admin-limit", "description": "This allows you to edit limit configured for the system", "permissionType": "LIMIT"}, {"name": "approve-edit-admin-limit", "description": "This allows you to approve edit limit request configured for the system", "permissionType": "LIMIT"}, {"name": "view-bank-limit", "description": "This allows you to view bank limits", "permissionType": "LIMIT"}, {"name": "edit-bank-limit", "description": "This allows you to edit a bank limit", "permissionType": "LIMIT"}, {"name": "approve-edit-bank-limit", "description": "This allows you to approve an edit bank limit request", "permissionType": "LIMIT"}, {"name": "view-customer-account", "description": "This allows you to view customer accounts", "permissionType": "ACCOUNT"}, {"name": "enable-admin-user", "description": "This allows you to enable an admin user profile", "permissionType": "USER"}, {"name": "approve-enable-admin-user", "description": "This allows you to approve an enable admin user request", "permissionType": "USER"}, {"name": "disable-admin-user", "description": "This allows you to disable an admin user profile", "permissionType": "USER"}, {"name": "approve-disable-admin-user", "description": "This allows you to approve an disable admin user request", "permissionType": "USER"}, {"name": "view-backoffice-preference", "description": "This allows you to view the backoffice preference", "permissionType": "PREFERENCE"}, {"name": "create-backoffice-preference", "description": "This allows you to create a backoffice preference", "permissionType": "PREFERENCE"}, {"name": "approve-create-backoffice-preference", "description": "This allows you to approve create backoffice preference request", "permissionType": "PREFERENCE"}]