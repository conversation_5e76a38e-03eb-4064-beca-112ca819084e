lucid:
  database:
    url: "*********************************************************************************************"
    username: "<PERSON><PERSON><PERSON>"
    password: "Digicore_123"
    driver: "com.mysql.cj.jdbc.Driver"
  security:
    jwtKeyStorePath: "/home//ubuntu//lucid//services//config-server//config//keys//keystore.jks"
    jwtKeyStorePassword: "password"
    jwtKeyAlias: "jwtsigning"
    jwtPrivateKeyPassphrase: "password"
    corsAllowedOrigins: "*"
    corsAllowedMethods:
      - POST
      - GET
      - PUT
      - DELETE
      - PATCH
      - HEAD
    corsAllowedHeaders:
      - Content-Type
      - Access-Control-Allow-Headers
      - Access-Control-Allow-Origin
      - Access-Control-Expose-Headers
      - Content-Disposition
      - Authorization
      - Access-Control-Allow-Methods
      - X-Requested-With
    corsAllowedExposedHeaders:
      - Content-Disposition
    allowedUrls:
      - /lucid-transfer/documentation/**
      - /actuator/**
      - /api/v1/authentication/process/**
      - /api/v1/onboarding/process/**
    platform: "CUSTOMER"
    filteredUrls:
      - /actuator/**
      - /lucid-transfer/documentation/**
      - /api/v1/onboarding/process/**
      - /api/v1/bank/**
      - /api/v1/admin/**
      - /api/v1/customer/**
      - /api/v1/authentication/process/**
    allowedClientUrls:
      - unity.lucid.local:8073
      - lucid.local:8073
    redisHost: localhost
    redisPort: 6379
    skipBvnOtpValidation: true
    skipSmsValidation: true
  feature:
    modules:
      - name: TRANSFER
        supportThresholdConfiguration: true
        activities:
          - SINGLE
          - BULK
  swagger:
    deployedServerUrl: "https://lucid-gateway-service.digicoreltds.com/lucid-transfer/"
  file:
    fileUploadDirectory: "/home//ubuntu//files"
    minFileUploadSize: 1000
    maxFileUploadSize: 5000000
    s3BucketName: lucid-staging-bucket
    s3AccessKey: test
    s3SecretKey: test
    s3Region: us-east-2


