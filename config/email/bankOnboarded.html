<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap");

      body {
        font-size: 12px;
        background-color: #e8e8e8;
        position: relative;
        font-family: "Outfit", sans-serif;
      }

      * {
        position: relative;
      }

      table {
        border-collapse: collapse;
        border: none;
      }
      td,
      tr {
        border: none !important;
      }

      svg {
        width: 100%;
        height: auto;
        max-width: 100%;
      }

      @media (max-width: 600px) {
        td {
          padding: 0 10px !important;
        }
      }

      /* Dark Mode Styles */
      @media (prefers-color-scheme: dark) {
        body {
          background-color: #e8e8e8;
          color: #fff;
          font-size: 13px;
        }

        table,
        td {
          border-color: #30303028;
          border-width: 0px;
        }
      }
    </style>
  </head>

  <body>
    <div
      style="
        background-color: #e8e8e8;
        margin: 0 auto;
        width: 100%;
        max-width: 600px;
      "
    >
      <table style="padding: 0; border: 0; margin: 0; width: 100%">
        <tbody style="background-color: #fff">
          <tr>
            <td>
              <img src="https://lucid-prod-bucket.s3.amazonaws.com/templates/imgs/12.png" width="600px" height="188px" alt="">
            </td>
          </tr>

          <tr>
            <td style="color: #101541b2; padding: 0px 50px">
              <br />
              <br />
              <br />
              <br />

              <span style="color: #101541; font-weight: 500">
                Dear
                <span th:text="${name}">[Admin Name]</span>,
              </span>

              <br />
              <br />

              <span style="color: #101541b2; line-height: 20px">
                We’re pleased to inform you that your bank has been successfully
                onboarded to
                <span th:text="${platformName}">[Platform Name]. </span>

                You can now manage your services via the back-office dashboard.
              </span>
            </td>
          </tr>

          <tr style="border-color: white">
            <td style="color: #101541b2; padding: 0px 50px">
              <br />
              <br />

              <span style="color: #101541; font-weight: 600">
                Bank Login Details:
              </span>

              <br />
              <br />

              <div
                style="
                  border: 1px solid #3434341a;
                  border-radius: 16px;
                  position: relative;
                  padding: 15px;
                  overflow-y: hidden;
                "
              >

                <span style="color: #101541">
                  <b>Login URL: </b>
                  <span style="font-weight: 300" th:text="${loginUrl}">
                    [Bank URL]</span
                  >
                </span>

                <br />
                <br />

                <span style="color: #101541">
                  <b>Username: </b>
                  <span style="font-weight: 300" th:text="${username}"
                    >[Admin Username]</span
                  >
                </span>

                <br />
                <br />

                <span style="color: #101541">
                  <b>Temporary Password: </b>
                  <span
                    style="font-weight: 300"
                    th:text="${tempPassword}"
                  ></span>
                  [Temporary password]</span
                >
              </div>

              <br />

              <br />
              <br />

              <span style="color: #101541; font-weight: 600">
                Next Steps:
              </span>

              <br />
              <br />

              <div
                style="
                  border: 1px solid #3434341a;
                  border-radius: 16px;
                  padding: 15px;
                  overflow-y: hidden;
                "
              >
                <span style="color: #101541; font-weight: 300">
                  <span style="font-weight: 600">1. | Log in </span> to your
                  back-office dashboard using the credentials above.
                </span>

                <br />
                <br />

                <span style="color: #101541; font-weight: 300">
                  <span style="font-weight: 600">2.| Change your password</span>
                  upon first login for security.
                </span>

                <br />
                <br />

                <span style="color: #101541; font-weight: 300">
                  <span style="font-weight: 600"
                    >3.| Configure your banking services
                  </span>
                  as needed.
                </span>
              </div>

              <br />

              <br />
              <br />

              <span style="line-height: 20px">
                If you require any assistance, feel free to reach out to
              </span>

              <br /><br />

              <a
                th:href="${linkUrl}"
                target="_blank"
                style="
                  color:  ' + ${primaryColor} + ';
                  text-decoration: underline;
                  font-size: 14px;
                  font-weight: 500;
                "
              >
                Our Support Email
              </a>

              <br /><br />
              <br />

              <div style="border-top: 1px #3434341a solid"></div>

              <br /><br />

              <span>Best Regards</span>

              <br /><br />
              <b> <span th:text="${bankName}">[Bank Name]</span></b> Security
              Team
            </td>
          </tr>

          <tr>
            <td>
              <br /><br /><br />

              <div
                style="
                  min-height: 150px;
                  color: #101541;
                  padding: 30px;
                  border-top: 1px solid #3434341a;
                "
              >
                <img th:src="@{${logoUrl}}" alt="logo" style="width: auto; height:55px"/>
                <br />
                <a
                  th:href="${helpUrl}"
                  target="_blank"
                  style="
                    color:  ' + ${primaryColor} + ';
                    text-decoration: none;
                    font-size: 12px;
                    font-weight: 500;
                    opacity: 80%;
                  "
                >
                  Visit our help centre
                </a>

                <br />

                <span style="font-size: 12px; opacity: 80%">
                  <a
                    th:href="${updateEmailUrl}"
                    target="_blank"
                    style="
                      color:  ' + ${primaryColor} + ';
                      text-decoration: underline;
                      font-size: 12px;
                      font-weight: 500;
                      line-height: 14.4px;
                    "
                  >
                    Update your email preferences
                  </a>
                  to choose the type of emails you receive
                </span>

                <br />
                <br />

                <span style="font-size: 12px; opacity: 80%">
                  <a
                    th:href="${unsubscribeUrl}"
                    target="_blank"
                    style="
                      color:  ' + ${primaryColor} + ';
                      text-decoration: none;
                      font-size: 12px;
                      font-weight: 500;
                      line-height: 14.4px;
                    "
                  >
                    Unsubscribe
                  </a>
                  from all future emails.
                </span>

                <br />

                <span style="font-size: 12px; opacity: 80%">
                  Brocker Derive Suite 90, Chicago, IL, 702030, United States
                  <br />
                  Copyright &#169;2025
                  <span th:text="${bankName}">{Bank}</span>, All rights
                  reserved.
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </body>
</html>
