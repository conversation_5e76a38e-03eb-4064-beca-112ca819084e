/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.config;

import com.digicore.lucid.common.lib.properties.DatabasePropertyConfig;
import com.zaxxer.hikari.HikariDataSource;
import javax.sql.DataSource;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.*;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/*
 * <AUTHOR>
 * @createdOn Jan-28(Tue)-2025
 */

@Profile({"dev", "pilot", "prod"})
@Configuration
@ComponentScan({"com.digicore.lucid.customer.vas.data.lib.modules"})
@EntityScan({"com.digicore.lucid.customer.vas.data.lib.modules"})
@EnableJpaRepositories(basePackages = {"com.digicore.lucid.customer.vas.data.lib.modules"})
@RequiredArgsConstructor
@EnableJpaAuditing
public class DataSourceConfig {
  private final DatabasePropertyConfig databasePropertyConfig;

  @Bean
  @Primary
  @Profile({"dev", "pilot", "prod"})
  public DataSource dataSource() {
    return this.getHikariDataSource();
  }

  private HikariDataSource getHikariDataSource() {
    HikariDataSource dataSource = new HikariDataSource();
    dataSource.setDriverClassName(databasePropertyConfig.getDriver());
    dataSource.setJdbcUrl(databasePropertyConfig.getUrl());
    dataSource.setUsername(databasePropertyConfig.getUsername());
    dataSource.setPassword(databasePropertyConfig.getPassword());
    dataSource.setConnectionTimeout(30000L);
    return dataSource;
  }
}
