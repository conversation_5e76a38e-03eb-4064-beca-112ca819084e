package com.digicore.lucid.customer.vas.data.lib.modules.bill_payment_history.dto;

import com.digicore.registhentication.common.enums.Channel;
import com.digicore.registhentication.registration.enums.Status;
import com.digicore.registhentication.validator.enums.Currency;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class VasBillPaymentHistoryDto {
  private String transactionId;
  private String transactionReference;
  private String billerId;
  private String paymentItemId;
  private String categoryId;
  private String organizationId;
  private String amountInMinor;
  private String vatAmountInMinor;
  private String feeAmountInMinor;
  private Currency currency;
  private Status transactionStatus;
  private Channel channel;
  private LocalDateTime transactionDate;
  private String sourceAccount;
}
