/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.vas.data.lib.modules.beneficiary.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @createdOn Jul-04(Fri)-2025
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VasBeneficiaryRequestDto {
  private String name;

  @NotBlank(
      message = "Customer ID [phone number, meter number, smart card number, etc.] is required")
  private String customerId;

  private String email;
  private String category;
  private String billerId;
  private String billerCode;
  private String customerReference;
  private Boolean isFavorite;
}
