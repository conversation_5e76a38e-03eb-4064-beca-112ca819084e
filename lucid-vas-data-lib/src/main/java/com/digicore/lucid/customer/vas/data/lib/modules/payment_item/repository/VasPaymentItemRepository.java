/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.digicore.lucid.customer.vas.data.lib.modules.payment_item.repository;

import com.digicore.lucid.customer.vas.data.lib.modules.biller.model.VasBiller;
import com.digicore.lucid.customer.vas.data.lib.modules.payment_item.model.VasPaymentItem;
import com.digicore.registhentication.registration.enums.Status;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/*
 * <AUTHOR> John
 * @createdOn Apr-21(Mon)-2025
 */

public interface VasPaymentItemRepository extends JpaRepository<VasPaymentItem, Long> {

  List<VasPaymentItem> findAllByVasBillerAndIsDeletedFalse(VasBiller biller);

  @Query(
      """
    SELECT vp
    FROM VasPaymentItem vp
    JOIN FETCH vp.vasBiller vb
    JOIN FETCH vb.vasBillerCategory vbc
    WHERE vb.billerId = :billerId
      AND vbc.vasProvider = :provider
      AND vb.status = :status
      AND vbc.status = :status
      AND vp.status = :status
      AND vb.isDeleted = false
      AND vbc.isDeleted = false
      AND vp.isDeleted = false
    ORDER BY vp.name ASC
""")
  List<VasPaymentItem> findByBillerIdAndProviderAndStatus(
      @Param("billerId") String billerId,
      @Param("provider") String provider,
      @Param("status") Status status);

  @Query(
      """
        SELECT vp
        FROM VasPaymentItem vp
        JOIN FETCH vp.vasBiller vb
        JOIN FETCH vb.vasBillerCategory vbc
        WHERE vb.billerId = :billerId
          AND vbc.vasProvider = :provider
          AND vb.status = :status
          AND vbc.status = :status
          AND vp.status = :status
          AND vb.isDeleted = false
          AND vbc.isDeleted = false
          AND vp.isDeleted = false
          AND vp.paymentItemId = :paymentItemId
        ORDER BY vp.name ASC
    """)
  Optional<VasPaymentItem> findByPaymentItemIdAndBillerIdAndProviderAndStatus(
      @Param("paymentItemId") String paymentItemId,
      @Param("billerId") String billerId,
      @Param("provider") String provider,
      @Param("status") Status status);
}
