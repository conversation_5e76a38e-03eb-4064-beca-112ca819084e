<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.2</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.digicore</groupId>
	<artifactId>lucid-cba-service</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<name>lucid-cba-service</name>
	<description>This service handles all integrations with CBA.</description>
	<url/>
	<licenses>
		<license/>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>
	<properties>
		<java.version>21</java.version>
		<spring-cloud.version>2024.0.0</spring-cloud.version>
		<archiva.url>https://archiva.digicoreltds.com/repository/internal/</archiva.url>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.digicore</groupId>
			<artifactId>lucid-integration-lib</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.digicore</groupId>
			<artifactId>zeus-api-helper</artifactId>
			<version>1.0.9-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-config</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>com.diffplug.spotless</groupId>
				<artifactId>spotless-maven-plugin</artifactId>
				<version>2.43.0</version>
				<configuration>
					<java>
						<googleJavaFormat>
							<version>1.25.2</version>
						</googleJavaFormat>
						<importOrder/>
						<removeUnusedImports/>
						<formatAnnotations/>
						<trimTrailingWhitespace/>
						<endWithNewline/>
						<indent>
							<spaces>true</spaces>
							<spacesPerTab>2</spacesPerTab>
						</indent>
					</java>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>apply</goal>
						</goals>
						<phase>compile</phase>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>archiva</id>
			<name>Archiva Managed Internal Repository</name>
			<url>${archiva.url}</url>
		</repository>
		<repository>
			<id>oss-jfrog-artifactory</id>
			<name>oss-jfrog-artifactory-releases</name>
			<url>http://oss.jfrog.org/artifactory/oss-release-local</url>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>archiva</id>
			<name>Archiva Repository</name>
			<url>${archiva.url}</url>
		</pluginRepository>
	</pluginRepositories>

	<distributionManagement>
		<repository>
			<id>archiva</id>
			<name>External Release Repository</name>
			<url>${archiva.url}</url>
		</repository>
	</distributionManagement>

</project>
