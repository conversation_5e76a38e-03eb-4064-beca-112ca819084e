/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */

package com.example.demo.interview2;

/**
 * <AUTHOR>
 * @createdOn Apr-30(Wed)-2025
 */

//https://www.jdoodle.com/online-java-compiler

import java.util.*;

public class MyClassSolution {
    public static void main(String[] args) {
      /*
        You are given an array of integers. Write a function that returns all the elements that appear more than once in the array.
	    •	Return the result as a list of integers.
	    •	You can return them in any order.
	    •	Each element in the result must be unique (i.e., no duplicates in the result list).

         Input: [5, 1, 2, 3, 2, 4, 5, 5]
         Output: [2, 5]  // because 2 appeared twice and 5 appeared thrice

         ⏱ Constraints:
	    •	You may assume the array contains only integers.
      */

        int[] input = {5, 1, 2, 3, 2, 4, 5};
        System.out.println("Duplicates: " + findDuplicates(input));
    }

//    public static List<Integer> findDuplicates(int[] nums) {
//        Map<Integer, Integer> freqMap = new HashMap<>();
//        List<Integer> result = new ArrayList<>();
//
//        for (int num : nums) {
//            freqMap.put(num, freqMap.getOrDefault(num, 0) + 1);
//        }
//
//        for (Map.Entry<Integer, Integer> entry : freqMap.entrySet()) {
//            if (entry.getValue() > 1) {
//                result.add(entry.getKey());
//            }
//        }
//
//        return result;
//    }

    public static List<Integer> findDuplicates(int[] nums) {
        Set<Integer> seen = new HashSet<>();
        Set<Integer> duplicates = new HashSet<>();

        List<Integer> ss = new ArrayList<>();
        ss.add(1)

        for (int num : nums) {
            if (!seen.add(num)) {
                duplicates.add(num);
            }
        }

        return new ArrayList<>(duplicates);
    }

}
