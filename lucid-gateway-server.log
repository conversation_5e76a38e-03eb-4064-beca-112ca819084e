2025-07-07T15:38:40.765+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Starting LucidGatewayServerApplication using Java 21.0.1 with PID 98791 (/Users/<USER>/Desktop/LUCID/lucid-gateway-server/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T15:38:40.766+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : No active profile set, falling back to 1 default profile: "default"
2025-07-07T15:38:40.786+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:38:40.786+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-gateway-server, profiles=[default], label=null, version=null, state=null
2025-07-07T15:38:40.787+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07T15:38:40.787+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07T15:38:41.424+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=17050c0b-2ecf-37a9-b237-253e4dfa3b87
2025-07-07T15:38:41.814+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T15:38:41.845+01:00 ERROR 98791 --- [lucid-gateway-server] [restartedMain] i.n.r.d.DnsServerAddressStreamProviders  : Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-07-07T15:38:41.920+01:00  INFO 98791 --- [lucid-gateway-server] [redisson-netty-3-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:38:41.987+01:00  INFO 98791 --- [lucid-gateway-server] [redisson-netty-3-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:38:42.248+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-07-07T15:38:42.248+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-07-07T15:38:42.248+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-07-07T15:38:42.248+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-07-07T15:38:42.248+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-07-07T15:38:42.248+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-07-07T15:38:42.248+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-07-07T15:38:42.248+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-07-07T15:38:42.249+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-07-07T15:38:42.249+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-07-07T15:38:42.249+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-07-07T15:38:42.249+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-07T15:38:42.249+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-07-07T15:38:42.249+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-07T15:38:42.421+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T15:38:42.646+01:00  WARN 98791 --- [lucid-gateway-server] [restartedMain] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T15:38:42.690+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-07T15:38:42.696+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T15:38:42.742+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T15:38:42.753+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T15:38:42.755+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T15:38:42.758+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T15:38:42.759+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T15:38:42.759+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T15:38:42.759+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T15:38:42.759+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T15:38:42.759+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T15:38:42.759+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T15:38:42.957+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T15:38:42.961+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T15:38:42.964+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T15:38:42.965+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751899122965 with initial instances count: 1
2025-07-07T15:38:42.969+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-GATEWAY-SERVER with eureka with status UP
2025-07-07T15:38:42.971+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751899122970, current=UP, previous=STARTING]
2025-07-07T15:38:42.974+01:00  INFO 98791 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T15:38:43.031+01:00  INFO 98791 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T15:38:43.090+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8073 (http)
2025-07-07T15:38:43.091+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8073
2025-07-07T15:38:43.107+01:00  INFO 98791 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Started LucidGatewayServerApplication in 2.814 seconds (process running for 8.603)
2025-07-07T15:39:01.340+01:00  INFO 98791 --- [lucid-gateway-server] [reactor-http-nio-2] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T15:41:47.131+01:00  INFO 98791 --- [lucid-gateway-server] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-GATEWAY-SERVER with eureka with status DOWN
2025-07-07T15:41:47.132+01:00  INFO 98791 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751899307132, current=DOWN, previous=UP]
2025-07-07T15:41:47.133+01:00  INFO 98791 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T15:41:47.138+01:00  INFO 98791 --- [lucid-gateway-server] [SpringApplicationShutdownHook] o.s.b.w.embedded.netty.GracefulShutdown  : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T15:41:47.142+01:00  INFO 98791 --- [lucid-gateway-server] [netty-shutdown] o.s.b.w.embedded.netty.GracefulShutdown  : Graceful shutdown complete
2025-07-07T15:41:47.155+01:00  INFO 98791 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T15:41:47.177+01:00  INFO 98791 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T15:41:50.183+01:00  INFO 98791 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T15:41:50.196+01:00  INFO 98791 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - deregister  status: 200
2025-07-07T15:41:50.196+01:00  INFO 98791 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T15:41:58.083+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Starting LucidGatewayServerApplication using Java 21.0.1 with PID 99552 (/Users/<USER>/Desktop/LUCID/lucid-gateway-server/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T15:41:58.084+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : No active profile set, falling back to 1 default profile: "default"
2025-07-07T15:41:58.102+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:41:58.102+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-gateway-server, profiles=[default], label=null, version=null, state=null
2025-07-07T15:41:58.103+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07T15:41:58.103+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07T15:41:58.645+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=17050c0b-2ecf-37a9-b237-253e4dfa3b87
2025-07-07T15:41:58.985+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T15:41:59.005+01:00 ERROR 99552 --- [lucid-gateway-server] [restartedMain] i.n.r.d.DnsServerAddressStreamProviders  : Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-07-07T15:41:59.083+01:00  INFO 99552 --- [lucid-gateway-server] [redisson-netty-3-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:41:59.113+01:00  INFO 99552 --- [lucid-gateway-server] [redisson-netty-3-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:41:59.355+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-07-07T15:41:59.355+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-07-07T15:41:59.355+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-07-07T15:41:59.355+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-07-07T15:41:59.356+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-07-07T15:41:59.356+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-07-07T15:41:59.356+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-07-07T15:41:59.356+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-07-07T15:41:59.356+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-07-07T15:41:59.356+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-07-07T15:41:59.356+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-07-07T15:41:59.356+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-07T15:41:59.356+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-07-07T15:41:59.356+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-07T15:41:59.465+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T15:41:59.621+01:00  WARN 99552 --- [lucid-gateway-server] [restartedMain] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T15:41:59.671+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-07T15:41:59.676+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T15:41:59.724+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T15:41:59.734+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T15:41:59.736+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T15:41:59.739+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T15:41:59.739+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T15:41:59.739+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T15:41:59.739+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T15:41:59.739+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T15:41:59.739+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T15:41:59.739+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T15:41:59.890+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T15:41:59.891+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T15:41:59.892+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T15:41:59.893+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751899319893 with initial instances count: 2
2025-07-07T15:41:59.895+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-GATEWAY-SERVER with eureka with status UP
2025-07-07T15:41:59.896+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751899319896, current=UP, previous=STARTING]
2025-07-07T15:41:59.897+01:00  INFO 99552 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T15:41:59.925+01:00  INFO 99552 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T15:41:59.968+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8073 (http)
2025-07-07T15:41:59.969+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8073
2025-07-07T15:41:59.987+01:00  INFO 99552 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Started LucidGatewayServerApplication in 2.313 seconds (process running for 7.933)
2025-07-07T15:42:41.063+01:00  INFO 99552 --- [lucid-gateway-server] [reactor-http-nio-2] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T15:45:40.433+01:00  INFO 99552 --- [lucid-gateway-server] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-GATEWAY-SERVER with eureka with status DOWN
2025-07-07T15:45:40.435+01:00  INFO 99552 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751899540435, current=DOWN, previous=UP]
2025-07-07T15:45:40.435+01:00  INFO 99552 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T15:45:40.453+01:00  INFO 99552 --- [lucid-gateway-server] [SpringApplicationShutdownHook] o.s.b.w.embedded.netty.GracefulShutdown  : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T15:45:40.468+01:00  INFO 99552 --- [lucid-gateway-server] [netty-shutdown] o.s.b.w.embedded.netty.GracefulShutdown  : Graceful shutdown complete
2025-07-07T15:45:40.490+01:00  INFO 99552 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T15:45:40.547+01:00  INFO 99552 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T15:45:43.555+01:00  INFO 99552 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T15:45:43.605+01:00  INFO 99552 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/} exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:91)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:149)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 37 more

2025-07-07T15:45:43.605+01:00  WARN 99552 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T15:45:43.614+01:00  INFO 99552 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:149)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 38 more

2025-07-07T15:45:43.614+01:00  WARN 99552 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T15:45:43.614+01:00 ERROR 99552 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:149) ~[spring-boot-3.4.1.jar:3.4.1]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147) ~[spring-boot-3.4.1.jar:3.4.1]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116) ~[spring-boot-3.4.1.jar:3.4.1]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T15:45:43.619+01:00  INFO 99552 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T15:47:25.496+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Starting LucidGatewayServerApplication using Java 21.0.1 with PID 1493 (/Users/<USER>/Desktop/LUCID/lucid-gateway-server/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T15:47:25.497+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : No active profile set, falling back to 1 default profile: "default"
2025-07-07T15:47:25.516+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T15:47:25.516+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-gateway-server, profiles=[default], label=null, version=null, state=null
2025-07-07T15:47:25.517+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07T15:47:25.517+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07T15:47:26.051+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=17050c0b-2ecf-37a9-b237-253e4dfa3b87
2025-07-07T15:47:26.446+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T15:47:26.467+01:00 ERROR 1493 --- [lucid-gateway-server] [restartedMain] i.n.r.d.DnsServerAddressStreamProviders  : Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-07-07T15:47:26.548+01:00  INFO 1493 --- [lucid-gateway-server] [redisson-netty-3-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:47:26.602+01:00  INFO 1493 --- [lucid-gateway-server] [redisson-netty-3-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-07-07T15:47:26.864+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-07T15:47:26.977+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T15:47:27.128+01:00  WARN 1493 --- [lucid-gateway-server] [restartedMain] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T15:47:27.178+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-07T15:47:27.186+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T15:47:27.243+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T15:47:27.254+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T15:47:27.255+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T15:47:27.259+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T15:47:27.259+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T15:47:27.259+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T15:47:27.259+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T15:47:27.259+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T15:47:27.259+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T15:47:27.259+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T15:47:27.391+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T15:47:27.392+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T15:47:27.393+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T15:47:27.394+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751899647394 with initial instances count: 0
2025-07-07T15:47:27.398+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-GATEWAY-SERVER with eureka with status UP
2025-07-07T15:47:27.398+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751899647398, current=UP, previous=STARTING]
2025-07-07T15:47:27.399+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T15:47:27.427+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T15:47:27.446+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8073 (http)
2025-07-07T15:47:27.450+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8073
2025-07-07T15:47:27.467+01:00  INFO 1493 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Started LucidGatewayServerApplication in 2.395 seconds (process running for 8.03)
2025-07-07T15:47:57.396+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T15:47:57.398+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T15:47:57.398+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T15:47:57.399+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T15:47:57.399+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T15:47:57.399+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application version is -1: false
2025-07-07T15:47:57.399+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T15:47:57.454+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T15:47:59.855+01:00  INFO 1493 --- [lucid-gateway-server] [reactor-http-nio-2] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T15:52:27.268+01:00  INFO 1493 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T15:53:05.809+01:00  INFO 1493 --- [lucid-gateway-server] [reactor-http-nio-3] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T15:57:27.287+01:00  INFO 1493 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:00:07.700+01:00  INFO 1493 --- [lucid-gateway-server] [reactor-http-nio-4] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T16:01:35.692+01:00  INFO 1493 --- [lucid-gateway-server] [reactor-http-nio-5] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T16:01:55.129+01:00  INFO 1493 --- [lucid-gateway-server] [reactor-http-nio-5] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T16:02:05.224+01:00  WARN 1493 --- [lucid-gateway-server] [reactor-http-nio-5] r.netty.http.client.HttpClientConnect    : [9a4a46f4-1, L:/*************:53321 - R:/*************:8075] The connection observed an error

io.netty.handler.timeout.ReadTimeoutException: null

2025-07-07T16:02:27.318+01:00  INFO 1493 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:03:46.733+01:00  INFO 1493 --- [lucid-gateway-server] [reactor-http-nio-6] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T16:03:56.769+01:00  WARN 1493 --- [lucid-gateway-server] [reactor-http-nio-6] r.netty.http.client.HttpClientConnect    : [b1ca59d8-1, L:/*************:53383 - R:/*************:8075] The connection observed an error

io.netty.handler.timeout.ReadTimeoutException: null

2025-07-07T16:04:24.579+01:00  INFO 1493 --- [lucid-gateway-server] [reactor-http-nio-6] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T16:04:34.610+01:00  WARN 1493 --- [lucid-gateway-server] [reactor-http-nio-6] r.netty.http.client.HttpClientConnect    : [c0c849c0-1, L:/*************:53396 - R:/*************:8075] The connection observed an error

io.netty.handler.timeout.ReadTimeoutException: null

2025-07-07T16:07:27.329+01:00  INFO 1493 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:10:44.872+01:00  INFO 1493 --- [lucid-gateway-server] [reactor-http-nio-7] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T16:10:55.057+01:00  WARN 1493 --- [lucid-gateway-server] [reactor-http-nio-7] r.netty.http.client.HttpClientConnect    : [299a3cac-1, L:/*************:53537 - R:/*************:8075] The connection observed an error

io.netty.handler.timeout.ReadTimeoutException: null

2025-07-07T16:12:27.342+01:00  INFO 1493 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:16:21.272+01:00  INFO 1493 --- [lucid-gateway-server] [reactor-http-nio-8] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T16:16:36.388+01:00  INFO 1493 --- [lucid-gateway-server] [reactor-http-nio-8] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T16:16:42.596+01:00  INFO 1493 --- [lucid-gateway-server] [reactor-http-nio-8] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T16:16:59.314+01:00  INFO 1493 --- [lucid-gateway-server] [reactor-http-nio-8] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T16:17:27.271+01:00  INFO 1493 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:22:27.277+01:00  INFO 1493 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:26:59.508+01:00  INFO 1493 --- [lucid-gateway-server] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-GATEWAY-SERVER with eureka with status DOWN
2025-07-07T16:26:59.525+01:00  INFO 1493 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751902019525, current=DOWN, previous=UP]
2025-07-07T16:26:59.527+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T16:26:59.586+01:00  INFO 1493 --- [lucid-gateway-server] [SpringApplicationShutdownHook] o.s.b.w.embedded.netty.GracefulShutdown  : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T16:26:59.594+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T16:26:59.619+01:00  INFO 1493 --- [lucid-gateway-server] [netty-shutdown] o.s.b.w.embedded.netty.GracefulShutdown  : Graceful shutdown complete
2025-07-07T16:26:59.737+01:00  INFO 1493 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T16:26:59.932+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-HeartbeatExecutor-%d] o.a.h.c.h.i.c.HttpRequestRetryExec       : Recoverable I/O exception (org.apache.hc.core5.http.NoHttpResponseException) caught when processing request to {}->[http://localhost:8070]
2025-07-07T16:26:59.948+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-HeartbeatExecutor-%d] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/} exception=I/O error on PUT request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on PUT request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.sendHeartBeat(RestTemplateEurekaHttpClient.java:119)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:91)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:845)
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1402)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 19 more

2025-07-07T16:26:59.950+01:00  WARN 1493 --- [lucid-gateway-server] [DiscoveryClient-HeartbeatExecutor-%d] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on PUT request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T16:26:59.984+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-HeartbeatExecutor-%d] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on PUT request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on PUT request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.sendHeartBeat(RestTemplateEurekaHttpClient.java:119)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:845)
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1402)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 20 more

2025-07-07T16:26:59.986+01:00  WARN 1493 --- [lucid-gateway-server] [DiscoveryClient-HeartbeatExecutor-%d] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on PUT request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T16:26:59.986+01:00 ERROR 1493 --- [lucid-gateway-server] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - was unable to send heartbeat!

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:845) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1402) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T16:27:00.236+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/} exception=I/O error on GET request for "http://localhost:8070/eureka/apps/delta": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:8070/eureka/apps/delta": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.getApplicationsInternal(RestTemplateEurekaHttpClient.java:174)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.getDelta(RestTemplateEurekaHttpClient.java:186)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:91)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1080)
	at com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:963)
	at com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1473)
	at com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1441)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 22 more

2025-07-07T16:27:00.237+01:00  WARN 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on GET request for "http://localhost:8070/eureka/apps/delta": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T16:27:00.258+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on GET request for "http://localhost:8070/eureka/apps/delta": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:8070/eureka/apps/delta": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.getApplicationsInternal(RestTemplateEurekaHttpClient.java:174)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.getDelta(RestTemplateEurekaHttpClient.java:186)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1080)
	at com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:963)
	at com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1473)
	at com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1441)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 23 more

2025-07-07T16:27:00.259+01:00  WARN 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on GET request for "http://localhost:8070/eureka/apps/delta": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T16:27:00.260+01:00  INFO 1493 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - was unable to refresh its cache! This periodic background refresh will be retried in 30 seconds. status = Cannot execute request on any known server stacktrace = com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1080)
	at com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:963)
	at com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1473)
	at com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1441)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-07-07T16:27:02.758+01:00  INFO 1493 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T16:27:02.788+01:00  INFO 1493 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:149)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 38 more

2025-07-07T16:27:02.789+01:00  WARN 1493 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T16:27:02.789+01:00 ERROR 1493 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:149) ~[spring-boot-3.4.1.jar:3.4.1]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147) ~[spring-boot-3.4.1.jar:3.4.1]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116) ~[spring-boot-3.4.1.jar:3.4.1]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T16:27:02.791+01:00  INFO 1493 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T16:57:11.685+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Starting LucidGatewayServerApplication using Java 21.0.1 with PID 18131 (/Users/<USER>/Desktop/LUCID/lucid-gateway-server/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T16:57:11.687+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : No active profile set, falling back to 1 default profile: "default"
2025-07-07T16:57:11.709+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T16:57:11.709+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-gateway-server, profiles=[default], label=null, version=null, state=null
2025-07-07T16:57:11.710+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07T16:57:11.710+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07T16:57:12.337+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=17050c0b-2ecf-37a9-b237-253e4dfa3b87
2025-07-07T16:57:12.727+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T16:57:12.759+01:00 ERROR 18131 --- [lucid-gateway-server] [restartedMain] i.n.r.d.DnsServerAddressStreamProviders  : Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-07-07T16:57:12.850+01:00  INFO 18131 --- [lucid-gateway-server] [redisson-netty-3-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T16:57:12.908+01:00  INFO 18131 --- [lucid-gateway-server] [redisson-netty-3-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-07T16:57:13.207+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-07-07T16:57:13.208+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-07T16:57:13.333+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T16:57:13.504+01:00  WARN 18131 --- [lucid-gateway-server] [restartedMain] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T16:57:13.558+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-07T16:57:13.567+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T16:57:13.617+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T16:57:13.627+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T16:57:13.628+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T16:57:13.632+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T16:57:13.632+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T16:57:13.632+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T16:57:13.632+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T16:57:13.632+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T16:57:13.632+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T16:57:13.632+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T16:57:13.905+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T16:57:13.906+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T16:57:13.907+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T16:57:13.907+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751903833907 with initial instances count: 0
2025-07-07T16:57:13.909+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-GATEWAY-SERVER with eureka with status UP
2025-07-07T16:57:13.909+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751903833909, current=UP, previous=STARTING]
2025-07-07T16:57:13.910+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T16:57:13.963+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8073 (http)
2025-07-07T16:57:13.965+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8073
2025-07-07T16:57:13.968+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T16:57:13.983+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Started LucidGatewayServerApplication in 3.017 seconds (process running for 8.97)
2025-07-07T16:57:43.912+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T16:57:43.912+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T16:57:43.912+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T16:57:43.912+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T16:57:43.912+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T16:57:43.912+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Application version is -1: false
2025-07-07T16:57:43.913+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T16:57:43.946+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-CacheRefreshExecutor-%d] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T17:01:13.428+01:00  INFO 18131 --- [lucid-gateway-server] [reactor-http-nio-2] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T17:01:43.654+01:00 ERROR 18131 --- [lucid-gateway-server] [reactor-http-nio-2] a.w.r.e.AbstractErrorWebExceptionHandler : [0a54f8ee-1]  500 Server Error for HTTP POST "/lucid-customer/api/v1/authentication/process/login"

io.netty.channel.ConnectTimeoutException: connection timed out after 30000 ms: /*************:8075
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263) ~[netty-transport-4.1.116.Final.jar:4.1.116.Final]
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ com.digicore.lucid.gateway.server.filter.CORSFilter$$Lambda/0x000000780157d570 [DefaultWebFilterChain]
	*__checkpoint ⇢ org.springframework.cloud.gateway.filter.WeightCalculatorWebFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP POST "/lucid-customer/api/v1/authentication/process/login" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263) ~[netty-transport-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:156) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T17:02:13.643+01:00  INFO 18131 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:03:22.071+01:00  INFO 18131 --- [lucid-gateway-server] [reactor-http-nio-3] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T17:03:52.103+01:00 ERROR 18131 --- [lucid-gateway-server] [reactor-http-nio-3] a.w.r.e.AbstractErrorWebExceptionHandler : [e28ebedb-2]  500 Server Error for HTTP POST "/lucid-customer/api/v1/authentication/process/login"

io.netty.channel.ConnectTimeoutException: connection timed out after 30000 ms: /*************:8075
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263) ~[netty-transport-4.1.116.Final.jar:4.1.116.Final]
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ com.digicore.lucid.gateway.server.filter.CORSFilter$$Lambda/0x000000780157d570 [DefaultWebFilterChain]
	*__checkpoint ⇢ org.springframework.cloud.gateway.filter.WeightCalculatorWebFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP POST "/lucid-customer/api/v1/authentication/process/login" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263) ~[netty-transport-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:156) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.116.Final.jar:4.1.116.Final]
		at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T17:04:45.548+01:00  INFO 18131 --- [lucid-gateway-server] [reactor-http-nio-4] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T17:07:13.648+01:00  INFO 18131 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:09:14.241+01:00  INFO 18131 --- [lucid-gateway-server] [reactor-http-nio-5] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T17:09:27.972+01:00  INFO 18131 --- [lucid-gateway-server] [reactor-http-nio-5] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T17:10:58.727+01:00  INFO 18131 --- [lucid-gateway-server] [reactor-http-nio-6] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T17:12:13.656+01:00  INFO 18131 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:17:13.627+01:00  INFO 18131 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:22:13.626+01:00  INFO 18131 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:27:13.634+01:00  INFO 18131 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:32:13.797+01:00  INFO 18131 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:37:13.806+01:00  INFO 18131 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:42:13.818+01:00  INFO 18131 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:47:13.833+01:00  INFO 18131 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:52:13.844+01:00  INFO 18131 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:55:36.522+01:00  INFO 18131 --- [lucid-gateway-server] [reactor-http-nio-7] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/onboarding/process/account/initiate
2025-07-07T17:56:16.229+01:00  INFO 18131 --- [lucid-gateway-server] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-07T17:56:16.293+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-7] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-GATEWAY-SERVER with eureka with status DOWN
2025-07-07T17:56:16.296+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-7] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=*************, current=DOWN, previous=UP]
2025-07-07T17:56:16.298+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T17:56:16.434+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-7] o.s.b.w.embedded.netty.GracefulShutdown  : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T17:56:16.497+01:00  INFO 18131 --- [lucid-gateway-server] [netty-shutdown] o.s.b.w.embedded.netty.GracefulShutdown  : Graceful shutdown complete
2025-07-07T17:56:16.522+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T17:56:16.691+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-7] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T17:56:19.706+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-7] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T17:56:19.707+01:00  WARN 18131 --- [lucid-gateway-server] [DiscoveryClient-%d] c.netflix.discovery.TimedSupervisorTask  : task supervisor shutting down, can't accept the task
2025-07-07T17:56:19.806+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-7] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - deregister  status: 200
2025-07-07T17:56:19.811+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-7] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T17:56:20.486+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Starting LucidGatewayServerApplication using Java 21.0.1 with PID 18131 (/Users/<USER>/Desktop/LUCID/lucid-gateway-server/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T17:56:20.486+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : No active profile set, falling back to 1 default profile: "default"
2025-07-07T17:56:20.490+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T17:56:20.490+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-gateway-server, profiles=[default], label=null, version=null, state=null
2025-07-07T17:56:21.616+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=17050c0b-2ecf-37a9-b237-253e4dfa3b87
2025-07-07T17:56:22.030+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T17:56:22.046+01:00  INFO 18131 --- [lucid-gateway-server] [redisson-netty-7-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T17:56:22.380+01:00  INFO 18131 --- [lucid-gateway-server] [redisson-netty-7-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T17:56:22.659+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-07-07T17:56:22.660+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-07T17:56:22.764+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T17:56:22.827+01:00  WARN 18131 --- [lucid-gateway-server] [restartedMain] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T17:56:22.847+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-07T17:56:22.872+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T17:56:22.898+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T17:56:22.900+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T17:56:22.900+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:56:22.900+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T17:56:22.900+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T17:56:22.900+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T17:56:22.900+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T17:56:22.900+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T17:56:22.900+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T17:56:22.900+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T17:56:22.953+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T17:56:22.954+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T17:56:22.954+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T17:56:22.954+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751907382954 with initial instances count: 2
2025-07-07T17:56:22.956+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-GATEWAY-SERVER with eureka with status UP
2025-07-07T17:56:22.956+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751907382956, current=UP, previous=STARTING]
2025-07-07T17:56:22.956+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T17:56:22.987+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8073 (http)
2025-07-07T17:56:22.988+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8073
2025-07-07T17:56:22.992+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T17:56:23.001+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Started LucidGatewayServerApplication in 2.947 seconds (process running for 3557.898)
2025-07-07T17:56:23.013+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-07-07T17:56:24.272+01:00  INFO 18131 --- [lucid-gateway-server] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 18 class path changes (0 additions, 0 deletions, 18 modifications)
2025-07-07T17:56:24.274+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-17] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-GATEWAY-SERVER with eureka with status DOWN
2025-07-07T17:56:24.274+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-17] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751907384274, current=DOWN, previous=UP]
2025-07-07T17:56:24.274+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T17:56:24.275+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-17] o.s.b.w.embedded.netty.GracefulShutdown  : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T17:56:24.276+01:00  INFO 18131 --- [lucid-gateway-server] [netty-shutdown] o.s.b.w.embedded.netty.GracefulShutdown  : Graceful shutdown complete
2025-07-07T17:56:24.278+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T17:56:24.285+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-17] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T17:56:27.297+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-17] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T17:56:27.360+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-17] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - deregister  status: 200
2025-07-07T17:56:27.361+01:00  INFO 18131 --- [lucid-gateway-server] [Thread-17] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T17:56:28.190+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Starting LucidGatewayServerApplication using Java 21.0.1 with PID 18131 (/Users/<USER>/Desktop/LUCID/lucid-gateway-server/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T17:56:28.191+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : No active profile set, falling back to 1 default profile: "default"
2025-07-07T17:56:28.194+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T17:56:28.194+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-gateway-server, profiles=[default], label=null, version=null, state=null
2025-07-07T17:56:29.049+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=17050c0b-2ecf-37a9-b237-253e4dfa3b87
2025-07-07T17:56:29.203+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T17:56:29.210+01:00  INFO 18131 --- [lucid-gateway-server] [redisson-netty-11-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T17:56:29.372+01:00  INFO 18131 --- [lucid-gateway-server] [redisson-netty-11-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T17:56:29.883+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-07-07T17:56:29.888+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-07-07T17:56:29.889+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-07-07T17:56:29.889+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-07-07T17:56:29.889+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-07-07T17:56:29.889+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-07-07T17:56:29.889+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-07-07T17:56:29.889+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-07-07T17:56:29.889+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-07-07T17:56:29.889+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-07-07T17:56:29.889+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-07-07T17:56:29.889+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-07T17:56:29.889+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-07-07T17:56:29.889+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-07T17:56:30.098+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T17:56:30.149+01:00  WARN 18131 --- [lucid-gateway-server] [restartedMain] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T17:56:30.171+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-07T17:56:30.185+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T17:56:30.319+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T17:56:30.322+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T17:56:30.323+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T17:56:30.324+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T17:56:30.324+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T17:56:30.324+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T17:56:30.324+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T17:56:30.324+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T17:56:30.324+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T17:56:30.324+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T17:56:30.654+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T17:56:30.658+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T17:56:30.662+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T17:56:30.664+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751907390664 with initial instances count: 2
2025-07-07T17:56:30.666+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-GATEWAY-SERVER with eureka with status UP
2025-07-07T17:56:30.666+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751907390666, current=UP, previous=STARTING]
2025-07-07T17:56:30.667+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T17:56:30.726+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T17:56:30.739+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8073 (http)
2025-07-07T17:56:30.741+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8073
2025-07-07T17:56:30.788+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Started LucidGatewayServerApplication in 2.991 seconds (process running for 3565.684)
2025-07-07T17:56:30.805+01:00  INFO 18131 --- [lucid-gateway-server] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-07-07T17:59:06.891+01:00  INFO 18131 --- [lucid-gateway-server] [reactor-http-nio-2] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/onboarding/process/account/initiate
2025-07-07T17:59:23.252+01:00  INFO 18131 --- [lucid-gateway-server] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-GATEWAY-SERVER with eureka with status DOWN
2025-07-07T17:59:23.283+01:00  INFO 18131 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=*************, current=DOWN, previous=UP]
2025-07-07T17:59:23.286+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T17:59:23.314+01:00  INFO 18131 --- [lucid-gateway-server] [SpringApplicationShutdownHook] o.s.b.w.embedded.netty.GracefulShutdown  : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T17:59:23.341+01:00  INFO 18131 --- [lucid-gateway-server] [netty-shutdown] o.s.b.w.embedded.netty.GracefulShutdown  : Graceful shutdown complete
2025-07-07T17:59:23.422+01:00  INFO 18131 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T17:59:23.508+01:00  INFO 18131 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T17:59:26.517+01:00  INFO 18131 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T17:59:26.579+01:00  INFO 18131 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/} exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:91)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:149)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 37 more

2025-07-07T17:59:26.580+01:00  WARN 18131 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T17:59:26.593+01:00  INFO 18131 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:149)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 38 more

2025-07-07T17:59:26.594+01:00  WARN 18131 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T17:59:26.594+01:00 ERROR 18131 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:149) ~[spring-boot-3.4.1.jar:3.4.1]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147) ~[spring-boot-3.4.1.jar:3.4.1]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116) ~[spring-boot-3.4.1.jar:3.4.1]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T17:59:26.597+01:00  INFO 18131 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T19:24:20.671+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Starting LucidGatewayServerApplication using Java 21.0.1 with PID 49855 (/Users/<USER>/Desktop/LUCID/lucid-gateway-server/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T19:24:20.673+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : No active profile set, falling back to 1 default profile: "default"
2025-07-07T19:24:20.726+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T19:24:20.726+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-gateway-server, profiles=[default], label=null, version=null, state=null
2025-07-07T19:24:20.729+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07T19:24:20.730+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07T19:24:21.656+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=17050c0b-2ecf-37a9-b237-253e4dfa3b87
2025-07-07T19:24:22.254+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T19:24:22.293+01:00 ERROR 49855 --- [lucid-gateway-server] [restartedMain] i.n.r.d.DnsServerAddressStreamProviders  : Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-07-07T19:24:22.443+01:00  INFO 49855 --- [lucid-gateway-server] [redisson-netty-3-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T19:24:22.647+01:00  INFO 49855 --- [lucid-gateway-server] [redisson-netty-3-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T19:24:23.141+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-07-07T19:24:23.141+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-07-07T19:24:23.141+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-07-07T19:24:23.142+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-07-07T19:24:23.142+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-07-07T19:24:23.142+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-07-07T19:24:23.142+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-07-07T19:24:23.142+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-07-07T19:24:23.142+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-07-07T19:24:23.142+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-07-07T19:24:23.142+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-07-07T19:24:23.142+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-07T19:24:23.142+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-07-07T19:24:23.142+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-07T19:24:23.414+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T19:24:23.822+01:00  WARN 49855 --- [lucid-gateway-server] [restartedMain] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T19:24:23.902+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-07T19:24:23.911+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T19:24:23.966+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T19:24:23.982+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T19:24:23.984+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T19:24:23.989+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T19:24:23.989+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T19:24:23.989+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T19:24:23.990+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T19:24:23.990+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T19:24:23.990+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T19:24:23.990+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T19:24:24.290+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T19:24:24.291+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T19:24:24.292+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T19:24:24.293+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751912664293 with initial instances count: 1
2025-07-07T19:24:24.295+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-GATEWAY-SERVER with eureka with status UP
2025-07-07T19:24:24.295+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751912664295, current=UP, previous=STARTING]
2025-07-07T19:24:24.296+01:00  INFO 49855 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T19:24:24.367+01:00  INFO 49855 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T19:24:24.381+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8073 (http)
2025-07-07T19:24:24.382+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8073
2025-07-07T19:24:24.399+01:00  INFO 49855 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Started LucidGatewayServerApplication in 5.231 seconds (process running for 13.712)
2025-07-07T19:25:55.982+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-2] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/authentication/process/login
2025-07-07T19:26:25.909+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-4] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/documentation/swagger-ui/favicon-32x32.png
2025-07-07T19:26:25.910+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-3] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/documentation/swagger-ui/index.html
2025-07-07T19:26:26.052+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-3] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/documentation/swagger-ui/swagger-ui.css
2025-07-07T19:26:26.053+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-5] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/documentation/swagger-ui/index.css
2025-07-07T19:26:26.054+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-8] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/documentation/swagger-ui/swagger-initializer.js
2025-07-07T19:26:26.055+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-6] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/documentation/swagger-ui/swagger-ui-bundle.js
2025-07-07T19:26:26.055+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-7] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/documentation/swagger-ui/swagger-ui-standalone-preset.js
2025-07-07T19:26:26.056+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-1] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/documentation/swagger-ui/favicon-16x16.png
2025-07-07T19:26:26.472+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-6] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/documentation/v3/api-docs/swagger-config
2025-07-07T19:26:26.484+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-8] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/documentation/swagger-ui/favicon-32x32.png
2025-07-07T19:26:26.500+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-8] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/documentation/v3/api-docs
2025-07-07T19:29:12.003+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-2] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/account/process/export
2025-07-07T19:29:22.090+01:00  WARN 49855 --- [lucid-gateway-server] [reactor-http-nio-2] r.netty.http.client.HttpClientConnect    : [359a5913-1, L:/*************:59809 - R:/*************:8075] The connection observed an error

io.netty.handler.timeout.ReadTimeoutException: null

2025-07-07T19:29:24.000+01:00  INFO 49855 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T19:30:34.493+01:00  INFO 49855 --- [lucid-gateway-server] [reactor-http-nio-3] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/account/process/export
2025-07-07T19:52:09.632+01:00  INFO 49855 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T21:49:17.221+01:00  INFO 49855 --- [lucid-gateway-server] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application LUCID-GATEWAY-SERVER with eureka with status DOWN
2025-07-07T21:49:17.266+01:00  INFO 49855 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=*************, current=DOWN, previous=UP]
2025-07-07T21:49:17.276+01:00  INFO 49855 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T21:49:17.591+01:00  INFO 49855 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T21:49:17.702+01:00  INFO 49855 --- [lucid-gateway-server] [SpringApplicationShutdownHook] o.s.b.w.embedded.netty.GracefulShutdown  : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07T21:49:17.857+01:00  INFO 49855 --- [lucid-gateway-server] [netty-shutdown] o.s.b.w.embedded.netty.GracefulShutdown  : Graceful shutdown complete
2025-07-07T21:49:18.384+01:00  INFO 49855 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-07T21:49:21.392+01:00  INFO 49855 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-07T21:49:21.448+01:00  INFO 49855 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/} exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:91)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:149)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 37 more

2025-07-07T21:49:21.449+01:00  WARN 49855 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T21:49:21.467+01:00  INFO 49855 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:841)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:702)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.cancel(RestTemplateEurekaHttpClient.java:100)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:149)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:549)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:174)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:141)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900)
	... 38 more

2025-07-07T21:49:21.469+01:00  WARN 49855 --- [lucid-gateway-server] [SpringApplicationShutdownHook] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on DELETE request for "http://localhost:8070/eureka/apps/LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
2025-07-07T21:49:21.470+01:00 ERROR 49855 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:919) ~[eureka-client-2.0.4.jar:2.0.4]
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:900) ~[eureka-client-2.0.4.jar:2.0.4]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:415) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:239) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:202) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:195) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:389) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:136) ~[spring-cloud-context-4.2.0.jar:4.2.0]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:668) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:625) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1368) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:584) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1361) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1219) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1180) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:149) ~[spring-boot-3.4.1.jar:3.4.1]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1126) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:147) ~[spring-boot-3.4.1.jar:3.4.1]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:116) ~[spring-boot-3.4.1.jar:3.4.1]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-07-07T21:49:21.479+01:00  INFO 49855 --- [lucid-gateway-server] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-07T22:00:02.298+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Starting LucidGatewayServerApplication using Java 21.0.1 with PID 54973 (/Users/<USER>/Desktop/LUCID/lucid-gateway-server/target/classes started by ikechi in /Users/<USER>/Desktop/LUCID)
2025-07-07T22:00:02.299+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : No active profile set, falling back to 1 default profile: "default"
2025-07-07T22:00:02.322+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Fetching config from server at : http://localhost:8071/
2025-07-07T22:00:02.322+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.c.c.ConfigServerConfigDataLoader   : Located environment: name=lucid-gateway-server, profiles=[default], label=null, version=null, state=null
2025-07-07T22:00:02.324+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07T22:00:02.324+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07T22:00:02.892+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=17050c0b-2ecf-37a9-b237-253e4dfa3b87
2025-07-07T22:00:03.235+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] org.redisson.Version                     : Redisson 3.44.0
2025-07-07T22:00:03.256+01:00 ERROR 54973 --- [lucid-gateway-server] [restartedMain] i.n.r.d.DnsServerAddressStreamProviders  : Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-07-07T22:00:03.343+01:00  INFO 54973 --- [lucid-gateway-server] [redisson-netty-3-5] o.redisson.connection.ConnectionsHolder  : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-07T22:00:03.387+01:00  INFO 54973 --- [lucid-gateway-server] [redisson-netty-3-20] o.redisson.connection.ConnectionsHolder  : 24 connections initialized for localhost/127.0.0.1:6379
2025-07-07T22:00:03.629+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-07-07T22:00:03.629+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-07-07T22:00:03.629+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-07-07T22:00:03.629+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-07-07T22:00:03.629+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-07-07T22:00:03.629+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-07-07T22:00:03.629+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-07-07T22:00:03.629+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-07-07T22:00:03.629+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-07-07T22:00:03.629+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-07-07T22:00:03.629+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-07-07T22:00:03.630+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-07T22:00:03.630+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-07-07T22:00:03.630+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-07T22:00:03.738+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 19 endpoints beneath base path '/actuator'
2025-07-07T22:00:03.883+01:00  WARN 54973 --- [lucid-gateway-server] [restartedMain] i.m.c.i.b.cache.CaffeineCacheMetrics     : The cache 'loadbalancer' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
2025-07-07T22:00:03.933+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-07T22:00:03.940+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-07T22:00:03.986+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-07T22:00:03.996+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-07T22:00:03.997+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T22:00:04.000+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-07T22:00:04.000+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-07T22:00:04.001+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-07T22:00:04.001+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-07T22:00:04.001+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-07T22:00:04.001+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-07T22:00:04.001+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-07T22:00:04.173+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-07T22:00:04.175+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 30
2025-07-07T22:00:04.176+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-07T22:00:04.177+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751922004176 with initial instances count: 3
2025-07-07T22:00:04.180+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application LUCID-GATEWAY-SERVER with eureka with status UP
2025-07-07T22:00:04.181+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751922004181, current=UP, previous=STARTING]
2025-07-07T22:00:04.182+01:00  INFO 54973 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073: registering service...
2025-07-07T22:00:04.210+01:00  INFO 54973 --- [lucid-gateway-server] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_LUCID-GATEWAY-SERVER/*************:lucid-gateway-server:8073 - registration status: 204
2025-07-07T22:00:04.261+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8073 (http)
2025-07-07T22:00:04.262+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8073
2025-07-07T22:00:04.279+01:00  INFO 54973 --- [lucid-gateway-server] [restartedMain] c.d.l.g.s.LucidGatewayServerApplication  : Started LucidGatewayServerApplication in 2.699 seconds (process running for 8.516)
2025-07-07T22:03:17.920+01:00  INFO 54973 --- [lucid-gateway-server] [reactor-http-nio-2] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/onboarding/process/account/initiate
2025-07-07T22:05:03.960+01:00  INFO 54973 --- [lucid-gateway-server] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-07T22:05:39.604+01:00  INFO 54973 --- [lucid-gateway-server] [reactor-http-nio-3] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/onboarding/process/account/initiate
2025-07-07T22:07:01.160+01:00  INFO 54973 --- [lucid-gateway-server] [reactor-http-nio-4] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/onboarding/process/account/create
2025-07-07T22:07:31.674+01:00  INFO 54973 --- [lucid-gateway-server] [reactor-http-nio-4] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/onboarding/process/account/create
2025-07-07T22:08:07.056+01:00  INFO 54973 --- [lucid-gateway-server] [reactor-http-nio-4] c.d.l.g.s.filter.EncryptResponseFilter   : the url - /lucid-customer/api/v1/onboarding/process/account/create
2025-07-07T22:08:17.114+01:00  WARN 54973 --- [lucid-gateway-server] [reactor-http-nio-4] r.netty.http.client.HttpClientConnect    : [7c0e74a4-1, L:/*************:61237 - R:/*************:8075] The connection observed an error

io.netty.handler.timeout.ReadTimeoutException: null

