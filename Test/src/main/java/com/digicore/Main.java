package com.digicore;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Main {
    public static void main(String[] args) {
        System.out.println(getDiscountedPrice(74002314));
    }

    //SELECT c.name as country, SUM(g.goals) as no_of_goals
    //FROM test.countries c
    //INNER JOIN test.players p ON p.country_id = c.id
    //INNER JOIN test.goals g ON g.player_id = p.id
    //GROUP BY c.name, c.id
    //ORDER BY no_of_goals DESC, c.id ASC;

    public int balancedArray(List<Integer> arr) {
        for (int i = 1; i < arr.size(); i++) {
            int backward = 0;
            int forward = 0;

            for (int j = i-1; j >= 0; j--) {
                backward = backward + arr.get(j);
            }

            for (int j = i+1; j < arr.size(); j++) {
                forward = forward + arr.get(j);
            }

            if (backward == forward) {
                return i;
            }
        }
        return 0;
    }

    public static int getDiscountedPrice(int barcode) {
        try {
            String url = "https://jsonmock.hackerrank.com/api/inventory?barcode=" + barcode;
            URL apiUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) apiUrl.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            String responseString = "";
            if(responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String line;
                StringBuilder response = new StringBuilder();
                line = reader.readLine();
                while (line != null) {
                    response.append(line);
                    line = reader.readLine();
                }
                reader.close();
                responseString = response.toString();
                String regexPattern = "\"data\":\\[.*\\]";

                Pattern pattern = Pattern.compile(regexPattern);
                Matcher matcher = pattern.matcher(responseString);
                if(matcher.find()) {
                    var data = matcher.group();
                    var listOfData = data.substring(7);
                    int size = listOfData.length();
                    if(listOfData.length() > 2) {
                        listOfData = listOfData.substring(1, size -1);
                        regexPattern = "\"price\":\\d+,";
                        pattern = Pattern.compile(regexPattern);
                        matcher = pattern.matcher(listOfData);
                        if(matcher.find()) {
                            double price = Double.parseDouble(matcher.group().replaceAll("\"price\":","").replaceAll(",",""));
                            regexPattern = "\"discount\":\\d+,";
                            pattern = Pattern.compile(regexPattern);
                            matcher = pattern.matcher(listOfData);
                            if(matcher.find()) {
                                double discount = Double.parseDouble(matcher.group().replaceAll("\"discount\":","").replaceAll(",",""));
                                double result = (price - ((price*discount)/100));
                                result = Math.round(result);
                                return (int) result;
                            }
                        }
                    }
                }
            }
            return -1;
        } catch (Exception e) {
            return -1;
        }
    }


}