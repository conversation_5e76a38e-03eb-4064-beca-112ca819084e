# Lucid-BackOffice-Service

## Contributing
In order to have a standard uniform code base, the following are required of you before you can contribute to this project.

## Project Structure
- Please create a folder named lucid-projects if it doesn’t already exist by executing the following command:
```
mkdir lucid-projects
```
- Change into the lucid-projects directory:

```
cd lucid-projects
```
- Clone the code from the repository directly into the lucid-projects folder:

```
git clone <repository-url>
```

## File Rules (Class, Interface, Enum, etc)
- At the beginning of each file, the copyright notice must be included to ensure proper attribution and compliance. The typical format for the copyright notice is as follows:

```
/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */
```
- Following the copyright notice, leave one blank line, and then on the next line, include the package definition. Here’s how the structure should look:

```
/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */
 
package com.digicore.lucid;
```
- Following the package definition, leave one blank line, and then on the next line, include the import statements. Here’s how the structure should look:

```
/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */
 
package com.digicore.lucid;

import lombok.*;
```
- Following the import statement, leave one blank line, and then on the next line, include the author signature. Here’s how the structure should look:

```
/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */
 
package com.digicore.lucid;

import lombok.*;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-06(Thur)-2025
 */
 
```
- Following the author signature, leave one blank line, and then on the next line, include the @ annotations followed by the class definition. Here’s how the structure should look:

```
/*
 * Copyright (c) 2025 Digicore Limited. All Rights Reserved.
 * Unauthorized use or distribution is strictly prohibited.
 * For details, see the LICENSE file.
 */
 
package com.digicore.lucid;

import lombok.*;

/*
 * <AUTHOR> Ogunwuyi
 * @createdOn Feb-06(Thur)-2025
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Example {
 private String name;
}
 
```

## Versioning Rules
The versioning rules adapted is as follows :

Starting version - 1.0.0-SNAPSHOT

Each numbers in a version is used to represent the current state of the project.

- 1 : This represents a stable version of the project
- 0 : This represents a feature addition to the project
- 0 : This represents a bug fix on the project
- SNAPSHOT : This represents the project is in development mode
- ALPHA : This represents the project is in production mode

The versioning rules is based on the sequential increment of each number, based on the state of the project development.


**Laptop Requirements Recommended**

- OS : MacBook Pro ( Ventura 13.2 )
- Chip : Apple M2
- Memory : 8 GB


**IDE** 

We make use of Intellij IDEA ( Community Edition/Ultimate Edition )


**Code Formatting Style**

We adhere to the Google Java Style for formatting. The plugin for this is included in the pom.xml file. Once you have made changes to the files, simply run the following command to apply the formatter:
```
mvn spotless:apply
```
**Additional plugins to be installed in IDEA**

The below plugins can be found in Intellij Plugin Marketplace

- _**SonarLint by SonarSource**_ : SonarLint is a free IDE extension to find and fix coding issues in real-time, flagging issues as you code, just like a spell-checker. More than a linter, it also delivers rich contextual guidance to help developers understand why there is an issue, assess the risk and educate them on how to fix it. This helps improve their skills, enhance their productivity, and take ownership of their code, taking linting to a different level.

- _**GitToolBox by LukasZielinski**_ : Extends Git Integration 


<details><summary>Merge Request Rules</summary>

- Ensure to use the development template for your merge request description. see path to the template on the project root .gitlab/merge_request_templates/development.md

- For every issue/ticket, kindly check out a new branch from the main branch, this new branch name should follow this convention 
{task-title}{issue-no} e.g **sign-in-#1**

- Ensure to include the right milestone and label in your MR.
Labels are used to track current status of an issue/ticket so it's important you look at the project defined label [here](https://gitlab.com/teamdigicore/billent-backoffice-service/-/labels)

</details>

<details><summary>Test Rules</summary>

If you are new to writing test [here](https://www.baeldung.com/spring-boot-testing) is a good guide to help you get started.

-  Ensure to write both successful and failed integration test for the controllers, Your integration test should assert the followings
    - HTTP status code
    - Expected Data
-  Ensure to write both successful and failed unit test for the services.
</details>


## Authors and acknowledgment
- Oluwatobi Ogunwuyi ( Team Lead )
- Stephen Oloto ( Project Manager )
- Usman Abass ( Devops Engineer )

